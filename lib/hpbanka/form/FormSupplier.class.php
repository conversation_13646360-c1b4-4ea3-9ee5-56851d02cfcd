<?php
namespace form;

class FormSupplier {
    
    private $foodBankId;
    
    public function __construct($foodBankId) {
    
        $this->foodBankId = intval($foodBankId);
    }
    
    public static function createForFoodBank($foodBankId) {
    
        $formSupplier= new self($foodBankId);
        return $formSupplier;
    }
    
    public function removeFormSupplierByFormId($formId) {
        \dibi::delete('FormSupplier')->where('[formId] = %i AND [foodBankId] = %i', $formId, $this->foodBankId)->execute();
    }
}