<?php

namespace form;

class GroupOfSupportedPersonManager {

    public static function create() {
        $groupOfSupportedPersonManager = new self();
        return $groupOfSupportedPersonManager;
    }

    public function getGroupOfSupportedPersonList($addSummaryItem = false) {
        $query = array();
        $query[] = 'SELECT [groupId], name';
        $query[] = 'FROM [GroupOfSupportedPersons]';
        $query[] = 'ORDER BY name DESC';
        $groupOfSupportedPersonList = \dibi::query($query);

        $groupOfSupportedPersonListData = array();
        if ($addSummaryItem) {
            $groupOfSupportedPersonListData[] = self::getSummaryItemGroupOfSupportedPerson();
        }

        foreach ($groupOfSupportedPersonList as $groupOfSupportedPerson) {
            $groupOfSupportedPersonListData[] = array(
                'groupId' => $groupOfSupportedPerson->groupId,
                'name' => $groupOfSupportedPerson->name
            );
        }

        return $groupOfSupportedPersonListData;
    }

    public function saveGroupOfSupportedPerson($groupOfSupportedPerson) {
        $groupId = intval($groupOfSupportedPerson['groupId']);
        unset($groupOfSupportedPerson['groupId']);
        $values = (array) $groupOfSupportedPerson;

        if ($groupId > 0) {
            \dibi::update('GroupOfSupportedPersons', $values)->where("[groupId] = %i", $groupId)->execute();
        } else {
            \dibi::insert('GroupOfSupportedPersons', $values)->execute();
        }
    }

    public function deleteGroupOfSupportedPerson($groupId) {
        \dibi::delete('GroupOfSupportedPersons')->where('[groupId] = %i', $groupId)->execute();
    }

    public function getUsedGroupIdListByFoodBankId($foodBankId) {
        $list = \dibi::select('DISTINCT[groupId]')->from('[SupportedPersons]')->where('[foodBankId] = %i', $foodBankId)->orderBy('[groupId] ASC')->fetchAll();
        $usedGroupId = array();

        foreach ($list as $row) {
            $groupId = $row->groupId;
            $usedGroupId[$groupId] = $groupId;
        }
        return $usedGroupId;
    }

    public function getSummaryItemGroupOfSupportedPerson() {
        return array('groupId' => 0, 'name' => 'Všechny skupiny');
    }
}