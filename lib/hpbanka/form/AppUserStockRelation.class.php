<?php
namespace form;

class AppUserStockRelation {
    
    private $foodBankId;
    private $stockManager;
    
    public function __construct($foodBankId) {
        $this->foodBankId = intval($foodBankId);
        $this->stockManager = StockManager::createForFoodBank($this->foodBankId);
    }
    
    public static function createForFoodBank($foodBankId) {
        $appUserStockRelation = new self($foodBankId);
        return $appUserStockRelation;
    }
    
    public function getSelectedStockListByAppUser($appUserId, $canReadAllStocks, $addSummaryItem = true) {
        
        $selectedStockList = array();
       
        if ($canReadAllStocks) {
            if ($addSummaryItem) {
                $selectedStockList[] = $this->stockManager->getSummaryItemStock();
            }
            //SELECT * FROM "Stock" WHERE "foodBankId"=1 ORDER BY "name" ASC
            $query[] = 'SELECT [stockId], [name]';
            $query[] = 'FROM [Stock]';
            array_push($query, 'WHERE [foodBankId] = %i', $this->foodBankId);
            $query[] = 'ORDER BY [name] ASC';
            
        } else {
            //SELECT * FROM "AppUserStockRelation" as asr JOIN "Stock" as s on s."stockId"= asr."stockId" WHERE asr."appUserId"=78 AND asr."foodBankId"=1 ORDER BY asr."itemOrder" ASC
            $query = array();
            $query[] = 'SELECT asr.[stockId], s.[name]';
            $query[] = 'FROM [AppUserStockRelation] AS asr';
            $query[] = 'JOIN [Stock] AS s ON s.[stockId]=asr.[stockId]';
            array_push($query, 'WHERE asr.[appUserId] = %i AND asr.[foodBankId] = %i', $appUserId, $this->foodBankId);
            $query[] = 'ORDER BY asr.[itemOrder] ASC';
        }
        $stocks = \dibi::query($query);
        
        foreach ($stocks as $stock) {
            $selectedStockList[] = array(
                    'stockId' => $stock->stockId
                    ,'name' => $stock->name
                    ,'appUserId' => intval($appUserId)
            );
        };
        
        return $selectedStockList;
    }
    
    public function getAvailableStockListByAppUser($appUserId) {
        
        //SELECT * FROM "Stock" WHERE "stockId" NOT IN(SELECT [stockId] FROM [AppUserStockRelation] WHERE "appUserId"=78) AND "foodBankId"=1
        $query = array();
        $query[] = 'SELECT [stockId], [name]';
        $query[] = 'FROM [Stock]';
        $query[] = 'WHERE [stockId] NOT IN (SELECT [stockId] FROM [AppUserStockRelation] WHERE [appUserId] = ' . intval($appUserId) . ')';
        array_push($query, 'AND [foodBankId] = %i', $this->foodBankId);
        $stocks = \dibi::query($query);
        //echo \dibi::$sql;
        $availableStockList = array();
        foreach ($stocks as $stock) {
            $availableStockList[] = array(
                    'stockId' => $stock->stockId
                    ,'name' => $stock->name
                    ,'appUserId' => intval($appUserId)
            );
        };
        return $availableStockList;
    }
    
    public function retriveStockByAppUser($appUserId) {
        
        $addSummaryItem = false;
        $canReadAllStocks = false;
        $selectedStockList = self::getSelectedStockListByAppUser($appUserId, $canReadAllStocks, $addSummaryItem);
        $availableStockList = self::getAvailableStockListByAppUser($appUserId);
        return array('selectedStockListData' => $selectedStockList, 'availableStockListData' => $availableStockList);
    }
    
    public function saveStockByAppUser($stockId, $appUserId) {
        $result = \dibi::select('max([itemOrder])')->from('AppUserStockRelation')->where("[appUserId] = %i AND [foodBankId] = %i", $appUserId, $this->foodBankId)->execute();
        $itemOrder = intval($result->fetchSingle());
        $itemOrder++;
        $values = array('appUserId' => $appUserId, 'stockId' => $stockId, 'itemOrder' => $itemOrder, 'foodBankId' => $this->foodBankId);
        \dibi::insert('AppUserStockRelation', $values)->execute();
        self::saveCurrentStockId($appUserId);
    }
    
    public function removeStockByAppUser($stockId, $appUserId) {
        \dibi::delete('AppUserStockRelation')->where('[stockId]=' . $stockId . ' AND [appUserId]=' . $appUserId . ' AND [foodBankId]=' . $this->foodBankId)->execute();
        self::saveCurrentStockId($appUserId);
    }
      
    public function saveStockByAppUserOrder($orderedIdList, $appUserId){
        $order = 1;
        foreach ($orderedIdList as $id) {
            $values = array( 'itemOrder' => $order++ );
            \dibi::update('AppUserStockRelation', $values)->where('[stockId]=' . intval($id) . ' AND [appUserId]=' . $appUserId . ' AND [foodBankId]=' . $this->foodBankId)->execute();
        }
        self::saveCurrentStockId($appUserId);
    }
      
    public function saveCurrentStockId($appUserId){
        $currentStockId = self::getCurrentStockIdOrZeroForAppUserByAppUserId($appUserId);
        \core\AppUserManager::saveCurrentStockdIdByAppUser($appUserId, $currentStockId);
    }
      
    public function getCurrentStockIdOrZeroForAppUserByAppUserId($appUserId){
        $query[] = 'SELECT [stockId]';
        $query[] = 'FROM [AppUserStockRelation]';
        array_push($query, 'WHERE [appUserId] = %i AND [foodBankId] = %i', $appUserId, $this->foodBankId);
        $query[] = 'ORDER BY [itemOrder] ASC LIMIT 1';
        $result = \dibi::query($query)->fetchSingle();
        return !empty($result) ? $result : 0;
    }
}