<?php

namespace form;

class FilledForm {
    private $foodBankId;
    private $customerName;
    private $supplierName;
    private $dateFrom;
    private $dateTo;
    private $filledFormCommodity;
    private $stockId;
    private $stockReleaseNote;
    private $stockStockReleaseNoteId;
    private $actionName;

    protected function __construct($foodBankId) {
        $this->foodBankId = intval($foodBankId);
        $this->filledFormCommodity = \form\FilledFormCommodity::createForFoodBank($this->foodBankId);
        $this->stockReleaseNote = \form\StockReleaseNote::createForFoodBank($this->foodBankId);
    }

    public static function createForFoodBank($foodBankId) {
        $filledForm = new self($foodBankId);
        return $filledForm;
    }

    public function deleteFilledForm($filledFormId) {
        $this->filledFormCommodity->removeFilledFormCommodityByFilledFormId($filledFormId);
        \dibi::delete('FilledForm')->where('[filledFormId] = %i AND [foodBankId] = %i', $filledFormId, $this->foodBankId)->execute();
    }

    public function saveFilledForm($filledForm) {
        $filledFormId = intval($filledForm['filledFormId']);
        unset($filledForm['filledFormId']);
        $filledForm['foodBankId'] = $this->foodBankId;
        $filledForm['giftValue'] = round($filledForm['giftValue'] * 1000);

        if ($filledFormId > 0) {
            \dibi::update('FilledForm', $filledForm)->where("[filledFormId] = %i", $filledFormId)->execute();
            $ids = $this->stockReleaseNote->getStockReleaseNoteIdsByFilledFormId($filledFormId);
            if (!empty($ids->stockReleaseNoteId)) { // pokud se edituje primy odber potom uloz stejne datum do vydejky
                $stockReleaseNote['stockReleaseNoteId'] = $ids->stockReleaseNoteId;
                $stockReleaseNote['issuedAt'] = $filledForm['consumptionDate'];
                $this->stockReleaseNote->saveStockReleaseNote($stockReleaseNote);
            }
        } else {
            $filledForm['stockFilledFormId'] = $this->getNewStockFilledFormIdByStockId($filledForm['stockId']);
            $filledForm['foodBankFilledFormId'] = $this->getNewFoodBankFilledFormId();
            $filledFormId = \dibi::query("INSERT INTO [FilledForm]", $filledForm, " RETURNING [filledFormId] AS id")->fetchSingle();
        }
        $filledForm['filledFormId'] = $filledFormId;
        return (object) $filledForm;
    }

    public function updateFilledFormNote($note, $id) {
        $filledFormId = intval($id);

        if ($filledFormId > 0) {
            \dibi::update('FilledForm', ['note' => $note])->where("[filledFormId] = %i", $filledFormId)->execute();
        }
    }

    public function updateTotalAmount($totalAmount, $id) {
        $filledFormId = intval($id);

        if ($filledFormId > 0) {
            \dibi::update('FilledForm', ['totalAmount' => $totalAmount])->where("[filledFormId] = %i", $filledFormId)->execute();
        }
    }

    public function getNewStockFilledFormIdByStockId($stockId) {
        $result = \dibi::select('MAX([stockFilledFormId])')
            ->from('[FilledForm]')
            ->where('[foodBankId] = %i AND [stockId] = %i', $this->foodBankId, $stockId)
            ->execute();

        $maxId = $result->fetchSingle();

        return ++$maxId;
    }

    public function getNewFoodBankFilledFormId() {
        $result = \dibi::select('MAX([foodBankFilledFormId])')
            ->from('[FilledForm]')
            ->where('[foodBankId] = %i', $this->foodBankId)
            ->execute();

        $maxId = $result->fetchSingle();

        return ++$maxId;
    }

    public function getSentFormListByCustomerId($customerId) {
        global $_foodBank;

        $query = array();
        $query[] = 'SELECT srn.[stockReleaseNoteId],srn.[stockStockReleaseNoteId],f.[actionName],srn.[issuedAt],srn.[formId],srn.[filledFormId],sup.[name] AS [supplierName],srn.[donationAgreementId]';
        $query[] = 'FROM [StockReleaseNote] AS srn';
        $query[] = 'JOIN [Form] AS f ON srn.[formId]=f.[formId]';
        $query[] = 'LEFT JOIN [FilledForm] AS ff ON ff.[filledFormId]=srn.[filledFormId]';
        $query = $this->addSupplierNameFilterOrLeftJoin($query);
        array_push($query, 'WHERE srn.[customerId] = %i AND srn.[foodBankId] = %i', $customerId, $this->foodBankId);
        $query = $this->addDateFromFilter($query, 'issuedAt', 'srn');
        $query = $this->addDateToFilter($query, 'issuedAt', 'srn');
        $query = $this->addStockStockReleaseNoteIdFilter($query);
        $query = $this->addActionNameFilter($query);
        $query[] = 'ORDER BY srn.[stockReleaseNoteId] DESC';
        $sentFormList = \dibi::query($query);

        $sentFormListData = array();

        foreach ($sentFormList as $sentForm) {
            $stockReleaseNoteId = $sentForm->stockReleaseNoteId;
            if (empty($sentForm->filledFormId)) {
                $sentForm->supplierName = $_foodBank->pbName;
            }

            $sentFormListData[] = array(
                'stockReleaseNoteId' => $stockReleaseNoteId,
                'stockStockReleaseNoteId' => $sentForm->stockStockReleaseNoteId,
                'donationAgreementId' => $sentForm->donationAgreementId,
                'formId' => $sentForm->formId,
                'actionName' => $sentForm->actionName,
                'issuedAt' => date('j.n.Y', strtotime($sentForm->issuedAt)),
                'supplierName' => $sentForm->supplierName,
                'calculatedValue' => $this->filledFormCommodity->getCalculatedValueByStockReleaseNoteId($stockReleaseNoteId) / 1000
            );
        }

        return $sentFormListData;
    }

    public function getReceivedFormList() {
        $query = array();
        $query[] = 'SELECT ff.[filledFormId], ff.[foodBankFilledFormId], ff.[stockFilledFormId], sup.name, f.[actionName], ff.[consumptionDate], ff.[giftValue], ff.[note], ff.[totalAmount], srn.[stockReleaseNoteId], srn.[stockStockReleaseNoteId], srn.[donationAgreementId], srn.[customerId]';
        $query[] = 'FROM [FilledForm] AS ff';
        $query[] = 'JOIN [Form] AS f ON ff.[formId]=f.[formId]';
        $query[] = 'LEFT JOIN [StockReleaseNote] AS srn ON srn.[filledFormId]=ff.[filledFormId]';
        $query = $this->addSupplierNameFilterOrJoin($query);
        array_push($query, 'WHERE ff.[foodBankId] = %i', $this->foodBankId);
        $query = $this->addDateFromFilter($query, 'consumptionDate', 'ff');
        $query = $this->addDateToFilter($query, 'consumptionDate', 'ff');
        $query = $this->addStock($query, 'ff');
        $query = $this->addActionNameFilter($query);
        $query = $this->addStockFilledFormIdFilter($query);
        $query[] = 'ORDER BY ff.[foodBankFilledFormId] DESC';
        $receivedFormList = \dibi::query($query);
        $customerManager = new \party\CustomerManager();
        $listOfcustomerIdArchivedUsers = $customerManager->getListOfCustomerIdArchivedUsers($this->foodBankId);

        $receivedFormListData = array();

        foreach ($receivedFormList as $receivedForm) {
            $filledFormId = $receivedForm->filledFormId;
            $note = trim($receivedForm->note);
            $receivedFormListData[] = array(
                'filledFormId' => $filledFormId,
                'foodBankFilledFormId' => $receivedForm->foodBankFilledFormId,
                'stockFilledFormId' => $receivedForm->stockFilledFormId,
                'name' => $receivedForm->name,
                'actionName' => $receivedForm->actionName,
                'consumptionDate' => substr($receivedForm->consumptionDate, 0, 10),
                'giftValue' => $receivedForm->giftValue / 1000,
                'totalAmount' => $receivedForm->totalAmount / 1000,
                'isDirectConsumption' => empty($receivedForm->stockReleaseNoteId) ? false : true,
                'stockReleaseNoteId' => $receivedForm->stockReleaseNoteId,
                'stockStockReleaseNoteId' => $receivedForm->stockStockReleaseNoteId ? $receivedForm->stockStockReleaseNoteId : '--',
                'donationAgreementId' => $receivedForm->donationAgreementId,
                'note' => $note,
                'isNote' => !empty($note) ? true : false,
                'isCustomerArchived' => array_key_exists($receivedForm->customerId, $listOfcustomerIdArchivedUsers)
            );
        }

        return $receivedFormListData;
    }

    public function setCustomerNameFilter($customerName) {
        $this->customerName = mb_strtolower($customerName, "UTF8");
    }

    public function setSupplierNameFilter($supplierName) {
        $this->supplierName = mb_strtolower($supplierName, "UTF8");
    }

    public function setDateFromFilter($dateFrom) {
        $this->dateFrom = $dateFrom;
    }

    public function setDateToFilter($dateTo) {
        $this->dateTo = $dateTo;
    }

    public function setStockFilledFormIdToFilter($stockFilledFormId) {
        $this->stockFilledFormId = intval($stockFilledFormId);
    }

    public function setActionNameToFilter($actionName) {
        $this->actionName = mb_strtolower($actionName, "UTF8");
    }

    public function setStockByAppUser($appUserId) {
        $this->stockId = \core\AppUserManager::getCurrentStockIdByAppUser($appUserId);
    }

    public function setStockStockReleaseNoteId($stockStockReleaseNoteId) {
        $this->stockStockReleaseNoteId = $stockStockReleaseNoteId;
    }

    public function setActionName($actionName) {
        $this->actionName = mb_strtolower($actionName, "UTF8");
    }

    private function addSupplierNameFilterOrLeftJoin($query) {
        $join = 'JOIN [Supplier] AS sup ON sup.[supplierId]=ff.[supplierId]';
        if ($this->supplierName) {
            array_push($query, $join . ' AND lower(sup.[name]) LIKE %~like~', $this->supplierName);
        } else {
            $query[] = 'LEFT ' . $join;
        }
        return $query;
    }

    private function addSupplierNameFilterOrJoin($query) {
        $join = 'JOIN [Supplier] AS sup ON sup.[supplierId]=ff.[supplierId]';
        if ($this->supplierName) {
            array_push($query, $join . ' AND lower(sup.[name]) LIKE %~like~', $this->supplierName);
        } else {
            $query[] = $join;
        }
        return $query;
    }

    private function addDateFromFilter($query, $dateColumnName, $tableAlias) {
        if ($this->dateFrom) {
            array_push($query, 'AND ' . $tableAlias . '.[' . $dateColumnName . '] >= %d', $this->dateFrom);
        }
        return $query;
    }

    private function addDateToFilter($query, $dateColumnName, $tableAlias) {
        if ($this->dateTo) {
            array_push($query, 'AND ' . $tableAlias . '.[' . $dateColumnName . '] <= %d', $this->dateTo);
        }
        return $query;
    }

    private function addStock($query, $tableAlias) {
        if ($this->stockId > 0) {
            array_push($query, 'AND ' . $tableAlias . '.[stockId] = %i', $this->stockId);
        }
        return $query;
    }

    private function addActionNameFilter($query) {
        if ($this->actionName) {
            array_push($query, ' AND lower(f.[actionName]) LIKE %~like~', $this->actionName);
        }
        return $query;
    }

    private function addStockFilledFormIdFilter($query) {
        if ($this->stockFilledFormId > 0) {
            array_push($query, ' AND ff.[stockFilledFormId] = %i', $this->stockFilledFormId);
        }
        return $query;
    }

    private function addStockStockReleaseNoteIdFilter($query) {
        if ($this->stockStockReleaseNoteId > 0) {
            array_push($query, 'AND srn.[stockStockReleaseNoteId] = %i', $this->stockStockReleaseNoteId);
        }
        return $query;
    }

    public function getSentFormByFilledFormId($filledFormId) {
        $stockManager = \form\StockManager::createForFoodBank($this->foodBankId);

        $sentForm = \dibi::select('ff.[consumptionDate],  f.[actionName], s.[name] AS [supplierName], ff.[giftValue], ff.[filledFormId], ff.[foodBankFilledFormId], ff.[formId], ff.[supplierId], ff.[stockId], ff.[note]')
            ->from('[FilledForm] AS ff')
            ->join('[Form] AS f')->on('f.[formId]=ff.[formId]')
            ->join('[Supplier] AS s')->on('s.[supplierId]=ff.[supplierId]')
            ->where('ff.[filledFormId] = %i AND ff.[foodBankId] = %i', $filledFormId, $this->foodBankId)
            ->execute();

        $result = $sentForm->fetch();

        $note = trim($result->note);

        $sentFormData = array(
            'consumptionDate' => substr($result->consumptionDate, 0, 10),
            'actionName' => $result->actionName,
            'supplierName' => $result->supplierName,
            'giftValue' => $result->giftValue / 1000,
            'filledFormId' => $result->filledFormId,
            'foodBankFilledFormId' => $result->foodBankFilledFormId,
            'formId' => $result->formId,
            'supplierId' => $result->supplierId,
            'stockId' => $result->stockId,
            'nameStock' => $stockManager->getNameStockByStockId($result->stockId),
            'note' => $note,
            'isNote' => !empty($note) ? true : false
        );
        return $sentFormData;
    }

    public function getReceiptToStockListByFilledFormIds($filledFormIds) {
        $receiptToStockList = \dibi::select('ff.[consumptionDate], f.[actionName], ff.[supplierName], ff.[supplierAddress],' .
            ' ff.[supplierIC], ff.[foodBankName], ff.[foodBankAddress], ff.[foodBankIC], ff.[giftValue], ff.[filledFormId],' .
            ' ff.[stockFilledFormId], st.[name] AS [stockName], ff.[appUserId], ff.[note]')
            ->from('[FilledForm] AS ff')
            ->join('[Form] AS f')->on('f.[formId]=ff.[formId]')
            ->join('[Stock] AS st')->on('st.[stockId]=ff.[stockId]')
            ->where('ff.[filledFormId] IN (%i) AND ff.[foodBankId] = %i', $filledFormIds, $this->foodBankId)
            ->execute();

        $receiptToStockListData = array();

        foreach ($receiptToStockList as $receiptToStock) {
            $filledFormCommodity = \form\FilledFormCommodity::createForFoodBank($this->foodBankId);
            $commodityListData = $filledFormCommodity->getFilledFormCommodityByFilledFormId($receiptToStock->filledFormId);
            $receiptToStockListData[] = array(
                'consumptionDate' => substr($receiptToStock->consumptionDate, 0, 10),
                'actionName' => $receiptToStock->actionName,
                'giftValue' => $receiptToStock->giftValue / 1000,
                'stockFilledFormId' => $receiptToStock->stockFilledFormId,
                'commodityListData' => $commodityListData,
                'supplierName' => $receiptToStock->supplierName,
                'supplierAddress' => $receiptToStock->supplierAddress,
                'supplierIC' => $receiptToStock->supplierIC,
                'foodBankName' => $receiptToStock->foodBankName,
                'foodBankAddress' => $receiptToStock->foodBankAddress,
                'foodBankIC' => $receiptToStock->foodBankIC,
                'stockName' => $receiptToStock->stockName,
                'note' => $receiptToStock->note,
                'isNote' => !empty(trim($receiptToStock->note)) ? 1 : 0,
                'appUserId' => $receiptToStock->appUserId
            );
        }

        return $receiptToStockListData;
    }

    public function getUsedSupplierIdListByFormId($formId) {
        $result = \dibi::select('ff.[supplierId]')
            ->from('[FilledForm] AS ff')
            ->where("ff.[formId] = %i AND ff.[foodBankId] = %i", $formId, $this->foodBankId)
            ->execute();

        $supplierIdList = array();
        foreach ($result as $filledForm) {
            $supplierIdList[$filledForm->supplierId] = $filledForm->supplierId;
        };

        return $supplierIdList;
    }
}
