<?php

namespace form;

class DonationAgreementManager {

    private $foodBankId;
    private $stockReleaseNoteCommodity;
    private $foodBankDonationAgreementId;
    private $customerName;

    public static function createForFoodBank($foodBankId) {
        $donationAgreementManager = new self($foodBankId);
        return $donationAgreementManager;
    }

    private function __construct($foodBankId) {
        $this->foodBankId = intval($foodBankId);
        $this->stockReleaseNoteCommodity = \form\StockReleaseNoteCommodity::createForFoodBank($this->foodBankId);
    }

    public function getDonationAgreementIdsByRange($donationAgreementIdFrom, $donationAgreementIdTo) {
        if (!empty($donationAgreementIdFrom) and !empty($donationAgreementIdTo) and ($donationAgreementIdFrom > $donationAgreementIdTo)) {
            $tempDonationAgreementIdTo = $donationAgreementIdFrom;
            $donationAgreementIdFrom = $donationAgreementIdTo;
            $donationAgreementIdTo = $tempDonationAgreementIdTo;
        }

        $query = array();
        $query[] = 'SELECT [donationAgreementId]';
        $query[] = 'FROM [DonationAgreement]';
        array_push($query, 'WHERE [foodBankId] = %i', $this->foodBankId);
        $query = $this->addDonationAgreementIdFrom($query, $donationAgreementIdFrom);
        $query = $this->addDonationAgreementIdTo($query, $donationAgreementIdTo);
        $result = \dibi::query($query);

        $donationAgreementIdListData = array();
        foreach ($result as $donationAgreementId) {
            $donationAgreementIdListData[] = $donationAgreementId->donationAgreementId;
        }
        return $donationAgreementIdListData;
    }

    private function addDonationAgreementIdFrom($query, $donationAgreementIdFrom) {
        if (!empty($donationAgreementIdFrom)) {
            array_push($query, ' AND [foodBankDonationAgreementId] >= %i', $donationAgreementIdFrom);
        }
        return $query;
    }

    private function addDonationAgreementIdTo($query, $donationAgreementIdTo) {
        if (!empty($donationAgreementIdTo)) {
            array_push($query, ' AND [foodBankDonationAgreementId] <= %i', $donationAgreementIdTo);
        }
        return $query;
    }

    public function getDonationAgreementListByDonationAgreementIds($donationAgreementIds) {

        $donationAgreementList = \dibi::select('*')
            ->from('DonationAgreement')
            ->where('[foodBankId] = %i AND [donationAgreementId] IN (%i)', $this->foodBankId, $donationAgreementIds)
            ->orderBy('[dateFrom]', 'DESC')
            ->execute();

        $donationAgreementListData = array();
        foreach ($donationAgreementList as $donationAgreement) {
            $query = array();
            $query[] = 'SELECT s.[stockReleaseNoteId], s.[stockStockReleaseNoteId], s.[issuedAt], f.[actionName], s.[filledFormId], ff.[stockFilledFormId], s.[customerId]';
            $query[] = 'FROM [StockReleaseNote] AS s';
            $query[] = 'JOIN [Form] AS f ON f.[formId]=s.[formId]';
            $query[] = 'LEFT JOIN [FilledForm] AS ff ON ff.[filledFormId]=s.[filledFormId]';
            array_push($query, 'WHERE s.[donationAgreementId] = %i', $donationAgreement->donationAgreementId);
            array_push($query, 'ORDER BY s.[stockStockReleaseNoteId] ASC');
            $result = \dibi::query($query);

            $stockReleaseNotes = array();
            $totalAmount = 0;

            foreach ($result as $row) {
                $amount = $this->calculateCommodityAmountForStockReleaseNote($row->stockReleaseNoteId);
                $totalAmount += $amount;
                $stockReleaseNotes[] = array(
                    'stockStockReleaseNoteId' => $row->stockStockReleaseNoteId,
                    'issuedAt' => substr($row->issuedAt, 0, 10),
                    'actionName' => $row->actionName,
                    'stockFilledFormId' => $row->stockFilledFormId,
                    'amount' => $amount / 1000,
                    'calculatedValue' => $this->stockReleaseNoteCommodity->getCalculatedValueByStockReleaseNoteId($row->stockReleaseNoteId) / 1000
                );
            }

            $donationAgreementListData[] = array(
                'donationAgreementId' => $donationAgreement->donationAgreementId,
                'donationAgreementName' => $donationAgreement->donationAgreementName,
                'foodBankDonationAgreementId' => $donationAgreement->foodBankDonationAgreementId,
                'dateFrom' => substr($donationAgreement->dateFrom, 0, 10),
                'dateTo' => substr($donationAgreement->dateTo, 0, 10),
                'totalCalculatedValue' => $donationAgreement->calculatedValue / 1000,
                'totalAmount' => $totalAmount / 1000,
                'donorName' => $donationAgreement->donorName,
                'donorAddress' => $donationAgreement->donorAddress,
                'donorIc' => \core\HpNumberFormatter::toIC($donationAgreement->donorIc),
                'doneeName' => $donationAgreement->doneeName,
                'doneeAddress' => $donationAgreement->doneeAddress,
                'doneeIC' => \core\HpNumberFormatter::toIC($donationAgreement->doneeIC),
                'stockReleaseNotes' => $stockReleaseNotes,
                'customerId' => $donationAgreement->customerId
            );
        }

        return $donationAgreementListData;
    }

    public function getDonationAgreementList($customerId = null) {

        $query = array();
        $query[] = 'SELECT da.[donationAgreementId],da.[foodBankDonationAgreementId],da.[dateFrom],da.[dateTo],da.[calculatedValue],c.[name],da.[note],da.[processed]';
        $query[] = 'FROM [DonationAgreement] AS da';
        $query = $this->addCustomerNameFilterOrJoin($query);
        array_push($query, 'WHERE da.[foodBankId] = %i', $this->foodBankId);

        if ($customerId) {
            array_push($query, ' AND da.[customerId] = %i', $customerId);
        }

        $query = $this->addFoodBankDonationAgreementIdFilter($query);
        $query[] = 'ORDER BY da.[foodBankDonationAgreementId] DESC';
        $donationAgreementList = \dibi::query($query);
        $donationAgreementListData = array();

        foreach ($donationAgreementList as $donationAgreement) {
            $note = trim($donationAgreement->note);
            $donationAgreementListData[] = array(
                'donationAgreementId' => $donationAgreement->donationAgreementId,
                'foodBankDonationAgreementId' => $donationAgreement->foodBankDonationAgreementId,
                'dateFrom' => substr($donationAgreement->dateFrom, 0, 10),
                'dateTo' => substr($donationAgreement->dateTo, 0, 10),
                'calculatedValue' => $donationAgreement->calculatedValue / 1000,
                'customerName' => $donationAgreement->name,
                'note' => $note,
                'isNote' => !empty($note) ? true : false,
                'processed' => $donationAgreement->processed
            );
        }

        return $donationAgreementListData;
    }

    public function setFoodBankDonationAgreementIdFilter($foodBankDonationAgreementId) {
        $this->foodBankDonationAgreementId = $foodBankDonationAgreementId;
    }

    public function setCustomerNameFilter($customerName) {
        $this->customerName = mb_strtolower($customerName, "UTF8");
    }

    private function addFoodBankDonationAgreementIdFilter($query) {
        if ($this->foodBankDonationAgreementId > 0) {
            array_push($query, 'AND da.[foodBankDonationAgreementId] = %i', $this->foodBankDonationAgreementId);
        }
        return $query;
    }

    private function addCustomerNameFilterOrJoin($query) {

        $join = 'JOIN [Customer] AS c ON c.[customerId]=da.[customerId]';
        if ($this->customerName) {
            array_push($query, $join . ' AND lower(c.[name])  LIKE %~like~', $this->customerName);
        } else {
            $query[] = $join;
        }
        return $query;
    }

    public function getNewFoodBankDonationAgreementId() {
        $result = \dibi::select('MAX([foodBankDonationAgreementId])')->from('[DonationAgreement]')->where('[foodBankId] = %i', $this->foodBankId)->execute();
        $maxId = $result->fetchSingle();
        return ++$maxId;
    }

    private function calculateCommodityAmountForStockReleaseNote($stockReleaseNoteId) {
        return \dibi::select('SUM([amount])')
            ->from('[StockReleaseNoteCommodity]')
            ->where('[stockReleaseNoteId]= %i AND [foodBankId] = %i', $stockReleaseNoteId, $this->foodBankId)
            ->execute()
            ->fetchSingle();
    }

    public function deleteDonationAgreementByDonationAgreementId($donationAgreementId) {
        \dibi::update('StockReleaseNote', array('donationAgreementId' => null))->where("[donationAgreementId] IN (%i)", $donationAgreementId)->execute();
        \dibi::delete('DonationAgreement')->where('[donationAgreementId] = %i AND [foodBankId] = %i', $donationAgreementId, $this->foodBankId)->execute();
    }

    public function saveDonationAgreement($donationAgreementData) {
        $stockReleaseNoteIdList = $donationAgreementData['stockReleaseNoteIdList'];
        if (empty($stockReleaseNoteIdList)) {
            return;
        }
        $stockReleaseNoteList = \dibi::select('*')->from('[StockReleaseNote]')->where('[stockReleaseNoteId] IN (%i)', $stockReleaseNoteIdList)->orderBy('[issuedAt]', 'DESC')->execute();

        $stockReleaseNoteListByCustomerId = array();
        foreach ($stockReleaseNoteList as $stockReleaseNote) {
            $stockReleaseNoteListByCustomerId[$stockReleaseNote->customerId][] = $stockReleaseNote;
        }

        //darce (alias dodavatel) je Potravinova banka
        $foodBankManager = \party\FoodBankManager::createForFoodBank($this->foodBankId);
        $foodBank = $foodBankManager->getFoodBankByFoodBankId();

        $customerManager = new \party\CustomerManager();

        foreach ($stockReleaseNoteListByCustomerId as $customerId => $stockReleaseNoteListByCustomer) {
            //obdarovany (alias odberatel)
            $customer = $customerManager->getCustomerByCustomerId($customerId);

            $donationAgreement = array(
                'foodBankId' => $this->foodBankId,
                'foodBankDonationAgreementId' => $this->getNewFoodBankDonationAgreementId(),
                'donationAgreementName' => $foodBank->donationAgreementName,
                'customerId' => $customerId,
                'donorName' => $foodBank->name,
                'donorAddress' => $foodBank->address,
                'donorIc' => $foodBank->ic,
                'donorDic' => $foodBank->dic,
                'doneeName' => $customer->name,
                'doneeAddress' => $customer->address,
                'doneeIC' => $customer->IC,
                'dateFrom' => $donationAgreementData['dateFrom'],
                'dateTo' => $donationAgreementData['dateTo'],
                'calculatedValue' => 0,
                'amount' => 0,
                'appUserId' => $donationAgreementData['appUserId'],
                'note' => $donationAgreementData['note']
            );

            $stockReleaseNoteIdList = array();
            foreach ($stockReleaseNoteListByCustomer as $stockReleaseNote) {
                $stockReleaseNoteId = $stockReleaseNote->stockReleaseNoteId;
                $stockReleaseNoteIdList[] = $stockReleaseNoteId;
                $donationAgreement['amount'] += $this->calculateCommodityAmountForStockReleaseNote($stockReleaseNoteId);
                $donationAgreement['calculatedValue'] += $this->stockReleaseNoteCommodity->getCalculatedValueByStockReleaseNoteId($stockReleaseNoteId);
            }

            $donationAgreementId = \dibi::query("INSERT INTO [DonationAgreement]", $donationAgreement, " RETURNING [donationAgreementId]")->fetchSingle();
            \dibi::update('StockReleaseNote', array('donationAgreementId' => $donationAgreementId))->where("[stockReleaseNoteId] IN (%i)", $stockReleaseNoteIdList)->execute();
        }
    }
}
