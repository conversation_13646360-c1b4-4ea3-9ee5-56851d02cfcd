<?php
namespace form;

class SupportedPersonManager {
    private $foodBankId;
    private $groupOfSupportedPersonManager;
    private $customerName;
    private $contactPerson;
    private $groupName;
    private $dateFrom;

    public function __construct($foodBankId) {
        $this->foodBankId = intval($foodBankId);
        $this->groupOfSupportedPersonManager = \form\GroupOfSupportedPersonManager::create();
    }

    public static function createForFoodBank($foodBankId) {
        $supportedPersonManager = new self($foodBankId);
        return $supportedPersonManager;
    }

    public function setCustomerNameFilter($customerName) {
        $this->customerName = mb_strtolower($customerName, "UTF8");
    }

    public function setContactPersonFilter($contactPerson) {
        $this->contactPerson = mb_strtolower($contactPerson, "UTF8");
    }

    public function setGroupNameFilter($groupName) {
        $this->groupName = mb_strtolower($groupName, "UTF8");
    }

    public function setDateFromFilter($dateFrom) {
        $this->dateFrom = $dateFrom;
    }

    private function addCustomerNameFilter($query, $tableAlias) {
        if ($this->customerName) {
            array_push($query, ' AND lower(' . $tableAlias . '.[name]) LIKE %~like~', $this->customerName);
        }

        return $query;
    }

    private function addContactPersonFilter($query, $tableAlias) {
        if ($this->contactPerson) {
            array_push($query, ' AND lower(' . $tableAlias . '.[contactPerson]) LIKE %~like~', $this->contactPerson);
        }

        return $query;
    }

    private function addGroupNameFilter($query, $tableAlias) {
        if ($this->groupName) {
            array_push($query, ' AND lower(' . $tableAlias . '.[groupName]) LIKE %~like~', $this->groupName);
        }

        return $query;
    }

    private function addDateFromFilter($query, $dateColumnName, $tableAlias) {
        if ($this->dateFrom) {
            array_push($query, 'AND ' . $tableAlias . '.[' . $dateColumnName . '] >= %d', $this->dateFrom);
        }

        return $query;
    }

    public function getCustomerListWithSumNumberOfSupportedPersons() {
        $query = array();
        $query[] = 'SELECT sp.[customerId], sp.[yearMonth], sp.[numberOfSupportedPersons], cus.[name], cus.[contactPerson], cus.[address], cus.[groupName]';
        $query[] = 'FROM [SupportedPersons] AS sp';
        $query[] = 'JOIN [Customer] AS cus ON cus.[customerId]=sp.[customerId]';
        array_push($query, 'WHERE sp.[foodBankId] = %i', $this->foodBankId);
        $query = $this->addCustomerNameFilter($query, 'cus');
        $query = $this->addContactPersonFilter($query, 'cus');
        $query = $this->addGroupNameFilter($query, 'cus');
        $query = $this->addDateFromFilter($query, 'yearMonth', 'sp');
        $query[] = 'ORDER BY [yearMonth], cus.[name] DESC';
        $list = \dibi::query($query);

        $customerListByYearMonthAndCustomer = array();

        foreach ($list as $row) {
            $customerId = $row->customerId;
            $yearMonth = substr($row->yearMonth, 0, 10);
            $sumNumberOfSupportedPerson = intval($customerListByYearMonthAndCustomer[$yearMonth][$customerId]['sumNumberOfSupportedPersons']) + $row->numberOfSupportedPersons;
            $customerListByYearMonthAndCustomer[$yearMonth][$customerId] = array(
                'customerId' => $customerId,
                'yearMonth' => $yearMonth,
                'sumNumberOfSupportedPersons' => $sumNumberOfSupportedPerson,
                'name' => $row->name,
                'contactPerson' => $row->contactPerson,
                'address' => $row->address,
                'groupName' => $row->groupName
            );
        }

        $customerListWithSumNumberOfSupportedPerson = array();

        foreach ($customerListByYearMonthAndCustomer as $key => $values) {
            foreach ($values as $value) {
                $customerListWithSumNumberOfSupportedPerson[] = $value;
            }
        }

        return $customerListWithSumNumberOfSupportedPerson;
    }

    public function getCountOfGroupedItemsByCustomerForTheStartDate($customerId, $startDate) {
        $query = array();
        $query[] = 'SELECT [yearMonth] FROM [SupportedPersons]';
        array_push($query, 'WHERE [foodBankId] = %i AND [customerId] = %i', $this->foodBankId, $customerId);
        array_push($query, ' AND [yearMonth] >= %d', $startDate);
        $query[] = 'GROUP BY [yearMonth]';
        $query[] = 'ORDER BY [yearMonth]';
        $result = \dibi::query($query);

        return $result->getRowCount();
    }

    public function getSupportedPersonListByPeriodAndCustomerId($year, $month, $customerId) {
        $yearMonthDate = \date\Date::create(1, $month, $year);
        $yearMonth = \date\DateFormatter::formatToSql($yearMonthDate);

        $supportedPersonListData = array();
        $groupOfSupportedPersonList = $this->groupOfSupportedPersonManager->getGroupOfSupportedPersonList();

        foreach ($groupOfSupportedPersonList as $groupOfSupportedPerson) {
            $groupId = $groupOfSupportedPerson['groupId'];
            $supportedPersonListData[$groupId] = array(
                'groupId' => $groupId,
                'name' => $groupOfSupportedPerson['name'],
                'numberOfSupportedPersons' => '',
                'customerId' => $customerId,
                'foodBankId' => $this->foodBankId,
                'yearMonth' => $yearMonth
            );
        }

        $query = array();
        $query[] = 'SELECT [groupId], [numberOfSupportedPersons], [yearMonth]';
        $query[] = 'FROM [SupportedPersons]';
        array_push($query, 'WHERE [foodBankId] = %i AND [customerId] = %i', $this->foodBankId, $customerId);
        array_push($query, ' AND [yearMonth] = %d', $yearMonth);
        $supportedPersons = \dibi::query($query);

        foreach ($supportedPersons as $person) {
            $groupId = $person->groupId;
            $supportedPersonListData[$groupId]['numberOfSupportedPersons'] = $person->numberOfSupportedPersons;
        }

        return $supportedPersonListData;
    }

    public function saveSupportedPersons($supportedPersons, $customerId, $year, $month) {
        $yearMonthDate = \date\Date::create(1, $month, $year);
        $yearMonth = \date\DateFormatter::formatToSql($yearMonthDate);
        self::removeSupportedPersons($customerId, $yearMonth);

        foreach ($supportedPersons as $person) {
            if (!empty($person->numberOfSupportedPersons)) {
                $person->numberOfSupportedPersons = intval($person->numberOfSupportedPersons);
                $values = (array)$person;
                unset($values['name']);
                \dibi::insert('SupportedPersons', $values)->execute();
            }
        }
    }

    private function removeSupportedPersons($customerId, $yearMonth) {
        \dibi::delete('SupportedPersons')->where('[customerId] = %i AND [foodBankId] = %i AND [yearMonth] = %d', $customerId, $this->foodBankId, $yearMonth)->execute();
    }
}
