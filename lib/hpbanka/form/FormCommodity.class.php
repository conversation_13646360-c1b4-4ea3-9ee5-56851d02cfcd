<?php
namespace form;

class FormCommodity {
    
    private $foodBankId;
    
    public function __construct($foodBankId) {
    
        $this->foodBankId = intval($foodBankId);
    }
    
    public static function createForFoodBank($foodBankId) {
    
        $formCommodity = new self($foodBankId);
        return $formCommodity;
    }
    
    public function removeFormCommodityByFormId($formId) {
        \dibi::delete('FormCommodity')->where('[formId] = %i AND [foodBankId] = %i', $formId, $this->foodBankId)->execute();
    }
}