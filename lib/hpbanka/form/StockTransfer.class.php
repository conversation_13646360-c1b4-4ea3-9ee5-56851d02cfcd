<?php

namespace form;

class StockTransfer {
    private $foodBankId;
    private $dateFrom;
    private $dateTo;
    private $stockId;
    private $stockTransferId;
    private $targetStockId;

    protected function __construct($foodBankId) {
        $this->foodBankId = intval($foodBankId);
    }

    public static function createForFoodBank($foodBankId) {
        $stockTransfer = new self($foodBankId);
        return $stockTransfer;
    }

    public function setDateFromFilter($dateFrom) {
        $this->dateFrom = $dateFrom;
    }

    public function setDateToFilter($dateTo) {
        $this->dateTo = $dateTo;
    }

    public function setStockByAppUser($appUserId) {
        $this->stockId = \core\AppUserManager::getCurrentStockIdByAppUser($appUserId);
    }

    public function setStockTransferIdToFilter($stockTransferId) {
        $this->stockTransferId = intval($stockTransferId);
    }

    public function setStockIdToFilter($stockId) {
        $this->stockId = intval($stockId);
    }

    public function getStockTransfersList() {
        $query = array();
        $query[] = 'SELECT t.[transferId], t.[sourceStockId], t.[targetStockId], s.[name] as [targetStockName], ss.[name] as [sourceStockName], t.[appUserId], t.[stockTransferId], t.[issuedAt], t.[note] ';
        $query[] = 'FROM [Transfer] AS t ';
        $query[] = 'LEFT JOIN [Stock] AS s ON (t.[targetStockId]=s.[stockId])';
        $query[] = 'LEFT JOIN [Stock] AS ss ON (t.[sourceStockId]=ss.[stockId])';
        array_push($query, 'WHERE t.[foodBankId] = %i', $this->foodBankId);
        $query = $this->addDateFromFilter($query);
        $query = $this->addDateToFilter($query);
        $query = $this->addStockFilter($query);
        $query = $this->addStockTransferIdFilter($query);
        $query = $this->addTargetStockIdFilter($query);
        $query[] = 'ORDER BY t.[stockTransferId] DESC';

        $stockTransfersList = \dibi::query($query);
        $stockTransfersListData = array();

        foreach ($stockTransfersList as $transfer) {

            $transferId = $transfer->transferId;

            $totalAmount = \dibi::select('SUM(tc.amount)')
                ->from('[TransferCommodity] AS tc')
                ->join('[Commodity] AS c')->on('tc.[commodityId]=c.[commodityId]')
                ->where('tc.[transferId] = %i AND tc.[foodBankId] = %i', $transferId, $this->foodBankId)
                ->fetchSingle();

            $note = trim($transfer->note);

            $stockTransfersListData[] = array(
                'transferId' => $transferId,
                'stockTransferId' => $transfer->stockTransferId,
                'issuedAt' => substr($transfer->issuedAt, 0, 10),
                'totalAmount' => $totalAmount / 1000,
                'sourceStockId' => $transfer->sourceStockId,
                'targetStockId' => $transfer->targetStockId,
                'sourceStockName' => $transfer->sourceStockName,
                'targetStockName' => $transfer->targetStockName,
                'itCanBeDeleted' => $this->canBeDeletedStockTransfer($transferId, $transfer->targetStockId),
                'note' => $note,
                'isNote' => !empty($note) ? true : false
            );
        }

        return $stockTransfersListData;
    }

    public function canBeDeletedStockTransfer($transferId, $targetStockId) {
        $itBeDeleted = true;
        $dateTo = date('Y-m-d');
        $reportManager = \report\ReportManager::createForFoodBank($this->foodBankId);
        $commoditySummaryList = $reportManager->getStocks($dateTo, null, $targetStockId);
        $availableCommoditiesInStock = array();

        foreach ($commoditySummaryList['commodities'] as $commodity) {
            $availableCommoditiesInStock[$commodity->commodityId] = $commodity->amount;
        }

        $transferCommodity = \form\TransferCommodity::createForFoodBank($this->foodBankId);
        $transferCommodityList = $transferCommodity->getTransferCommodityByTransferId($transferId);

        foreach ($transferCommodityList as $transferCommodity) {
            $transferCommodityAmout = $transferCommodity->amount;

            if (($availableCommoditiesInStock[$transferCommodity->commodityId] - $transferCommodityAmout) < 0) {
                $itBeDeleted = false;
            }
        }

        return $itBeDeleted;
    }

    public function getStockTransferByTransferId($transferId) {
        $stockManager = \form\StockManager::createForFoodBank($this->foodBankId);
        $result = \dibi::select('t.[transferId],t.[stockTransferId],t.[sourceStockId],t.[targetStockId],t.[issuedAt],t.note,t.[appUserId]')
            ->from('[Transfer] AS t')
            ->where('t.[transferId] = %i AND t.[foodBankId] = %i', $transferId, $this->foodBankId)
            ->execute()
            ->fetch();

        $note = trim($result->note);
        $transferCommodity = \form\TransferCommodity::createForFoodBank($this->foodBankId);
        $commoditiesListData = $transferCommodity->getStockTransferCommoditiesByStockTransferId($transferId);

        $stockTransferData = array(
            'issuedAt' => substr($result->issuedAt, 0, 10),
            'nameSourceStock' => $stockManager->getNameStockByStockId($result->sourceStockId),
            'nameTargetStock' => $stockManager->getNameStockByStockId($result->targetStockId),
            'sourceStockId' => $result->sourceStockId,
            'transferId' => $result->transferId,
            'stockTransferId' => $result->stockTransferId,
            'commoditiesListData' => $commoditiesListData,
            'note' => $note,
            'appUserId' => $result->appUserId
        );

        return $stockTransferData;
    }

    private function addDateFromFilter($query) {
        if ($this->dateFrom) {
            array_push($query, 'AND t.[issuedAt] >= %d', $this->dateFrom);
        }
        return $query;
    }

    private function addDateToFilter($query) {
        if ($this->dateTo) {
            array_push($query, 'AND t.[issuedAt] <= %d', $this->dateTo);
        }
        return $query;
    }

    private function addStockFilter($query) {
        if ($this->stockId > 0) {
            array_push($query, 'AND (t.[sourceStockId] = %i OR t.[targetStockId] = %i)', $this->stockId, $this->stockId);
        }

        return $query;
    }

    private function addStockTransferIdFilter($query) {
        if ($this->stockTransferId > 0) {
            array_push($query, ' AND t.[stockTransferId] = %i', $this->stockTransferId);
        }
        return $query;
    }

    private function addTargetStockIdFilter($query) {
        if ($this->targetStockId > 0) {
            array_push($query, ' AND t.[targetStockId] = %i', $this->targetStockId);
        }
        return $query;
    }

    public function saveStockTransfer($transferData) {

        $transferId = intval($transferData['transferId']);
        unset($transferData['transferId']);
        $transferData['foodBankId'] = $this->foodBankId;

        if ($transferId > 0) {
            \dibi::update('Transfer', $transferData)->where("[transferId] = %i", $transferId)->execute();
        } else {
            $transferData['stockTransferId'] = $this->getNewStockTransferId($transferData['sourceStockId']);
            $transferId = \dibi::query("INSERT INTO [Transfer]", $transferData, " RETURNING [transferId] AS id")->fetchSingle();
            \logger\AppUserActivityLogger::logTransferCreated($transferId);
        }
        $transferData['transferId'] = $transferId;
        return (object) $transferData;
    }

    public function getNewStockTransferId($sourceStockId) {

        $result = \dibi::select('MAX([stockTransferId])')
            ->from('[Transfer]')
            ->where('[foodBankId] = %i AND [sourceStockId] = %i', $this->foodBankId, $sourceStockId)
            ->execute();

        $maxId = $result->fetchSingle();
        return ++$maxId;
    }

    private function getTargetStockIdByTransferId($transferId) {

        $stockManager = \form\StockManager::createForFoodBank($this->foodBankId);
        $result = \dibi::select('t.[targetStockId]')->from('[Transfer] AS t')->where('t.[transferId] = %i AND t.[foodBankId] = %i', $transferId, $this->foodBankId)->execute()->fetch();
        return $result->targetStockId;
    }

    private function removeStockTransferByTransferId($transferId) {

        \dibi::delete('Transfer')->where('[transferId] = %i AND [foodBankId] = %i ', $transferId, $this->foodBankId)->execute();
        \logger\AppUserActivityLogger::logTransferDelete($transferId);
    }

    public function deleteStockTransferByTransferId($transferId) {
        $targetStockId = $this->getTargetStockIdByTransferId($transferId);
        if ($this->canBeDeletedStockTransfer($transferId, $targetStockId)) {
            $transferCommodity = \form\TransferCommodity::createForFoodBank($this->foodBankId);
            $transferCommodity->removeTransferCommodityByTransferId($transferId);
            $this->removeStockTransferByTransferId($transferId);
        }
    }
}
