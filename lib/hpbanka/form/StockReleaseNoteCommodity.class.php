<?php
namespace form;

//use logger;
class StockReleaseNoteCommodity {

    private $foodBankId;
    private $reportManager;

    public static function createForFoodBank($foodBankId) {
        $stockReleaseNoteCommodity = new self($foodBankId);
        return $stockReleaseNoteCommodity;
    }

    private function __construct($foodBankId) {
        $this->foodBankId = intval($foodBankId);
        $this->reportManager = \report\ReportManager::createForFoodBank($this->foodBankId);
    }

    public function getCalculatedValueByStockReleaseNoteId($stockReleaseNoteId) {
        return \dibi::select('SUM([pricePerUnit]*[amount])/1000')
            ->from('[StockReleaseNoteCommodity]')
            ->where('[stockReleaseNoteId]= %i AND [foodBankId] = %i', $stockReleaseNoteId, $this->foodBankId)
            ->execute()
            ->fetchSingle();
    }

    public function saveStockReleaseNoteCommodity($commodityList) {
        foreach ($commodityList as $commodity) {
            if (empty($commodity->amount)) {
                continue;
            }

            $values = array(
                'stockReleaseNoteId' => $commodity->stockReleaseNoteId,
                'commodityId' => $commodity->commodityId,
                'amount' => round($commodity->amount * 1000),
                'pricePerUnit' => $commodity->pricePerUnit,
                'foodBankId' => $this->foodBankId
            );

            \dibi::insert('StockReleaseNoteCommodity', $values)->execute();
            //logger\AppUserActivityLogger::logStockReleaseNoteCommodityCreated($commodity->stockReleaseNoteId);
        }
    }

    public function getCommodityListByFormIdAndStockReleaseNoteId($formId, $stockReleaseNoteId, $stockId) {
        $result = \dibi::select('fc.[commodityId], com.name, com.unit, srnc.amount, com.[pricePerUnit], com.code')
            ->from('[FormCommodity] AS fc')
            ->join('[Commodity] AS com')
            ->on('fc.[commodityId]=com.[commodityId]')
            ->leftJoin('[StockReleaseNoteCommodity] AS srnc')
            ->on('srnc.[commodityId]=com.[commodityId] AND srnc.[stockReleaseNoteId] = %i', $stockReleaseNoteId)
            ->where("fc.[formId] = %i AND fc.[foodBankId] = %i", $formId, $this->foodBankId)
            ->orderBy('fc.[itemOrder] ASC')
            ->execute();

        foreach ($result as $commodity) {
            $commodityIds[$commodity->commodityId] = $commodity->commodityId;
        };

        $dateTo = date('Y-m-d'); //TODO k jakemu datu se to ma kontrolovat k datu kdy realizuje nebo datumu vyskladnovani
        $commoditySummary = $this->reportManager->getStocks($dateTo, $commodityIds, $stockId);

        reset($result);
        $commodityListData = array();
        foreach ($result as $commodity) {
            $amount = empty($commodity->amount) ? 0 : $commodity->amount / 1000;
            $availableAmount = empty($commoditySummary['commodities'][$commodity->commodityId]->amount) ? 0 : $commoditySummary['commodities'][$commodity->commodityId]->amount + $amount;

            $commodityListData[] = array(
                'commodityId' => $commodity->commodityId,
                'name' => $commodity->name,
                'unit' => $commodity->unit,
                'amount' => $amount,
                'pricePerUnit' => $commodity->pricePerUnit,
                'code' => $commodity->code,
                'availableAmount' => $availableAmount
            );
        }
        return $commodityListData;
    }

    public function removeStockReleaseNoteCommodityByStockReleaseNoteId($stockReleaseNoteId) {

        \dibi::delete('StockReleaseNoteCommodity')->where('[stockReleaseNoteId] = %i AND [foodBankId] = %i ', $stockReleaseNoteId, $this->foodBankId)->execute();
        $affectedRows = \dibi::getAffectedRows();
        $message = "Pocet smazanych radku je: $affectedRows";
        //logger\AppUserActivityLogger::logStockReleaseNoteCommodityDeleted($stockReleaseNoteId, $message);
    }

    public function getStockReleaseNoteCommodityByStockReleaseNoteId($stockReleaseNoteId) {

        $stockReleaseNoteCommodityList = \dibi::select('c.name, s.amount, c.code, c.[pricePerUnit]')
            ->from('[StockReleaseNoteCommodity] AS s')
            ->join('[Commodity] AS c')->on('s.[commodityId]=c.[commodityId]')
            ->where('s.[stockReleaseNoteId] = %i AND s.[foodBankId] = %i', $stockReleaseNoteId, $this->foodBankId)
            ->execute();

        $stockReleaseNoteCommodityListData = array();
        $totalAmount = 0;
        $priceCalculation = 0;

        foreach ($stockReleaseNoteCommodityList as $stockReleaseNoteCommodity) {

            $amount = $stockReleaseNoteCommodity->amount;
            $priceCalculation += ($amount * ($stockReleaseNoteCommodity->pricePerUnit / 1000));
            $totalAmount += $amount;
            $stockReleaseNoteCommodityListData[] = array(
                'name' => $stockReleaseNoteCommodity->name,
                'amount' => $amount / 1000,
                'code' => $stockReleaseNoteCommodity->code
            );
        }

        return array(
            'stockReleaseNoteCommodityListData' => $stockReleaseNoteCommodityListData,
            'totalAmount' => $totalAmount / 1000,
            'priceCalculation' => $priceCalculation / 1000
        );
    }

    public function getUsedComodityIdListByFormId($formId) {
        $result = \dibi::select('fc.[commodityId]')
            ->from('[FormCommodity] AS fc')
            ->join('[Commodity] AS com')
            ->on('fc.[commodityId]=com.[commodityId]')
            ->join('[StockReleaseNote] AS srn')
            ->on('srn.[formId]=fc.[formId]')
            ->join('[StockReleaseNoteCommodity] AS srnc')
            ->on('srnc.[commodityId]=com.[commodityId]')
            ->where("fc.[formId] = %i AND fc.[foodBankId] = %i AND srn.[stockReleaseNoteId]=srnc.[stockReleaseNoteId]", $formId, $this->foodBankId)
            ->execute();

        $commodityIdList = array();
        foreach ($result as $commodity) {
            $commodityIdList[$commodity->commodityId] = $commodity->commodityId;
        };

        return $commodityIdList;
    }
}
