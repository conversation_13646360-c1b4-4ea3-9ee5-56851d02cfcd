<?php
namespace form;

class TransferCommodity {

    private $foodBankId;

    public static function createForFoodBank($foodBankId) {
        $transferCommodity = new self($foodBankId);
        return $transferCommodity;
    }

    private function __construct($foodBankId) {
        $this->foodBankId = intval($foodBankId);
    }

    public function removeTransferCommodityByTransferId($transferId) {
        \dibi::delete('TransferCommodity')->where('[transferId] = %i AND [foodBankId] = %i ', $transferId, $this->foodBankId)->execute();
        $affectedRows = \dibi::getAffectedRows();
        $message = "Pocet smazanych radku je: $affectedRows";
        \logger\AppUserActivityLogger::logTransferCommodityDeleted($transferId, $message);
    }

    public function saveTransferCommodity($transferCommodityList) {

        foreach ($transferCommodityList as $commodity) {
            if (empty($commodity->amount)) {
                continue;
            }

            $values = array(
                'transferId' => $commodity->transferId,
                'commodityId' => $commodity->commodityId,
                'amount' => round($commodity->amount * 1000),
                'pricePerUnit' => $commodity->pricePerUnit,
                'foodBankId' => $this->foodBankId
            );

            \dibi::insert('TransferCommodity', $values)->execute();
            $message = "CommodityId je: " . $commodity->commodityId;
            \logger\AppUserActivityLogger::logTransferCommodityCreated($commodity->transferId, $message);
        }
    }

    public function getTransferCommodityByTransferIdAndStockId($transferId, $stockId) {

        $transferCommodityList = $this->getTransferCommodityByTransferId($transferId);
        $transferCommodityListData = array();
        foreach ($transferCommodityList as $transferCommodity) {
            $transferCommodityListData[$transferCommodity->commodityId.'-'.$transferCommodity->pricePerUnit] = $transferCommodity;
        }
        $dateTo = date('Y-m-d');
        $reportManager = \report\ReportManager::createForFoodBank($this->foodBankId);
        $commoditySummaryList= $reportManager->getAvailableCommodities($dateTo, null, $stockId);
        $commodities = $commoditySummaryList['commodities'];

        foreach ($transferCommodityListData as $commodityFromTransfer) {

            $commodityKey = $commodityFromTransfer['commodityId'].'-'.$commodityFromTransfer['pricePerUnit'];
            if (array_key_exists($commodityKey, $commodities)) {
                $commodities[$commodityKey]->amount += $commodityFromTransfer->amount;
            } else {
                $commodities[$commodityKey] = $commodityFromTransfer;
            }
        }

        $commodityListData = array();
        foreach ($commodities as $commodity) {

            $amount = ($transferCommodityListData[$commodity->commodityId.'-'.$commodity->pricePerUnit]['amount']);
            $commodityListData[] = array(
                    'commodityId' => $commodity->commodityId,
                    'name' => $commodity->name,
                    'unit' => $commodity->unit,
                    'availableAmount' => $commodity->amount,
                    'code' => $commodity->code,
                    'amount' => empty($amount) ? '' : $amount,
                    'minTransferAmount' => empty($amount) ? '' : $amount,
                    'value' => $commodity->value,
                    'pricePerUnit' => $commodity->pricePerUnit,
                    'currencySymbol' => getCurrency()
            );
        }

        return $commodityListData;
    }

    public function getTransferCommodityByTransferId($transferId) {
        $query = array();
        $query[] = 'SELECT tc.[commodityId], ROUND((tc.[amount] / 1000), 2) as [amount], tc.[pricePerUnit], c.[name], c.[unit], c.[code], 0 as [value] ';
        $query[] = 'FROM [TransferCommodity] AS tc ';
        $query[] = 'JOIN [Commodity] AS c ON c.[commodityId]=tc.[commodityId] ';
        array_push($query, 'WHERE tc.[transferId] = %i', $transferId);
        return \dibi::query($query);
    }

    public function getStockTransferCommoditiesByStockTransferId($transferId) {

        $commoditiesList = \dibi::select('c.name, tc.amount, c.code, tc.[pricePerUnit]')
            ->from('[TransferCommodity] AS tc')
            ->join('[Commodity] AS c')->on('tc.[commodityId]=c.[commodityId]')
            ->where('tc.[transferId] = %i AND tc.[foodBankId] = %i', $transferId, $this->foodBankId)
            ->execute();

        $commoditiesListData = array();
        $totalAmount = 0;
        $priceCalculation = 0;

        foreach ($commoditiesList as $commodity) {
            $amount = $commodity->amount;
            $priceCalculation += ($amount * ($commodity->pricePerUnit / 1000));
            $totalAmount += $amount;
            $commoditiesListData[] = array(
                'name' => $commodity->name,
                'amount' => $amount / 1000,
                'code' => $commodity->code,
                'pricePerUnit' => $commodity->pricePerUnit / 1000
            );
        }

        return array(
            'commoditiesListData' => $commoditiesListData,
            'totalAmount' => $totalAmount / 1000,
            'priceCalculation' => $priceCalculation / 1000
        );
    }
}
