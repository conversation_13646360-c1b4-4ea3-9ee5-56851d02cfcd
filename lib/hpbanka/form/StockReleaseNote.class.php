<?php

namespace form;

//use logger;
class StockReleaseNote {
    private $foodBankId;
    private $customerName;
    private $dateFrom;
    private $dateTo;
    private $stockId;
    private $withoutDonationAgreement;

    protected function __construct($foodBankId) {
        $this->foodBankId = intval($foodBankId);
    }

    public static function createForFoodBank($foodBankId) {
        $stockReleaseNote = new self($foodBankId);
        return $stockReleaseNote;
    }

    public function saveStockReleaseNote($stockReleaseNote) {
        $stockReleaseNoteId = intval($stockReleaseNote['stockReleaseNoteId']);
        unset($stockReleaseNote['stockReleaseNoteId']);
        $stockReleaseNote['foodBankId'] = $this->foodBankId;

        if ($stockReleaseNoteId > 0) {
            \dibi::update('StockReleaseNote', $stockReleaseNote)->where("[stockReleaseNoteId] = %i", $stockReleaseNoteId)->execute();
            //logger\AppUserActivityLogger::logStockReleaseNoteUpdated($stockReleaseNoteId);
            if (!empty($stockReleaseNote['filledFormId'])) { // pokud se edituje primy odber potom uloz stejne datum do prijemky
                $filledFormData['filledFormId'] = $stockReleaseNote['filledFormId'];
                $filledFormData['consumptionDate'] = $stockReleaseNote['issuedAt'];
                $filledForm = \form\FilledForm::createForFoodBank($this->foodBankId);
                $filledForm->saveFilledForm($filledFormData);
            }
        } else {
            $stockReleaseNote['stockStockReleaseNoteId'] = $this->getNewStockStockReleaseNoteId($stockReleaseNote['stockId']);
            $stockReleaseNote['foodBankStockReleaseNoteId'] = $this->getNewFoodBankStockReleaseNoteId();
            $stockReleaseNoteId = \dibi::query("INSERT INTO [StockReleaseNote]", $stockReleaseNote, " RETURNING [stockReleaseNoteId] AS id")->fetchSingle();

            $message = "customerId = " . $stockReleaseNote['customerId'] . " customerName = " . $stockReleaseNote['customerName'];
            $message .= " customerAddress = " . $stockReleaseNote['customerAddress'] . " customerIC = " . $stockReleaseNote['customerIC'];
            //logger\AppUserActivityLogger::logStockReleaseNoteCreated($stockReleaseNoteId, $message);
        }
        $stockReleaseNote['stockReleaseNoteId'] = $stockReleaseNoteId;
        return (object) $stockReleaseNote;
    }

    public function updateFilledFormNote($note, $id) {
        $stockReleaseNoteId = intval($id);

        if ($stockReleaseNoteId > 0) {
            \dibi::update('StockReleaseNote', ['note' => $note])->where("[stockReleaseNoteId] = %i", $stockReleaseNoteId)->execute();
        }
    }

    public function updateTotalAmount($totalAmount, $id) {
        $stockReleaseNoteId = intval($id);

        if ($stockReleaseNoteId > 0) {
            \dibi::update('StockReleaseNote', ['totalAmount' => $totalAmount])->where("[stockReleaseNoteId] = %i", $stockReleaseNoteId)->execute();
        }
    }

    public function getNewStockStockReleaseNoteId($stockId) {
        $result = \dibi::select('MAX([stockStockReleaseNoteId])')
            ->from('[StockReleaseNote]')
            ->where('[foodBankId] = %i AND [stockId] = %i', $this->foodBankId, $stockId)
            ->execute();

        $maxId = $result->fetchSingle();
        return ++$maxId;
    }

    public function getNewFoodBankStockReleaseNoteId() {
        $result = \dibi::select('MAX([foodBankStockReleaseNoteId])')
            ->from('[StockReleaseNote]')
            ->where('[foodBankId] = %i', $this->foodBankId)
            ->execute();

        $maxId = $result->fetchSingle();
        return ++$maxId;
    }

    public function setCustomerNameFilter($customerName) {
        $this->customerName = mb_strtolower($customerName, "UTF8");
    }

    public function setDateFromFilter($dateFrom) {
        $this->dateFrom = $dateFrom;
    }

    public function setDateToFilter($dateTo) {
        $this->dateTo = $dateTo;
    }

    public function setStockByAppUser($appUserId) {
        $this->stockId = \core\AppUserManager::getCurrentStockIdByAppUser($appUserId);
    }

    public function setStockStockReleaseNoteIdToFilter($stockStockReleaseNoteId) {
        $this->stockStockReleaseNoteId = intval($stockStockReleaseNoteId);
    }

    public function setActionNameToFilter($actionName) {
        $this->actionName = mb_strtolower($actionName, "UTF8");
    }

    public function setWithoutDonationAgreementToFilter($withoutDonationAgreement) {
        $this->withoutDonationAgreement = (bool) $withoutDonationAgreement;
    }

    public function getStockReleaseNoteList() {
        $query = array();
        $query[] = 'SELECT srn.[stockReleaseNoteId], srn.[foodBankStockReleaseNoteId], srn.[stockStockReleaseNoteId], cust.name, cust.archived, f.[actionName], srn.[issuedAt], srn.[donationAgreementId], srn.[filledFormId], ff.[stockFilledFormId], srn.[note], srn.[totalAmount]';
        $query[] = 'FROM [StockReleaseNote] AS srn';
        $query[] = 'JOIN [Form] AS f ON srn.[formId]=f.[formId]';
        $query[] = 'LEFT JOIN [FilledForm] AS ff ON srn.[filledFormId]=ff.[filledFormId]';
        $query = $this->addCustomerNameFilterOrJoin($query);
        array_push($query, 'WHERE srn.[foodBankId] = %i', $this->foodBankId);
        $query = $this->addDateFromFilter($query);
        $query = $this->addDateToFilter($query);
        $query = $this->addStock($query, 'srn');
        $query = $this->addActionNameFilter($query);
        $query = $this->addWithoutDonationAgreementToFilter($query);
        $query = $this->addStockStockReleaseNoteIdFilter($query);
        $query[] = 'ORDER BY srn.[foodBankStockReleaseNoteId] DESC';

        $stockReleaseNoteList = \dibi::query($query);
        $stockReleaseNoteListData = array();

        foreach ($stockReleaseNoteList as $stockReleaseNote) {
            $stockReleaseNoteId = $stockReleaseNote->stockReleaseNoteId;
            $note = trim($stockReleaseNote->note);
            $stockReleaseNoteListData[] = array(
                'stockReleaseNoteId' => $stockReleaseNoteId,
                'foodBankStockReleaseNoteId' => $stockReleaseNote->foodBankStockReleaseNoteId,
                'stockStockReleaseNoteId' => $stockReleaseNote->stockStockReleaseNoteId,
                'name' => $stockReleaseNote->name,
                'actionName' => $stockReleaseNote->actionName,
                'issuedAt' => substr($stockReleaseNote->issuedAt, 0, 10),
                'totalAmount' => $stockReleaseNote->totalAmount / 1000,
                'donationAgreementId' => $stockReleaseNote->donationAgreementId,
                'isDirectConsumption' => empty($stockReleaseNote->filledFormId) ? false : true,
                'filledFormId' => $stockReleaseNote->filledFormId,
                'stockFilledFormId' => $stockReleaseNote->stockFilledFormId ? $stockReleaseNote->stockFilledFormId : '--',
                'note' => $note,
                'isNote' => !empty($note) ? true : false,
                'isCustomerArchived' => $stockReleaseNote->archived == 't' ? true : false
            );
        }

        return $stockReleaseNoteListData;
    }

    private function addCustomerNameFilterOrJoin($query) {
        $join = 'JOIN [Customer] AS cust ON cust.[customerId]=srn.[customerId]';
        if ($this->customerName) {
            array_push($query, $join . ' AND lower(cust.[name]) LIKE %~like~', $this->customerName);
        } else {
            $query[] = $join;
        }
        return $query;
    }

    private function addDateFromFilter($query) {
        if ($this->dateFrom) {
            array_push($query, 'AND srn.[issuedAt] >= %d', $this->dateFrom);
        }
        return $query;
    }

    private function addDateToFilter($query) {
        if ($this->dateTo) {
            array_push($query, 'AND srn.[issuedAt] <= %d', $this->dateTo);
        }
        return $query;
    }

    private function addStock($query, $tableAlias) {
        if ($this->stockId > 0) {
            array_push($query, 'AND ' . $tableAlias . '.[stockId] = %i', $this->stockId);
        }
        return $query;
    }

    private function addStockStockReleaseNoteIdFilter($query) {
        if ($this->stockStockReleaseNoteId > 0) {
            array_push($query, ' AND srn.[stockStockReleaseNoteId] = %i', $this->stockStockReleaseNoteId);
        }
        return $query;
    }

    private function addActionNameFilter($query) {
        if ($this->actionName) {
            array_push($query, ' AND lower(f.[actionName]) LIKE %~like~', $this->actionName);
        }
        return $query;
    }

    private function addWithoutDonationAgreementToFilter($query) {
        if ($this->withoutDonationAgreement) {
            array_push($query, ' AND srn.[donationAgreementId] IS NULL');
        }
        return $query;
    }

    public function getStockReleaseNoteByStockReleaseNoteId($stockReleaseNoteId) {
        global $_foodBank;

        $stockManager = \form\StockManager::createForFoodBank($this->foodBankId);

        $result = \dibi::select('srn.[issuedAt], f.[actionName], srn.[stockReleaseNoteId], srn.[foodBankStockReleaseNoteId], srn.[formId], srn.[customerId],' .
            'cus.[name] AS [customerName], s.[name] AS [supplierName], srn.[filledFormId], ff.[giftValue], srn.[stockId], srn.[note]')
            ->from('[StockReleaseNote] AS srn')
            ->join('[Form] AS f')->on('f.[formId]=srn.[formId]')
            ->leftJoin('[FilledForm] AS ff')->on('ff.[filledFormId]=srn.[filledFormId]')
            ->leftJoin('[Supplier] AS s')->on('s.[supplierId]=ff.[supplierId]')
            ->join('[Customer] AS cus')->on('cus.[customerId]=srn.[customerId]')
            ->where('srn.[stockReleaseNoteId] = %i AND srn.[foodBankId] = %i', $stockReleaseNoteId, $this->foodBankId)
            ->execute()
            ->fetch();

        if (empty($result->filledFormId)) {
            $result->supplierName = $_foodBank->pbName;
        }

        $note = trim($result->note);

        $stockReleaseNoteData = array(
            'issuedAt' => substr($result->issuedAt, 0, 10),
            'actionName' => $result->actionName,
            'stockReleaseNoteId' => $result->stockReleaseNoteId,
            'foodBankStockReleaseNoteId' => $result->foodBankStockReleaseNoteId,
            'formId' => $result->formId,
            'customerId' => $result->customerId,
            'customerName' => $result->customerName,
            'supplierName' => $result->supplierName,
            'giftValue' => $result->giftValue / 1000,
            'stockId' => $result->stockId,
            'nameStock' => $stockManager->getNameStockByStockId($result->stockId),
            'filledFormId' => $result->filledFormId,
            'note' => $note,
            'isNote' => !empty($note) ? true : false
        );

        return $stockReleaseNoteData;
    }

    public function getStockReleaseNoteListByStockReleaseNoteIds($stockReleaseNoteIds) {

        $stockReleaseNoteList = \dibi::select('srn.[issuedAt], f.[actionName], srn.[stockReleaseNoteId], srn.[foodBankStockReleaseNoteId], srn.[formId], srn.[customerId],' .
            ' srn.[customerName], srn.[filledFormId], srn.[stockStockReleaseNoteId], srn.[customerAddress], srn.[customerIC], srn.[foodBankName], srn.[foodBankAddress], srn.[foodBankIC],' .
            ' st.[name] AS [stockName], srn.[appUserId], srn.[note]')
            ->from('[StockReleaseNote] AS srn')
            ->join('[Form] AS f')->on('f.[formId]=srn.[formId]')
            ->join('[Stock] AS st')->on('st.[stockId]=srn.[stockId]')
            ->where('srn.[stockReleaseNoteId] IN (%i) AND srn.[foodBankId] = %i', $stockReleaseNoteIds, $this->foodBankId)
            ->execute();

        $stockReleaseNoteListData = array();

        foreach ($stockReleaseNoteList as $stockReleaseNote) {
            $stockReleaseNoteCommodity = \form\StockReleaseNoteCommodity::createForFoodBank($this->foodBankId);
            $commodityListData = $stockReleaseNoteCommodity->getStockReleaseNoteCommodityByStockReleaseNoteId($stockReleaseNote->stockReleaseNoteId);
            $stockReleaseNoteListData[] = array(
                'issuedAt' => substr($stockReleaseNote->issuedAt, 0, 10),
                'actionName' => $stockReleaseNote->actionName,
                'customerId' => $stockReleaseNote->customerId,
                'stockStockReleaseNoteId' => $stockReleaseNote->stockStockReleaseNoteId,
                'commodityListData' => $commodityListData,
                'customerName' => $stockReleaseNote->customerName,
                'customerAddress' => $stockReleaseNote->customerAddress,
                'customerIC' => $stockReleaseNote->customerIC,
                'foodBankName' => $stockReleaseNote->foodBankName,
                'foodBankAddress' => $stockReleaseNote->foodBankAddress,
                'foodBankIC' => $stockReleaseNote->foodBankIC,
                'stockName' => $stockReleaseNote->stockName,
                'note' => $stockReleaseNote->note,
                'isNote' => !empty(trim($stockReleaseNote->note)) ? 1 : 0,
                'appUserId' => $stockReleaseNote->appUserId
            );
        }

        return $stockReleaseNoteListData;
    }

    public function getStockReleaseNoteIdsByFilledFormId($filledFormId) {

        $result = \dibi::select('[stockReleaseNoteId], [stockStockReleaseNoteId], [donationAgreementId], [customerId]')
            ->from('[StockReleaseNote]')
            ->where('[filledFormId] = %i AND [foodBankId] = %i', $filledFormId, $this->foodBankId)
            ->execute()
            ->fetch();

        return $result;
    }

    public function getCustomerIdByStockReleaseNoteId($stockReleaseNoteId) {

        $result = \dibi::select('[customerId]')
            ->from('[StockReleaseNote]')
            ->where('[foodBankId] = %i AND [stockReleaseNoteId] = %i', $this->foodBankId, $stockReleaseNoteId)
            ->execute();

        return $result->fetchSingle();
    }
}
