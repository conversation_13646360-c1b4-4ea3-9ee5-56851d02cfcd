<?php

namespace form;

class FormManager {

    private $foodBankId;
    private $formCommodity;
    private $formSupplier;

    public function __construct($foodBankId) {

        $this->foodBankId = intval($foodBankId);
        $this->formCommodity = FormCommodity::createForFoodBank($this->foodBankId);
        $this->formSupplier = FormSupplier::createForFoodBank($this->foodBankId);
    }

    public static function createForFoodBank($foodBankId) {

        $formManager = new self($foodBankId);
        return $formManager;
    }

    public function getFormList() {

        return \dibi::select('*')->from('Form')->where('[foodBankId] = %i', $this->foodBankId)->orderBy('[validFrom]', 'DESC')->execute();
    }

    public function getActiveFormListForDirectConsumption($sqlDate) {
        $result = \dibi::select('*')->from('Form')
            ->where('[validFrom] <= %s AND [validTo] >= %s AND [foodBankId] = %i AND [directConsumption] = %b', $sqlDate, $sqlDate, $this->foodBankId, true)
            ->execute();
        return $result;
    }

    public function getActiveFormListForStock($sqlDate) {
        $result = \dibi::select('*')->from('Form')
            ->where('[validFrom] <= %s AND [validTo] >= %s AND [foodBankId] = %i AND ([incomePerStock] = %b OR [directConsumption] = %b)', $sqlDate, $sqlDate, $this->foodBankId, true, true)
            ->execute();
        return $result;
    }

    public function getActiveFormListForOutputFromStock($sqlDate) {
        $result = \dibi::select('*')->from('Form')
            ->where('[validFrom] <= %s AND [validTo] >= %s AND [foodBankId] = %i AND [outputFromStock] = %b', $sqlDate, $sqlDate, $this->foodBankId, true)
            ->execute();
        return $result;
    }

    public function getForm($formId) {
        $result = \dibi::select('*')->from('Form')->where('[formId] = %i AND [foodBankId] = %i', $formId, $this->foodBankId)->execute();
        return $result->fetch();
    }

    public function saveForm($form) {
        $formId = $form['formId'];
        unset($form['formId']);
        $values = (array) $form;
        if ($formId > 0) {
            \dibi::update('Form', $values)->where("[formId] = %i", $formId)->execute();
        } else {
            \dibi::insert('Form', $values)->execute();
        }
    }

    public function isFormFilled($formId) {
        $rowCountFilledForm = \dibi::select('COUNT(*)')->from('FilledForm')->where('[formId] = %i AND [foodBankId] = %i', $formId, $this->foodBankId)->execute()->fetchSingle();
        $rowCountStockReleaseNote = \dibi::select('COUNT(*)')->from('StockReleaseNote')->where('[formId] = %i AND [foodBankId] = %i', $formId, $this->foodBankId)->execute()->fetchSingle();
        return ($rowCountFilledForm or $rowCountStockReleaseNote) ? true : false;
    }



    public function deleteForm($formId) {
        \dibi::delete('Form')->where('[formId] = %i AND [foodBankId] = %i', $formId, $this->foodBankId)->execute();
        $this->formCommodity->removeFormCommodityByFormId($formId);
        $this->formSupplier->removeFormSupplierByFormId($formId);
    }

    public function getStockIdByFormId($formId) {
        return \dibi::select('[stockId]')->from('Form')->where('[formId] = %i AND [foodBankId] = %i', $formId, $this->foodBankId)->fetchSingle();
    }
}
