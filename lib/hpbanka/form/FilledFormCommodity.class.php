<?php
namespace form;

class FilledFormCommodity {

    private $foodBankId;

    public function __construct($foodBankId) {
        $this->foodBankId = intval($foodBankId);
    }

    public static function createForFoodBank($foodBankId) {
        $filledFormCommodity = new self($foodBankId);
        return $filledFormCommodity;
    }

    public function getCalculatedValueByStockReleaseNoteId($stockReleaseNoteId) {
        $calculatedValue = \dibi::select('SUM([pricePerUnit]*[amount])/1000')
            ->from('[StockReleaseNoteCommodity]')
            ->where('[stockReleaseNoteId]= %i AND [foodBankId] = %i', $stockReleaseNoteId, $this->foodBankId)
            ->execute()
            ->fetchSingle();
        return $calculatedValue;
    }

    public function removeFilledFormCommodityByFilledFormId($filledFormId) {
        \dibi::delete('FilledFormCommodity')
            ->where('[filledFormId] = %i AND [foodBankId] = %i ', $filledFormId, $this->foodBankId)
            ->execute();
    }

    public function saveFilledFormCommodity($commodityList) {
        foreach ($commodityList as $commodity) {
            if (empty($commodity->amount)) {
                continue;
            }

            $values = array(
                'filledFormId' => $commodity->filledFormId,
                'commodityId' => $commodity->commodityId,
                'amount' => round($commodity->amount * 1000),
                'pricePerUnit' => $commodity->pricePerUnit,
                'foodBankId' => $this->foodBankId
            );

            \dibi::insert('FilledFormCommodity', $values)->execute();
        }
    }

    public function getFilledFormCommodityByFilledFormId($filledFormId) {

        $filledFormCommodityList = \dibi::select('c.name, ffc.amount, c.code, c.[pricePerUnit]')
            ->from('[FilledFormCommodity] AS ffc')
            ->join('[Commodity] AS c')->on('ffc.[commodityId]=c.[commodityId]')
            ->where('ffc.[filledFormId] = %i AND ffc.[foodBankId] = %i', $filledFormId, $this->foodBankId)
            ->execute();

        $filledFormCommodityListData = array();
        $totalAmount = 0;
        $priceCalculation = 0;

        foreach ($filledFormCommodityList as $filledFormCommodity) {
            $amount = $filledFormCommodity->amount;
            $priceCalculation += ($amount * ($filledFormCommodity->pricePerUnit / 1000));
            $totalAmount += $amount;
            $filledFormCommodityListData[] = array(
                'name' => $filledFormCommodity->name,
                'amount' => $amount / 1000,
                'code' => $filledFormCommodity->code
            );
        }

        return array('filledFormCommodityListData' => $filledFormCommodityListData,
            'totalAmount' => $totalAmount / 1000,
            'priceCalculation' => $priceCalculation / 1000
        );
    }

    public function getCommodityListByFormIdAndFilledFormId($formId, $filledFormId) {

        $result = \dibi::select('fc.[commodityId], com.name, com.unit, ffc.amount, com.[pricePerUnit], com.code')
            ->from('[FormCommodity] AS fc')
            ->join('[Commodity] AS com')
            ->on('fc.[commodityId]=com.[commodityId]')
            ->leftJoin('[FilledFormCommodity] AS ffc')
            ->on('ffc.[commodityId]=com.[commodityId] AND ffc.[filledFormId] = %i', $filledFormId)
            ->where("fc.[formId] = %i AND fc.[foodBankId] = %i", $formId, $this->foodBankId)
            ->orderBy('fc.[itemOrder] ASC')
            ->execute();

        $commodityListData = array();
        foreach ($result as $commodity) {
            $commodityListData[] = array(
                'commodityId' => $commodity->commodityId,
                'name' => $commodity->name,
                'unit' => $commodity->unit,
                'amount' => empty($commodity->amount) ? 0 : $commodity->amount / 1000,
                'pricePerUnit' => $commodity->pricePerUnit,
                'code' => $commodity->code
            );
        }

        return $commodityListData;
    }

    public function getUsedComodityIdListByFormId($formId) {
        $result = \dibi::select('fc.[commodityId]')
            ->from('[FormCommodity] AS fc')
            ->join('[Commodity] AS com')
            ->on('fc.[commodityId]=com.[commodityId]')
            ->join('[FilledForm] AS ff')
            ->on('ff.[formId]=fc.[formId]')
            ->join('[FilledFormCommodity] AS ffc')
            ->on('ffc.[commodityId]=com.[commodityId]')
            ->where("fc.[formId] = %i AND fc.[foodBankId] = %i AND ff.[filledFormId]=ffc.[filledFormId]", $formId, $this->foodBankId)
            ->execute();

        $commodityIdList = array();
        foreach ($result as $commodity) {
            $commodityIdList[$commodity->commodityId] = $commodity->commodityId;
        };

        return $commodityIdList;
    }
}
