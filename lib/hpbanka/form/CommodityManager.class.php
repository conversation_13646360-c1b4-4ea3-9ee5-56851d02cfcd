<?php
namespace form;

class CommodityManager {
  
  public function getCommodityList() {
    
    $list = \dibi::select('*')->from('Commodity')->orderBy('[commodityId] ASC')->execute();
    $commodities = array();
    foreach ($list as $commodity) {
        $commodities[$commodity->commodityId] = $commodity;
    }
    
    return $commodities;
  }

  public function getUsedCommodityIdList() {
    
    $list = \dibi::select('DISTINCT[commodityId]')->from('FilledFormCommodity')->orderBy('[commodityId] ASC')->fetchAll();
    $usedCommodityId = array();
    foreach ($list as $row) {
      $commodityId = $row->commodityId;
      $usedCommodityId[$commodityId] = $commodityId;
    }
    return $usedCommodityId;
  }
  
  
	public function getCommodityListForUse($id) {
	  $formId = intval($id);
	  return \dibi::select('*')->from('Commodity')->where('[commodityId] NOT IN (SELECT [commodityId] FROM [FormCommodity] WHERE [formId] = ' . $formId . ')')->execute();
	}
	
	
	public function getSelectedCommodityList($formId) {
	  
		$result = \dibi::select('*')
		  ->from('[FormCommodity] AS fc')
		  ->join('[Commodity] AS com')
		  ->on('fc.[commodityId]=com.[commodityId]')
		  ->where("fc.[formId] = %i", $formId)
		  ->orderBy('fc.[itemOrder] ASC')
		  ->execute();
	  
		$commodities = array();
        foreach ($result as $commodity) {
            $commodities[$commodity->commodityId] = $commodity;
        }
        
        return $commodities;
	}
	
	public function saveCommodity($commodity) {
	     
	    $commodityId = intval($commodity['commodityId']);
	    unset($commodity['commodityId']);
	    $values = (array) $commodity;
	     
	    if ($commodityId > 0) {
	        return \dibi::update('Commodity', $values)->where("[commodityId] = %i", $commodityId)->execute();
	    } else {
	        return \dibi::insert('Commodity', $values)->execute();
	    }
	}
	
	
	public function deleteCommodity($commodityId) {
	    $values = (array) $commodity;
	    return \dibi::delete('Commodity')->where('[commodityId] = %i', $commodityId)->execute();
	}
}