<?php
namespace form;

class StockManager {

    private $foodBankId;
    private $foodBankManager;
    const summaryItemStockId = 0;
    
    public function __construct($foodBankId) {
        $this->foodBankId = intval($foodBankId);
        $this->foodBankManager = \party\FoodBankManager::createForFoodBank($this->foodBankId);
    }
    
    public static function createForFoodBank($foodBankId) {
        $stockManager = new self($foodBankId);
        return $stockManager;
    }
    
    public function getStockList($addSummaryItem = true) {
        $result = \dibi::select('[stockId], name')
                        ->from('[Stock]')
                       ->where('[foodBankId] = %i', $this->foodBankId)
                     ->orderBy('name ASC')
                     ->execute();
      
        $stockListData = array();
        if ($addSummaryItem) {
            $stockListData[] = self::getSummaryItemStock();
        }
        foreach ($result as $stock) {
            $stockListData[] = array('stockId' => $stock->stockId, 'name' => $stock->name);
        }
      
        return $stockListData;
    }
    
    public function getStockListWithEmptyItem() {
        $stockListData = array();
        $stockListData[] = self::getEmptyItemStock();
        $list = $this->getStockList(false);
        foreach ($list as $stock) {
            $stockListData[] = $stock;
        }
        return $stockListData;
    }

    public function getStockListWhitoutStockId($stockId) {
        
        $query = array();
        $query[] = 'SELECT [stockId], [name] ';
        $query[] = 'FROM [Stock] ';
        array_push($query, 'WHERE [foodBankId] = %i', $this->foodBankId);
        array_push($query, ' AND [stockId] <> %i', $stockId);
        $query[] = ' ORDER BY [name] ASC';
        $result = \dibi::query($query);
        
        $stockListData = array();
        if ($addEmptyItem) {
            $stockListData[] = self::getEmptyItemStock();
        }
        
        foreach ($result as $stock) {
            $stockListData[] = array('stockId' => $stock->stockId, 'name' => $stock->name);
        }
    
        return $stockListData;
    }
    
    public function getCountStock() {
        return \dibi::select('count([stockId])')->from('[Stock]')->where('[foodBankId] = %i', $this->foodBankId)->execute()->fetchSingle();
    }
    
    public function getNameStockByStockId($stockId) {
        
        $nameStock = \dibi::select('[name]')->from('[Stock]')->where('[foodBankId] = %i AND [stockId] = %i ', $this->foodBankId, $stockId)->execute()->fetchSingle();
        if (!$nameStock) {
            $summaryItemStock = self::getSummaryItemStock();
            $nameStock = $summaryItemStock['stockId'] == $stockId ? $summaryItemStock['name'] : false;
        }
        return $nameStock;
    }
    
    public function getSummaryItemStock() {
        return array('stockId'=> 0, 'name' => 'Sklady celkem');
    }

    public function getEmptyItemStock() {
        return array('stockId'=> 0, 'name' => '');
    }
    
    public function saveStock($stock) {
        
        $stockId = intval($stock['stockId']);
        unset($stock['stockId']);
        $values = (array) $stock;
        if ($stockId > 0) {
            return \dibi::update('Stock', $values)->where("[stockId] = %i", $stockId)->execute();
        } else {
            $values['foodBankId'] = $this->foodBankId;
            \dibi::insert('Stock', $values)->execute();
            $countStock = $this->getCountStock();
            $this->foodBankManager->setMoreThanOneStockByCountStock($countStock);
        }
    }
    
    public function deleteStock($stockId) {
        \dibi::delete('Stock')->where('[stockId] = %i AND [foodBankId] = %i', $stockId, $this->foodBankId)->execute();
        $countStock = $this->getCountStock();
        $this->foodBankManager->setMoreThanOneStockByCountStock($countStock);
        \core\AppUserManager::saveDefaultValueCurrentStockIdForAdminUserType($stockId);
    }
    
    public function getUsedStockIdList() {
    
        $list = \dibi::select('DISTINCT[stockId]')->from('[Form]')->where('[foodBankId] = %i', $this->foodBankId)->orderBy('[stockId] ASC')->fetchAll();
        $usedStockId = array();
        foreach ($list as $row) {
            $stockId = $row->stockId;
            $usedStockId[$stockId] = $stockId;
        }
        return $usedStockId;
    }
}