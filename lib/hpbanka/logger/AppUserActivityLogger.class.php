<?php
namespace logger;

class AppUserActivityLogger {
    

    public static function logStockReleaseNoteCreated($stockReleaseNoteId, $message) {
    
        self::log(ObjectNameForLogEnum::StockReleaseNote, $stockReleaseNoteId, ActivityNameForLogEnum::created, $message);
    }
    
    public static function logStockReleaseNoteUpdated($stockReleaseNoteId) {
    
        self::log(ObjectNameForLogEnum::StockReleaseNote, $stockReleaseNoteId, ActivityNameForLogEnum::updated);
    }
    
    public static function logStockReleaseNoteCommodityCreated($stockReleaseNoteId) {
        
        self::log(ObjectNameForLogEnum::StockReleaseNoteCommodity, $stockReleaseNoteId, ActivityNameForLogEnum::created);
    }
    
    public static function logStockReleaseNoteCommodityDeleted($stockReleaseNoteId, $message) {
        
        self::log(ObjectNameForLogEnum::StockReleaseNoteCommodity, $stockReleaseNoteId, ActivityNameForLogEnum::deleted, $message);
    }
    
    public static function logTransferCreated($transferId) {
    
        self::log(ObjectNameForLogEnum::Transfer, $transferId, ActivityNameForLogEnum::created);
    }
    
    public static function logTransferDelete($transferId) {
    
        self::log(ObjectNameForLogEnum::Transfer, $transferId, ActivityNameForLogEnum::deleted);
    }
    
    public static function logTransferCommodityCreated($transferId, $message) {
    
        self::log(ObjectNameForLogEnum::TransferCommodity, $transferId, ActivityNameForLogEnum::created, $message);
    }
    
    public static function logTransferCommodityDeleted($transferId, $message) {
    
        self::log(ObjectNameForLogEnum::TransferCommodity, $transferId, ActivityNameForLogEnum::deleted, $message);
    }
    
    
    
    
    private static function log($objectName, $objectId, $activityName, $message="") {
        global $_loggedAppUser;
        
        $appUserActivityLogToBeSaved = array(
                'appUserId' => intval($_loggedAppUser->appUserId),
                'objectName' => $objectName,
                'objectId' => intval($objectId),
                'activityName' => $activityName,
                'message' => $message,
                'foodBankId' => intval($_loggedAppUser->foodBankId)
        );
        
        \dibi::query("INSERT INTO [AppUserActivityLog]", $appUserActivityLogToBeSaved);
    }
}
