<?php

namespace date;

class DateFactory {

    public static function createBySqlDate($sqlDate) {
        list($year, $month, $day) = explode("-", $sqlDate);
        $date = \date\Date::create($day, $month, $year);

        return $date;
    }

    public static function createByFormattedDate($formattedDate) {

        list($day, $month, $year) = explode(".", $formattedDate);
        $date = \date\Date::create($day, $month, $year);

        return $date;
    }
}
