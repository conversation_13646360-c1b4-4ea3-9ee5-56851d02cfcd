<?php

namespace date;

class HpDateComparator {

    public static function isDateInsidePeriod($date, $periodFrom, $periodTo) {
        return (self::isFirstDateGreaterThanOrEqualToSecondDate($date, $periodFrom) and
            self::isFirstDateLessThanOrEqualToSecondDate($date, $periodTo));
    }

    public static function isFirstDateGreaterThanSecondDate($firstDate, $secondDate) {
        $result = self::compareFirstDateWithSecondDate($firstDate, $secondDate);

        return ($result == \util\ResultOfTheComparison::greaterThan) ? true : false;
    }

    public static function isFirstDateGreaterThanOrEqualToSecondDate($firstDate, $secondDate) {
        $result = self::compareFirstDateWithSecondDate($firstDate, $secondDate);

        if ($result == \util\ResultOfTheComparison::equal or $result == \util\ResultOfTheComparison::greaterThan) {
            return true;
        }

        return false;
    }

    public static function isFirstDateLessThanSecondDate($firstDate, $secondDate) {
        $result = self::compareFirstDateWithSecondDate($firstDate, $secondDate);

        return ($result == \util\ResultOfTheComparison::lessThan) ? true : false;
    }

    public static function isFirstDateLessThanOrEqualToSecondDate($firstDate, $secondDate) {
        $result = self::compareFirstDateWithSecondDate($firstDate, $secondDate);

        if ($result == \util\ResultOfTheComparison::equal or $result == \util\ResultOfTheComparison::lessThan) {
            return true;
        }

        return false;
    }

    public static function isFirstDateLessThanToSecondDate($firstDate, $secondDate) {
        $result = self::compareFirstDateWithSecondDate($firstDate, $secondDate);

        if ($result == \util\ResultOfTheComparison::lessThan) {
            return true;
        }

        return false;
    }

    public static function equals($firstDate, $secondDate) {
        return (self::compareFirstDateWithSecondDate($firstDate, $secondDate) == \util\ResultOfTheComparison::equal);
    }

    private static function compareFirstDateWithSecondDate($firstDate, $secondDate) {
        $intFirstDate = \date\DateFormatter::formatToInt($firstDate);
        $intSecondDate = \date\DateFormatter::formatToInt($secondDate);

        if ($intFirstDate == $intSecondDate) {
            return \util\ResultOfTheComparison::equal;
        }

        return ($intFirstDate > $intSecondDate ? \util\ResultOfTheComparison::greaterThan : \util\ResultOfTheComparison::lessThan);
    }
}
