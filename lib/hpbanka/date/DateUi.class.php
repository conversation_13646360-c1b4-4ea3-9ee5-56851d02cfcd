<?php
namespace date;

class DateUi {

    public static function monthToArray() {
        return array(
            1 => 'leden',
            2 => 'únor',
            3 => 'březen',
            4 => 'duben',
            5 => 'květen',
            6 => 'červen',
            7 => 'červenec',
            8 => 'srpen',
            9 => 'zaří',
            10 => 'říjen',
            11 => 'listopad',
            12 => 'prosinec'
        );
    }

    public static function buildYearList($from, $to) {
        $dataYearList = array();

        for ($i = $from; $i <= $to; $i++) {
            $dataYearList[] = array('id' => intval($i), 'name' => (string)$i);
        }

        return $dataYearList;
    }

    public static function buildMonthList() {
        $dataMonthList = array();
        $nameOfMonth = self::monthToArray();

        for ($i = 1; $i <= 12; $i++) {
            $dataMonthList[] = array(
                'id' => intval($i),
                'name' => substr("0" . $i, -2) . ' (' . $nameOfMonth[$i] . ')'
            );
        }

        return $dataMonthList;
    }
}
