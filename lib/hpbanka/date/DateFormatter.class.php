<?php
namespace date;

class DateFormatter {

    public static function format($date, $format) {
        $timestamp = self::getTimestamp($date);
        $formattedDate = date($format, $timestamp);

        return $formattedDate;
    }


    public static function formatToSql($date) {
        $formattedDate = self::format($date, DateFormat::DATE_FORMAT_SQL);

        return $formattedDate;
    }


    public static function formatToInt($date) {
        return ($date->getYear() * 10000 + $date->getMonth() * 100 + $date->getDay());
    }


    public static function getTimestamp($date) {
        return mktime(0, 0, 0, $date->getMonth(), $date->getDay(), $date->getYear());
    }


    public static function formatToDateFormatAppUserWithTimeOrEmptyString($sqlDate) {
        $formattedDate = self::formatToDateFormatAppUserOrEmptyString($sqlDate);

        if (!empty($formattedDate)) {
            $formattedDate .= ", " . substr($sqlDate, 11, 5);
        }
        return $formattedDate;
    }


    public static function formatToDateFormatAppUserOrEmptyString($sqlDate) {
        if (empty($sqlDate)) {
            $formattedDate = '';
        } else {
            $date = DateFactory::createBySqlDate($sqlDate);
            $formattedDate = self::format($date, 'j.n.Y');
        }
        return $formattedDate;
    }


    public static function formatToDateFormatAppUser($date) {
        global $_loggedAppUserDateFormat;
        return self::format($date, $_loggedAppUserDateFormat);
    }


    public static function returnYearOrEmptyString($sqlDate) {
        if (is_null($sqlDate)) {
            $year = '';
        } else {
            $date = DateFactory::createBySqlDate($sqlDate);
            $year = $date->getYear();
        }
        return $year;
    }
}
