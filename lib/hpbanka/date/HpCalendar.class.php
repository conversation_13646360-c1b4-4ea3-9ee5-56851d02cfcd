<?php
namespace date;

class HpCalendar {

    public static function getToday() {
        return \date\Date::create(date('d'), date('m'), date('Y'));
    }

    public static function getYesterday() {
        return \date\Date::create(date('d') - 1, date('m'), date('Y'));
    }

    public static function getNextDay($date) {
        $dateCopy = clone $date;
        $dateCopy->addDay(1);

        return $dateCopy;
    }

    public static function getFirstDayOfCurrentMonth() {
        $date = self::getToday();
        $date->setDay(1);

        return $date;
    }

    public static function getFirstDayOfTheYear($year) {
        return \date\Date::create(1, 1, $year);
    }

    public static function getLastDayOfTheYear($year) {
        return \date\Date::create(31, 12, $year);
    }

    public static function getFirstDayOfPastMonth() {
        $date = self::getFirstDayOfCurrentMonth();
        $date->addMonth(-1);

        return $date;
    }

    public static function getLastDayOfPastMonth() {
        $date = self::getFirstDayOfCurrentMonth();
        $date->addDay(-1);

        return $date;
    }

    public static function getLastDayInMonth($date) {
        $dateCopy = clone $date;
        $dateCopy->addMonth(1);
        $dateCopy->setDay(0);

        return $dateCopy;
    }

    public static function getFirstAndLastDateOfMonth($year, $month) {
        $returnValue = new stdClass();
        $returnValue->firstDayOfMonth = \date\Date::create(1, $month, $year);
        $returnValue->lastDayOfMonth = self::getLastDayInMonth($returnValue->firstDayOfMonth);

        return $returnValue;
    }

    public static function getNumberOfNights($date1, $date2) {
        $timestamp1 = mktime(0, 0, 0, $date1->getMonth(), $date1->getDay(), $date1->getYear());
        $timestamp2 = mktime(0, 0, 0, $date2->getMonth(), $date2->getDay(), $date2->getYear());
        return abs(round(($timestamp1 - $timestamp2) / 86400));
    }

    public static function getNumberOfDays($date1, $date2) {
        $timestamp1 = mktime(0, 0, 0, $date1->getMonth(), $date1->getDay(), $date1->getYear());
        $timestamp2 = mktime(0, 0, 0, $date2->getMonth(), $date2->getDay(), $date2->getYear());
        return abs(round(($timestamp1 - $timestamp2) / 86400)) + 1;
    }

    public static function getNumberOfMonths($firstDateSql, $secondDateSql) {
        $firstDate = date_create($firstDateSql);
        $secondDate = date_create($secondDateSql);
        $dateInterval = date_diff($firstDate, $secondDate);
        return (($dateInterval->y * 12) + $dateInterval->m);
    }

    public static function getActualYear() {
        return date('Y');
    }

    public static function getPreviousMonth() {
        return date('m') - 1;
    }

    public static function getPreviousYear() {
        return date('Y') - 1;
    }

    public static function getFirstDateOfMonth($year, $month) {
        return \date\Date::create(1, $month, $year);
    }
}
