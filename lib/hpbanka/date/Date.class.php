<?php

namespace date;

class Date {

    private $day;
    private $month;
    private $year;

    private function __construct() {
        // objekty lze vytvaret pouze metodami createXX() nebo clone
    }

    public static function create($day, $month, $year) {
        $date = new Date();
        $date->setDate($day, $month, $year);

        return $date;
    }

    public static function createBySqlDate($sqlDate) {
        list($year, $month, $day) = explode("-", $sqlDate);
        return Date::create($day, $month, $year);
    }

    public function getDay() {
        return $this->day;
    }

    public function getMonth() {
        return $this->month;
    }

    public function getYear() {
        return $this->year;
    }

    public function setDate($day, $month, $year) {
        $date = mktime(0, 0, 0, intval($month), intval($day), intval($year));
        $this->day = date('d', $date);
        $this->month = date('m', $date);
        $this->year = date('Y', $date);
    }

    public function setDay($day) {
        $this->setDate($day, $this->month, $this->year);
    }

    public function addDay($count = 1) {
        $this->setDate($this->day + $count, $this->month, $this->year);
    }

    public function addNight($count = 1) {
        $count = $count - 1;
        $this->setDate($this->day + $count, $this->month, $this->year);
    }

    public function addMonth($count = 1) {
        $this->setDate($this->day, $this->month + $count, $this->year);
    }
}
