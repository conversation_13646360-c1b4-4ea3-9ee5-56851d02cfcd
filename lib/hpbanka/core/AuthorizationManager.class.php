<?php

namespace core;

class AuthorizationManager {

    private $userType;
    private $role;
    private $rights;
    private $customerId;

    public function __construct($loggedAppUser) {
        $this->userType = $loggedAppUser->userType;
        $this->role = AppUserRoleFactory::getAppUserRole($this->userType);
        $this->rights = $this->role->getRights();
        $this->customerId = $loggedAppUser->customerId;
    }

    public function getRights() {
        return $this->rights;
    }

    public function hasRightToPage($rightName) {
        return isset($this->rights[$rightName]['read']) ? $this->rights[$rightName]['read'] : false;
    }

    public function canRead($rightName) {
        return $this->hasRightToPage($rightName);
    }

    public function hasFullAccessToPage($rightName) {
        return $this->hasFullAccess($rightName);
    }

    public function hasFullAccess($rightName) {
        return isset($this->rights[$rightName]['fullAccess']) ? $this->rights[$rightName]['fullAccess'] : false;
    }

    public function hasAccessToAllPbanks() {
        return $this->userType === AppUserRoleName::FEDERACE ? true : false;
    }

    public function isTheSameCustomerAs($customerId) {
        return $this->customerId === $customerId;
    }

    public function isUser() {
        return $this->userType === AppUserRoleName::USER;
    }

    public function isCompanyReporter() {
        return $this->role->isCompanyReporter();
    }

    public function isSuperAdmin() {
        return $this->role->isSuperAdmin();
    }
}
