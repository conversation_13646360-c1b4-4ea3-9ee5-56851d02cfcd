<?php

namespace core;

class HttpUtils {

    public static function sendSuccessfulEmptyAjaxResponseAndExit() {
        header('Cache-Control: no-cache, must-revalidate');
        header('Expires: Mon, 1 Jan 1997 01:00:00 GMT');
        header('HTTP/1.1 204 No Content');
        exit;
    }

    public static function sendErrorResponseAndExit() {
        header('Cache-Control: no-cache, must-revalidate');
        header('Expires: Mon, 1 Jan 1997 01:00:00 GMT');
        header('HTTP/1.1 500 Internal Server Error');
        exit;
    }

    public static function sendSuccessfulJsonAjaxResponseAndExit($responseData) {
        header('Cache-Control: no-cache, must-revalidate');
        header('Expires: Mon, 1 Jan 1997 01:00:00 GMT');
        header('Content-Type: application/json; charset=utf8');
        echo json_encode($responseData);
        exit;
    }

    public static function redirectAndExit() {
        header('Location: index.php?error=notRights');
        exit;
    }
}
