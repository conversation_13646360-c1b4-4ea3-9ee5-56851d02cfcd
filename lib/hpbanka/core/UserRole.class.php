<?php

namespace core;

class UserRole extends Right {

    static public function getRights() {

        $rights = self::initializeRights();

        $rights[RightName::activeFormSelector] = array('fullAccess' => true, 'read' => true);
        $rights[RightName::formFiller] = array('fullAccess' => true, 'read' => true);
        $rights[RightName::sentFormList] = array('fullAccess' => true, 'read' => true);
        $rights[RightName::donationAgreementList] = array('read' => true);
        $rights[RightName::supportedPersonList] = array('fullAccess' => true, 'read' => true);

        return $rights;
    }
}
