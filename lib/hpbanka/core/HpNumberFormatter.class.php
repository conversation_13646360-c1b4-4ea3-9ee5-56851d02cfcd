<?php

namespace core;

class HpNumberFormatter {

    public static function toTwoDecimals($number) {
        return number_format(round($number, 2), 2);
    }

    public static function toMoney($number) {
        return number_format(round($number, 2), 2, ",", "");
    }

    public static function toAmount($number) {
        return number_format(round($number, 3), 3, ",", "");
    }

    public static function toAmountInCsv($number) {
        return HpNumberFormatter::replaceCommaToDotInNumber(HpNumberFormatter::toAmount($number));
    }

    public static function replaceCommaToDotInNumber($number) {
        return str_replace(',', '.', $number);
    }

    public static function toIC($number) {
        return trim(substr($number, 0, 3) . ' ' . substr($number, 3, 2) . ' ' . substr($number, 5, 3));
    }

    public static function removeGaps($string) {
        return trim(str_replace(" ", "", $string));
    }
}
