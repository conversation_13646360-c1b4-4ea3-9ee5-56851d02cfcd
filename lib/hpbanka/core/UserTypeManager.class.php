<?php

namespace core;

class UserTypeManager {

    public static function toArray() {
        $userTypeListData = array();
        $userTypeListData[] = array(
            'userTypeId' => AppUserRoleName::ADMIN,
            'name' => AppUserManager::getLocalizedUserType(AppUserRoleName::ADMIN)
        );
        $userTypeListData[] = array(
            'userTypeId' => AppUserRoleName::REPORTER,
            'name' => AppUserManager::getLocalizedUserType(AppUserRoleName::REPORTER)
        );
        $userTypeListData[] = array(
            'userTypeId' => AppUserRoleName::STOREKEEPER,
            'name' => AppUserManager::getLocalizedUserType(AppUserRoleName::STOREKEEPER)
        );
        $userTypeListData[] = array(
            'userTypeId' => AppUserRoleName::FEDERACE,
            'name' => AppUserManager::getLocalizedUserType(AppUserRoleName::FEDERACE)
        );

        return $userTypeListData;
    }

    public static function getUserTypeList($userTypeId) {
        $userTypeList = self::toArray();

        foreach ($userTypeList as $key => $userType) {
            if ($userType['userTypeId'] === AppUserRoleName::FEDERACE) {
                if ($userTypeId != AppUserRoleName::SUPERADMIN) {
                    unset($userTypeList[$key]);
                }
            }
        }
        return $userTypeList;
    }

    public static function getUserTypeName($userTypeId) {
        $userTypeList = self::toArray();

        foreach ($userTypeList as $userType) {
            if ($userType['userTypeId'] === $userTypeId) {
                return $userType['name'];
            }
        }
    }
}
