<?php

namespace core;

class AppUserRoleFactory {

    public static function getAppUserRole($userType) {

        switch ($userType) {
            case AppUserRoleName::ADMIN:
                return new AdminRole();
                break;

            case AppUserRoleName::USER:
                return new UserRole();
                break;

            case AppUserRoleName::REPORTER:
                return new ReporterRole();
                break;

            case AppUserRoleName::COMPANY_REPORTER:
                return new CompanyReporterRole();
                break;

            case AppUserRoleName::FEDERACE:
                return new FederaceRole();
                break;

            case AppUserRoleName::STOREKEEPER:
                return new StorekeeperRole();
                break;

            case AppUserRoleName::SUPERADMIN:
                return new SuperAdminRole();
                break;

            default:
                return;
                break;
        }
    }
}
