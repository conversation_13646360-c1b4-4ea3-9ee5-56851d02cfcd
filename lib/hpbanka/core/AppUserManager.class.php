<?php

namespace core;

class AppUserManager {

    const allStockSelectedId = 0;

    public static function createAppUser($appUserId) {
        $query[] = 'SELECT app.*, cus.[name], cus.[email] FROM [AppUser] as app';
        $query[] = 'LEFT JOIN [Customer] as cus ON cus.[customerId]=app.[customerId]';
        array_push($query, 'WHERE app.[appUserId] = %i', $appUserId);
        $result = \dibi::query($query);
        $appUser = $result->fetch();
        $appUser->localizedUserType = AppUserManager::getLocalizedUserType($appUser->userType);

        return $appUser;
    }

    public function getAppUserList($_loggedAppUser) {
        $foodBankId = $_loggedAppUser->foodBankId;

        $allowedUserType = array(
            AppUserRoleName::ADMIN,
            AppUserRoleName::REPORTER,
            AppUserRoleName::STOREKEEPER
        );

        if ($_loggedAppUser->userType === AppUserRoleName::SUPERADMIN) {
            $allowedUserType[] = AppUserRoleName::FEDERACE;
        }

        return \dibi::select('*')->from('[AppUser]')->where('[userType] IN (%s) AND [foodBankId] = %i', $allowedUserType, $foodBankId)->execute();
    }

    public function getAppUserByAppUserId($appUserId) {
        return \dibi::select('*')->from('[AppUser]')->where('[appUserId] = %i', $appUserId)->fetch();
    }

    public function saveAppUser($appUser) {
        $appUserId = intval($appUser['appUserId']);
        unset($appUser['appUserId']);
        $values = (array) $appUser;

        if ($appUserId > 0) {
            return \dibi::update('AppUser', $values)->where("[appUserId] = %i", $appUserId)->execute();
        } else {
            $values['currentStockId'] = $this->canReadAllStocks($values['userType']) ? self::allStockSelectedId : null;
            return \dibi::insert('AppUser', $values)->execute();
        }
    }

    private function canReadAllStocks($userType) {
        return ($userType === AppUserRoleName::ADMIN or $userType === AppUserRoleName::REPORTER);
    }

    public static function saveCurrentStockdIdByAppUser($appUserId, $currentStockId) {
        $values['currentStockId'] = $currentStockId;
        return \dibi::update('AppUser', $values)->where("[appUserId] = %i", $appUserId)->execute();
    }

    public static function saveDefaultValueCurrentStockIdForAdminUserType($stockId) {
        $values['currentStockId'] = 0;
        \dibi::update('AppUser', $values)->where("[userType] = %s AND [currentStockId] = %i", 'ADMIN', $stockId)->execute();
    }

    public static function getCurrentStockIdByAppUser($appUserId) {
        $result = \dibi::select('[currentStockId]')->from('[AppUser]')->where('[appUserId] = %i', $appUserId)->execute();
        return intval($result->fetchSingle());
    }

    public function deleteAppUser($appUserId, $foodBankId) {
        return \dibi::delete('AppUser')->where('[appUserId] = %i AND [foodBankId] = %i', $appUserId, $foodBankId)->execute();
    }

    public function deleteAppUserByCustomerId($customerId, $foodBankId) {
        return \dibi::delete('AppUser')->where('[customerId] = %i AND [foodBankId] = %i', $customerId, $foodBankId)->execute();
    }

    public function isAppUserUsed($appUserId, $foodBankId) {
        return $this->isAppUserUsedInCustomer($appUserId, $foodBankId) ||
        $this->isAppUserUsedInDonationAgreement($appUserId, $foodBankId) ||
        $this->isAppUserUsedInFilledForm($appUserId, $foodBankId) ||
        $this->isAppUserUsedInForm($appUserId, $foodBankId) ||
        $this->isAppUserUsedInStock($appUserId, $foodBankId) ||
        $this->isCustomerUsedInStockReleaseNote($appUserId, $foodBankId) ||
        $this->isAppUserUsedInSupplier($appUserId, $foodBankId);
    }

    public static function getLocalizedUserType($userType) {
        switch ($userType) {
        case AppUserRoleName::ADMIN:
            return 'administrátor';

        case AppUserRoleName::USER:
            return 'odběratel';

        case AppUserRoleName::REPORTER:
            return 'reportér';

        case AppUserRoleName::COMPANY_REPORTER:
            return 'reportér organizace';

        case AppUserRoleName::FEDERACE:
            return 'federace';

        case AppUserRoleName::STOREKEEPER:
            return 'skladník';

        case AppUserRoleName::SUPERADMIN:
            return 'superadmin';

        default:
            return;
        }
    }

    private function isAppUserUsedInCustomer($appUserId, $foodBankId) {
        $rowCount = \dibi::select('COUNT(*)')->from('Customer')->where('[appUserId] = %i AND [foodBankId] = %i', $appUserId, $foodBankId)->execute()->fetchSingle();
        return $rowCount ? true : false;
    }

    private function isAppUserUsedInDonationAgreement($appUserId, $foodBankId) {
        $rowCount = \dibi::select('COUNT(*)')->from('DonationAgreement')->where('[appUserId] = %i AND [foodBankId] = %i', $appUserId, $foodBankId)->execute()->fetchSingle();
        return $rowCount ? true : false;
    }

    private function isAppUserUsedInFilledForm($appUserId, $foodBankId) {
        $rowCount = \dibi::select('COUNT(*)')->from('FilledForm')->where('[appUserId] = %i AND [foodBankId] = %i', $appUserId, $foodBankId)->execute()->fetchSingle();
        return $rowCount ? true : false;
    }

    private function isAppUserUsedInForm($appUserId, $foodBankId) {
        $rowCount = \dibi::select('COUNT(*)')->from('Form')->where('[appUserId] = %i AND [foodBankId] = %i', $appUserId, $foodBankId)->execute()->fetchSingle();
        return $rowCount ? true : false;
    }

    private function isAppUserUsedInStock($appUserId, $foodBankId) {
        $rowCount = \dibi::select('COUNT(*)')->from('Stock')->where('[appUserId] = %i AND [foodBankId] = %i', $appUserId, $foodBankId)->execute()->fetchSingle();
        return $rowCount ? true : false;
    }

    private function isCustomerUsedInStockReleaseNote($appUserId, $foodBankId) {
        $rowCount = \dibi::select('COUNT(*)')->from('StockReleaseNote')->where('[appUserId] = %i AND [foodBankId] = %i', $appUserId, $foodBankId)->execute()->fetchSingle();
        return $rowCount ? true : false;
    }

    private function isAppUserUsedInSupplier($appUserId, $foodBankId) {
        $rowCount = \dibi::select('COUNT(*)')->from('Supplier')->where('[appUserId] = %i AND [foodBankId] = %i', $appUserId, $foodBankId)->execute()->fetchSingle();
        return $rowCount ? true : false;
    }
}
