<?php

namespace core;

class ReporterRole extends Right {

    static public function getRights() {

        $rights = self::initializeRights();

        $rights[RightName::formList] = array('fullAccess' => false, 'read' => true);
        $rights[RightName::commodityList] = array('fullAccess' => false, 'read' => true);
        $rights[RightName::customerList] = array('fullAccess' => false, 'read' => true);
        $rights[RightName::supplierList] = array('fullAccess' => false, 'read' => true);
        $rights[RightName::reportList] = array('fullAccess' => false, 'read' => true);
        $rights[RightName::donationAgreementList] = array('fullAccess' => false, 'read' => true);
        $rights[RightName::import] = array('fullAccess' => false, 'read' => false);
        $rights[RightName::incomePerStockList] = array('fullAccess' => false, 'read' => true);
        $rights[RightName::incomePerStock] = array('fullAccess' => false, 'read' => true);
        $rights[RightName::formFillerList] = array('fullAccess' => false, 'read' => true);
        $rights[RightName::receiptToStockList] = array('fullAccess' => false, 'read' => true);
        $rights[RightName::outputFromStockList] = array('fullAccess' => false, 'read' => true);
        $rights[RightName::outputFromStock] = array('fullAccess' => false, 'read' => true);
        $rights[RightName::stockReleaseNoteList] = array('fullAccess' => false, 'read' => true);
        $rights[RightName::stockList] = array('fullAccess' => false, 'read' => true);
        $rights[RightName::supply] = array('fullAccess' => false, 'read' => true);
        $rights[RightName::allStocks] = array('read' => true);

        return $rights;
    }
}
