<?php

namespace core;

class AdminRole extends Right {

    static public function getRights() {

        $rights = self::initializeRights();

        $rights[RightName::formList] = array('fullAccess' => true, 'read' => true);
        $rights[RightName::commodityList] = array('fullAccess' => false, 'read' => true);
        $rights[RightName::customerList] = array('fullAccess' => true, 'read' => true);
        $rights[RightName::supplierList] = array('fullAccess' => true, 'read' => true);
        $rights[RightName::reportList] = array('fullAccess' => true, 'read' => true);
        $rights[RightName::donationAgreement] = array('fullAccess' => true, 'read' => true);
        $rights[RightName::donationAgreementList] = array('fullAccess' => true, 'read' => true);
        $rights[RightName::import] = array('fullAccess' => true, 'read' => true);
        $rights[RightName::incomePerStockList] = array('fullAccess' => true, 'read' => true);
        $rights[RightName::incomePerStock] = array('fullAccess' => true, 'read' => true);
        $rights[RightName::formFiller] = array('fullAccess' => true, 'read' => true);
        $rights[RightName::formFillerList] = array('fullAccess' => true, 'read' => true);
        $rights[RightName::receiptToStockList] = array('fullAccess' => true, 'read' => true);
        $rights[RightName::outputFromStockList] = array('fullAccess' => true, 'read' => true);
        $rights[RightName::outputFromStock] = array('fullAccess' => true, 'read' => true);
        $rights[RightName::stockReleaseNoteList] = array('fullAccess' => true, 'read' => true);
        $rights[RightName::stockTransfersList] = array('fullAccess' => true, 'read' => true);
        $rights[RightName::stockList] = array('fullAccess' => true, 'read' => true);
        $rights[RightName::supply] = array('fullAccess' => true, 'read' => true);
        $rights[RightName::appUserList] = array('fullAccess' => true, 'read' => true);
        $rights[RightName::allStocks] = array('read' => true);
        $rights[RightName::groupOfSupportedPersonList] = array('fullAccess' => true, 'read' => true);
        $rights[RightName::supportedPersonListByCustomer] = array('fullAccess' => true, 'read' => true);
        $rights[RightName::supportedPersonList] = array('fullAccess' => true, 'read' => true);
        $rights[RightName::settingDonationAgreements] = array('fullAccess' => true, 'read' => true);
        $rights[RightName::settingReports] = array('fullAccess' => true, 'read' => true);
        $rights[RightName::manualList] = array('fullAccess' => true, 'read' => true);

        return $rights;
    }
}
