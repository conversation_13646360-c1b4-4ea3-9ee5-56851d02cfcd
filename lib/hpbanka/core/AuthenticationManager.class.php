<?php

namespace core;

class AuthenticationManager {


    public static function verify($login, $passwordHash) {

        $result = \dibi::select('*')->from('AppUser')->where("[login] = %s", $login)->execute();
        $countRows = $result->count();

        if (empty($countRows)) {
            throw \exception\ExceptionFactory::createHpAuthenticationException();
        }

        $value = $result->fetch();

        if ($value->password <> $passwordHash) {
            throw \exception\ExceptionFactory::createHpAuthenticationException();
        }

        self::registrNewAuthenticationCode($value->appUserId);

        return true;
    }


    private static function registrNewAuthenticationCode($appUserId) {
        $codeKey = rand(0, 9999);
        \dibi::query("UPDATE [AppUser] SET [codeKey]=$codeKey, [lastTime]=now() WHERE [appUserId]=$appUserId");
        $result = \dibi::select('[password]')->from('AppUser')->where("[appUserId] = %i", $appUserId)->execute();
        $value = $result->fetch();
        $authenticationCode = self::createAuthenticationCode($codeKey, $value->password);

        $_SESSION['loggedAppUserId'] = $appUserId;
        $_SESSION['authenticationCode'] = $authenticationCode;
    }


    private static function createAuthenticationCode($codeKey, $md5Password) {
        return md5($md5Password . $codeKey . $_SERVER['SERVER_ADDR']);
    }


    public static function verifyTheAuthenticity($authenticationCode, $appUserId) {

        if (empty($appUserId)) {
            header('Location: index.php?error=notCustomerId');
        }

        $result = \dibi::query("SELECT [password], [codeKey] FROM [AppUser] WHERE [appUserId]=$appUserId");
        $value = $result->fetch();

        $authenticationCodeFromDB = self::createAuthenticationCode($value->codeKey, $value->password);
        if (($authenticationCodeFromDB != $authenticationCode)) {
            self::setSessionNullAndDestroy();
            header('Location: index.php?error=notSameAuthCode');
        }

        self::registrNewAuthenticationCode($appUserId);
    }


    public static function logOut($id) {

        if (!$id) {
            return true;
        }
        $appUserId = intval($id);
        \dibi::query("UPDATE [AppUser] SET [codeKey]=0 WHERE [appUserId]=$appUserId");
        self::setSessionNullAndDestroy();
    }


    private static function setSessionNullAndDestroy() {

        $_SESSION['loggedAppUserId'] = 0;
        $_SESSION['authenticationCode'] = 0;
        session_destroy();
        return;
    }

    private static function retriveUserTypeByAppUser($appUserId) {

        if (empty($appUserId)) {
            header('Location: index.php?error=notCustomerId');
            exit;
        }

        $result = \dibi::select('[userType]')->from('AppUser')->where("[appUserId] = %i", $appUserId)->execute();
        $value = $result->fetch();
        return $value->userType;
    }
}
