<?php

namespace core;

class Right {

    public static function toArray() {
        $list = array(
            RightName::activeFormSelector,
            RightName::commodityList,
            RightName::customerList,
            RightName::formFiller,
            RightName::formList,
            RightName::reportList,
            RightName::companyReportList,
            RightName::sentFormList,
            RightName::supplierList,
            RightName::donationAgreementList,
            RightName::import,
            RightName::incomePerStockList,
            RightName::incomePerStock,
            RightName::formFillerList,
            RightName::receiptToStockList,
            RightName::outputFromStockList,
            RightName::outputFromStock,
            RightName::stockReleaseNoteList,
            RightName::stockList,
            RightName::supply,
            RightName::appUserList,
            RightName::hasAccessToAllPbanks,
            RightName::allStocks,
            RightName::supportedPersonList,
            RightName::groupOfSupportedPersonList,
            RightName::hasAccessToGroupOfSupportedPersonList,
            RightName::supportedPersonListByCustomer,
            RightName::settingDonationAgreements,
            RightName::settingFoodBank
        );

        return $list;
    }

    public static function initializeRights() {
        $rightList = self::toArray();
        $rightListData = array();

        foreach ($rightList as $right) {
            $rightListData[$right] = array('fullAccess' => false, 'read' => false);
        }

        return $rightListData;
    }

    public function isCompanyReporter() {
        return false;
    }

    public function isSuperAdmin() {
        return false;
    }
}
