<?php
namespace email;

class HpEmail {

    private $subject;
    private $content;
    private $headers;

    public function __construct($from, $cc, $bcc, $replayTo) {
        $this->initializeHeaders();
        $this->setAddress('From', $from);
        $this->setAddress('Cc', $cc);
        $this->setAddress('Bcc', $bcc);
        $this->setAddress('Replay-To', $replayTo);
    }

    public function setSubject($subject) {
        $this->subject = $this->encodeText($subject);
    }

    public function setAddress($headerType, $email) {
        if (!empty($email)) {
            $this->headers[] = $headerType . ": " . $this->encodeEmailAddress($email);
        }
    }

    public function setContent($content) {
        $this->content = $content;
    }

    public function sendTo($to) {
        return mail($this->encodeEmailAddress($to), $this->subject, $this->content, implode("\r\n", $this->headers));
    }

    private function initializeHeaders() {
        $this->headers = array();
        $this->headers[] = "MIME-Version: 1.0";
        $this->headers[] = "Content-type: text/plain; charset=utf-8";
        $this->headers[] = "X-Mailer: PHP/" . phpversion();
    }

    private function encodeText($text) {
        return "=?UTF-8?B?" . base64_encode($text) . "?=";
    }

    private function encodeEmailAddress($emailAddress) {
        $parsedEmail = explode("<", $emailAddress);
        if (count($parsedEmail) == 2) {
            $name = trim($parsedEmail[0]);
            $email = "<" . trim($parsedEmail[1]);
            return $this->encodeText($name) . $email;
        } else {
            return trim($parsedEmail[0]);
        }
    }
}
