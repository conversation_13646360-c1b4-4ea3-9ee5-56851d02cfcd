<?php

namespace util;

class AlertManager {

    private $foodBank;
    private $customerManager;
    private $loggedAppUser;
    private $supportedPersonManager;

    public function __construct($loggedAppUser) {
        $this->loggedAppUser = $loggedAppUser;
        $foodBankManager = \party\FoodBankManager::createForFoodBank($this->loggedAppUser->foodBankId);
        $this->foodBank = $foodBankManager->getFoodBankByFoodBankId();
        $this->customerManager = new \party\CustomerManager();
        $this->supportedPersonManager = \form\SupportedPersonManager::createForFoodBank($this->loggedAppUser->foodBankId);
    }

    public function getDataForAlertDanger() {
        $showFillSupportedPersonsAlert = $this->showSupportedPersonsAlert();
        $showUnprocessedDonationAgreementAlert = $this->showUnprocessedDonationAgreementAlert();

        $alertDangerInformationData = array(
            'showAlertBar' => $showFillSupportedPersonsAlert || $showUnprocessedDonationAgreementAlert,
            'showFillSupportedPersonsAlert' => $showFillSupportedPersonsAlert,
            'showUnprocessedDonationAgreementAlert' => $showUnprocessedDonationAgreementAlert
        );

        return $alertDangerInformationData;
    }

    private function showUnprocessedDonationAgreementAlert() {
        $showAlert = \dibi::query("SELECT COUNT(processed) FROM [DonationAgreement] WHERE [customerId]='" . intval($this->loggedAppUser->customerId) . "' AND processed='f'")->fetchSingle();

        return $showAlert > 0;
    }

    private function showSupportedPersonsAlert() {
        $daysToFillOutSupportedPersons = (!empty($this->foodBank->daysToFillOutSupportedPersons) ? $this->foodBank->daysToFillOutSupportedPersons : 0);
        $lastDayOfPastMonth = \date\HpCalendar::getLastDayOfPastMonth();
        $endDateForReportingSql = \date\DateFormatter::formatToSql($lastDayOfPastMonth);
        $dateForCompletion = clone $lastDayOfPastMonth;
        $dateForCompletion->addDay($daysToFillOutSupportedPersons);
        $today = \date\HpCalendar::getToday();
        $deadlineForCompletionHasExpired = \date\HpDateComparator::isFirstDateGreaterThanSecondDate($today, $dateForCompletion);

        $startDateForReporting = $this->customerManager->getAppUserStartDateForReportingByCustomerId($this->loggedAppUser->customerId);
        $startDateForReportingSql = substr($startDateForReporting, 0, 10);

        if ($deadlineForCompletionHasExpired) {
            $endDateForReportingSql = \date\DateFormatter::formatToSql($dateForCompletion);
        }

        $shouldBeRecords = \date\HpCalendar::getNumberOfMonths($startDateForReportingSql, $endDateForReportingSql);
        $isRecords = $this->supportedPersonManager->getCountOfGroupedItemsByCustomerForTheStartDate($this->loggedAppUser->customerId, $startDateForReportingSql);
        $showAlert = $isRecords < $shouldBeRecords;

        return $showAlert;
    }
}
