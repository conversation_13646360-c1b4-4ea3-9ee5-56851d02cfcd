<?php

namespace util;
use report\ReportManager;

class CSVHelper {

    private $separator;

    public function __construct() {
        $this->separator = ReportManager::CSV_COLUMN_SEPARATOR;
    }

    public function getCSVMonthList($textFirstCells = '') {
        $csvContent = '';

        $csvContent .= $this->separator . "Měsíce \n";
        $csvContent .= $textFirstCells . $this->separator;
        for ($i = 1; $i <= 12; $i++) {
            $csvContent .= "$i" . $this->separator;
        }
        $csvContent .= "suma 1-12" . "\n";
        return $csvContent;
    }

}
