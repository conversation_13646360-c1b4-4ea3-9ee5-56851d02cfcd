<?php

namespace manual;

class ManualManager {

    private $foodBankId;

    public function __construct($foodBankId) {
        $this->foodBankId = intval($foodBankId);
    }

    public static function createForFoodBank($foodBankId) {
        $manualManager = new self($foodBankId);
        return $manualManager;
    }

    /**
     * Získá manuál pro danou potravinovou banku
     * @return object|null
     */
    public function getManual() {
        $result = \dibi::select('*')
            ->from('[Manual]')
            ->where('[foodBankId] = %i', $this->foodBankId)
            ->fetch();
        
        return $result;
    }

    /**
     * Uloží nebo aktualizuje manuál
     * @param array $manualData
     * @param int $appUserId
     * @return bool
     */
    public function saveManual($manualData, $appUserId) {
        $existingManual = $this->getManual();
        
        $values = array(
            'title' => trim($manualData['title']),
            'content' => trim($manualData['content']),
            'appUserId' => intval($appUserId),
            'updatedAt' => new \DateTime()
        );

        if ($existingManual) {
            // Aktualizace existujícího manuálu
            return \dibi::update('Manual', $values)
                ->where('[foodBankId] = %i', $this->foodBankId)
                ->execute();
        } else {
            // Vytvoření nového manuálu
            $values['foodBankId'] = $this->foodBankId;
            $values['createdAt'] = new \DateTime();
            return \dibi::insert('Manual', $values)->execute();
        }
    }

    /**
     * Smaže manuál pro danou potravinovou banku
     * @return bool
     */
    public function deleteManual() {
        return \dibi::delete('Manual')
            ->where('[foodBankId] = %i', $this->foodBankId)
            ->execute();
    }

    /**
     * Zkontroluje, zda existuje manuál pro danou potravinovou banku
     * @return bool
     */
    public function hasManual() {
        $count = \dibi::select('COUNT(*)')
            ->from('[Manual]')
            ->where('[foodBankId] = %i', $this->foodBankId)
            ->fetchSingle();
        
        return $count > 0;
    }
}
