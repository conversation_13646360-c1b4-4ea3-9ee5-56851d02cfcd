<?php

namespace report;

class ReportManager {

    const CSV_COLUMN_SEPARATOR = ";";

    private $foodBankId;
    private $action;
    private $customerGroupName;
    private $exactCustomerGroupName;
    private $customerName;
    private $supplierGroupName;
    private $supplierName;
    private $commodity;
    private $filterFoodBankId;
    private $percentReduction;

    protected function __construct($foodBankId) {
        $this->foodBankId = intval($foodBankId);
    }

    public static function createForFoodBank($foodBankId) {
        $reportManager = new self($foodBankId);
        return $reportManager;
    }

    public function getStocks($to, $commodityIdList = null, $stockId = null) {
        $receivedCommodities = $this->getReceivedCommodities($to, $commodityIdList, $stockId);
        $releasedCommodities = $this->getReleasedCommodities($to, $commodityIdList, $stockId);
        $commoditiesTransferedFromStock = $this->getCommoditiesTransferedFromStock($to, $commodityIdList, $stockId);
        $commoditiesTransferedToStock = $this->getCommoditiesTransferedToStock($to, $commodityIdList, $stockId);

        $stocks = array();
        $totalSummary = $this->getInitializedTotalSummary();

        $stocks = $this->increaseCommoditySummaryWithCommodities($stocks, $totalSummary, $receivedCommodities);
        $stocks = $this->increaseCommoditySummaryWithCommodities($stocks, $totalSummary, $commoditiesTransferedToStock);
        $stocks = $this->decreaseCommoditySummaryWithCommodities($stocks, $totalSummary, $releasedCommodities);
        $stocks = $this->decreaseCommoditySummaryWithCommodities($stocks, $totalSummary, $commoditiesTransferedFromStock);

        return array(
            'commodities' => $this->getNonEmptyStocks($stocks),
            'totalValue' => $totalSummary->value,
            'totalAmount' => $totalSummary->amount
        );
    }

    public function getAvailableCommodities($to, $commodityIdList = null, $stockId = null) {
        $receivedCommodities = $this->getReceivedCommodities($to, $commodityIdList, $stockId);
        $releasedCommodities = $this->getReleasedCommodities($to, $commodityIdList, $stockId);
        $commoditiesTransferedFromStock = $this->getCommoditiesTransferedFromStock($to, $commodityIdList, $stockId);
        $commoditiesTransferedToStock = $this->getCommoditiesTransferedToStock($to, $commodityIdList, $stockId);

        $stocks = array();
        $totalSummary = $this->getInitializedTotalSummary();

        $stocks = $this->increaseCommodityPerPriceSummaryWithCommodities($stocks, $totalSummary, $receivedCommodities);
        $stocks = $this->increaseCommodityPerPriceSummaryWithCommodities($stocks, $totalSummary, $commoditiesTransferedToStock);
        $stocks = $this->decreaseCommodityPerPriceSummaryWithCommodities($stocks, $totalSummary, $releasedCommodities);
        $stocks = $this->decreaseCommodityPerPriceSummaryWithCommodities($stocks, $totalSummary, $commoditiesTransferedFromStock);

        return array(
            'commodities' => $this->getNonEmptyStocks($stocks),
            'totalValue' => $totalSummary->value,
            'totalAmount' => $totalSummary->amount
        );
    }

    private function increaseCommoditySummaryWithCommodities($stocks, $totalSummary, $commodities) {
        foreach ($commodities as $commodity) {
            $commodityKey = $commodity->commodityId;
            $stocks = $this->increaseCommoditySummary($stocks, $commodityKey, $commodity, $totalSummary);
        }

        return $stocks;
    }

    private function decreaseCommoditySummaryWithCommodities($stocks, $totalSummary, $commodities) {
        foreach ($commodities as $commodity) {
            $commodityKey = $commodity->commodityId;
            $stocks = $this->decreaseCommoditySummary($stocks, $commodityKey, $commodity, $totalSummary);
        }

        return $stocks;
    }

    private function increaseCommodityPerPriceSummaryWithCommodities($stocks, $totalSummary, $commodities) {
        foreach ($commodities as $commodity) {
            $commodityKey = $commodity->commodityId . '-' . $commodity->pricePerUnit;
            $stocks = $this->increaseCommoditySummary($stocks, $commodityKey, $commodity, $totalSummary);
        }

        return $stocks;
    }

    private function decreaseCommodityPerPriceSummaryWithCommodities($stocks, $totalSummary, $commodities) {
        foreach ($commodities as $commodity) {
            $commodityKey = $commodity->commodityId . '-' . $commodity->pricePerUnit;
            $stocks = $this->decreaseCommoditySummary($stocks, $commodityKey, $commodity, $totalSummary);
        }

        return $stocks;
    }

    private function getInitializedTotalSummary() {
        $totalSummary = new \stdClass;
        $totalSummary->value = 0;
        $totalSummary->amount = 0;

        return $totalSummary;
    }

    private function getNonEmptyStocks($stocks) {
        $nonEmptyStocks = array();

        foreach ($stocks as $commodityId => $commodity) {
            if ($commodity['amount'] != 0.0) {
                $nonEmptyStocks[$commodityId] = $commodity;
            }
        }

        return $nonEmptyStocks;
    }

    private function increaseCommoditySummary($stocks, $commodityKey, $commodity, $totalSummary) {
        if ($stocks[$commodityKey]) {
            $stocks[$commodityKey]['amount'] += $commodity->amount;
            $stocks[$commodityKey]['value'] += $commodity->value;
        } else {
            $stocks[$commodityKey] = clone $commodity;
        }

        $this->increaseTotalSummary($totalSummary, $commodity, $totalSummary);

        return $stocks;
    }

    private function decreaseCommoditySummary($stocks, $commodityKey, $commodity, $totalSummary) {
        if ($stocks[$commodityKey]) {
            $stocks[$commodityKey]['amount'] -= $commodity->amount;
            $stocks[$commodityKey]['value'] -= $commodity->value;
        } else {
            $stocks[$commodityKey] = clone $commodity;
            $stocks[$commodityKey]['amount'] = -$commodity->amount;
            $stocks[$commodityKey]['value'] = -$commodity->value;
        }

        $this->decreaseTotalSummary($totalSummary, $commodity);

        return $stocks;
    }

    private function increaseTotalSummary($total, $commodity) {
        $total->value += $commodity->value;
        $total->amount += $commodity->amount;
    }

    private function decreaseTotalSummary($total, $commodity) {
        $total->value -= $commodity->value;
        $total->amount -= $commodity->amount;
    }

    private function getCommoditiesTransferedFromStock($to, $commodityIdList, $stockId) {
        if (!$stockId) {
            return [];
        }
        $query[] = 'SELECT c.[commodityId],c.name,tc.[pricePerUnit],ROUND(SUM(tc.amount) / 1000, 2) AS amount,ROUND(SUM(tc.amount * tc.[pricePerUnit]) / 1000000, 2) AS value,c.[code],c.[unit]';
        $query[] = 'FROM [TransferCommodity] AS tc';
        array_push($query, 'JOIN [Transfer] AS t ON (t.[transferId]=tc.[transferId] AND t.[issuedAt] <= %s)', $to);
        array_push($query, ' AND t.[sourceStockId] = %i', $stockId);
        $query = $this->addCommodityJoinFilteredByIdList($query, $commodityIdList, 'tc');
        array_push($query, 'WHERE tc.[foodBankId] = %i', $this->foodBankId);
        $query[] = 'GROUP BY c.[commodityId],c.name,tc.[pricePerUnit],c.unit,c.code';
        $query[] = 'ORDER BY c.code';

        return \dibi::query($query)->fetchAll();
    }

    private function getCommoditiesTransferedToStock($to, $commodityIdList, $stockId) {
        if (!$stockId) {
            return [];
        }
        $query[] = 'SELECT c.[commodityId],c.name,tc.[pricePerUnit],ROUND(SUM(tc.amount) / 1000, 2) AS amount,ROUND(SUM(tc.amount * tc.[pricePerUnit]) / 1000000, 2) AS value,c.[code],c.[unit]';
        $query[] = 'FROM [TransferCommodity] AS tc';
        array_push($query, 'JOIN [Transfer] AS t ON (t.[transferId]=tc.[transferId] AND t.[issuedAt] <= %s)', $to);
        array_push($query, ' AND t.[targetStockId] = %i', $stockId);
        $query = $this->addCommodityJoinFilteredByIdList($query, $commodityIdList, 'tc');
        array_push($query, 'WHERE tc.[foodBankId] = %i', $this->foodBankId);
        $query[] = 'GROUP BY c.[commodityId],c.name,tc.[pricePerUnit],c.unit,c.code';
        $query[] = 'ORDER BY c.code';

        return \dibi::query($query)->fetchAll();
    }

    private function getReceivedCommodities($to, $commodityIdList, $stockId) {
        $query[] = 'SELECT c.[commodityId],c.name,ffc.[pricePerUnit],ROUND(SUM(ffc.amount) / 1000, 2) AS amount, ROUND(SUM(ffc.amount * ffc.[pricePerUnit]) / 1000000, 2) AS value, c.[code], c.[unit]';
        $query[] = 'FROM [FilledFormCommodity] AS ffc';
        array_push($query, 'JOIN [FilledForm] AS ff ON (ff.[filledFormId]=ffc.[filledFormId] AND ff.[consumptionDate] <= %s)', $to);
        if (!empty($stockId)) {
            array_push($query, ' AND ff.[stockId] = %i', $stockId);
        }
        $query = $this->addCommodityJoinFilteredByIdList($query, $commodityIdList);
        array_push($query, 'WHERE ffc.[foodBankId] = %i', $this->foodBankId);
        $query[] = 'GROUP BY c.[commodityId],c.name,ffc.[pricePerUnit],c.unit,c.code';
        $query[] = 'ORDER BY c.code';
        return \dibi::query($query)->fetchAll();
    }

    private function getReleasedCommodities($to, $commodityIdList, $stockId) {
        $query[] = 'SELECT c.[commodityId],c.name,ffc.[pricePerUnit],ROUND(SUM(ffc.amount) / 1000, 2) AS amount, ROUND(SUM(ffc.amount * ffc.[pricePerUnit]) / 1000000, 2) AS value, c.[code], c.[unit]';
        $query[] = 'FROM [StockReleaseNoteCommodity] AS ffc';
        array_push($query, 'JOIN [StockReleaseNote] AS ff ON (ff.[stockReleaseNoteId]=ffc.[stockReleaseNoteId] AND ff.[issuedAt] <= %s)', $to);
        if (!empty($stockId)) {
            array_push($query, ' AND ff.[stockId] = %i', $stockId);
        }
        $query = $this->addCommodityJoinFilteredByIdList($query, $commodityIdList);
        array_push($query, 'WHERE ffc.[foodBankId] = %i', $this->foodBankId);
        $query[] = 'GROUP BY c.[commodityId],c.name,ffc.[pricePerUnit],c.unit,c.code';

        return \dibi::query($query)->fetchAll();
    }

    private function addCommodityJoinFilteredByIdList($query, $commodityIdList, $commoditiesTableAlias = 'ffc') {
        $joinCommodity = 'JOIN [Commodity] AS c ON c.[commodityId]=' . $commoditiesTableAlias . '.[commodityId]';
        if (!empty($commodityIdList)) {
            $joinCommodity .= ' AND c.[commodityId] IN (' . implode(',', $commodityIdList) . ')';
        }
        $query[] = $joinCommodity;
        return $query;
    }

    public function getCommoditySummaryForPeriod($since, $to, $hasAccessToAllPbanks, $commodityIdIsExcludedExport, $withoutDirectConsumption, $commodityIdWithPercentReductionList) {
        $query[] = 'SELECT c.name, ROUND(SUM(CAST(ffc.amount AS numeric) / 1000), 2) AS amount, ROUND(SUM(CAST(ffc.amount * ffc.[pricePerUnit] AS numeric) / 1000000), 2) AS value, c.[code], c.[commodityId]';
        $query[] = 'FROM [FilledFormCommodity] AS ffc';
        $query[] = 'JOIN [FilledForm] AS ff ON (ff.[filledFormId]=ffc.[filledFormId])';
        if ($withoutDirectConsumption) {
            $query[] = 'LEFT JOIN [StockReleaseNote] AS srn ON srn.[filledFormId]=ff.[filledFormId]';
        }
        $query = $this->addCommodityFilterOrCommodityJoin($query);
        $query = $this->addCustomerAndCustomerGroupFilter($query);
        $query = $this->addSupplierAndSupplierGroupFilter($query);
        $query = $this->addActionFilter($query);
        $query = $this->addPeriodFilter($query, 'consumptionDate', $since, $to, 'ff');
        $query = $this->addFoodBankFilter($query, $hasAccessToAllPbanks);
        $query = $this->addFilterNotExportedCommodity($query, $commodityIdIsExcludedExport);
        if ($withoutDirectConsumption) {
            $query[] = ' AND srn.[stockStockReleaseNoteId] IS NULL';
        }
        $query[] = ' GROUP BY c.name, c.unit, c.code, c.[commodityId]';
        $query[] = 'ORDER BY c.code';

        $result = \dibi::query($query);

        $totalValue = 0;
        $totalAmount = 0;
        $totalReducedAmount = 0;
        $commodities = $result->fetchAll();

        $isPercentReductionByCommodity = empty($commodityIdWithPercentReductionList) ? false : true;

        foreach ($commodities as $key => $commodity) {
            $amount = $commodity->amount;
            if ($isPercentReductionByCommodity) {
                $this->setPercentReduction($commodityIdWithPercentReductionList[$commodity->commodityId]);
            }
            $reducedAmount = $this->getWeightAfterPercentReduce($amount);
            $commodities[$key]->reducedAmount = $reducedAmount;
            $commodities[$key]->percentReduction = $this->percentReduction;
            $commodities[$key]->print = true;
            $commodities[$key]->isEmpty = false;
            $commodities[$key]->commodityValueIsNotRange = false;
            $commodities[$key]->commodityValueIsNotNumber = false;
            $commodities[$key]->amount = $amount;
            $totalValue += $commodity->value;
            $totalAmount += $amount;
            $totalReducedAmount += $reducedAmount;
        }

        return array('commodities' => $commodities, 'totalValue' => $totalValue, 'totalAmount' => $totalAmount, 'totalReducedAmount' => $totalReducedAmount);
    }

    private function getWeightAfterPercentReduce($amount) {
        if ($this->percentReduction > 0) {
            $amount = $amount * (100 - $this->percentReduction) / 100;
        }
        return $amount;
    }

    public function getSupportedPersonsInDetailByFilterForTheYear($year, $customerId, $groupId, $hasAccessToAllPbanks) {
        return $this->getCustomerListAndGroupNameOfSupportedPersonsInDetailForTheYear($year, $customerId, $groupId, $hasAccessToAllPbanks);
    }

    private function getCustomerListAndGroupNameOfSupportedPersonsInDetailForTheYear($year, $customerId, $groupId, $hasAccessToAllPbanks) {
        $since = \date\DateFormatter::formatToSql(\date\Date::create(1, 1, $year));
        $to = \date\DateFormatter::formatToSql(\date\Date::create(31, 12, $year));

        $query = array();
        $query[] = 'SELECT cust.name AS [customer], sp.[customerId] AS [customerId], sp.[yearMonth], sp.[numberOfSupportedPersons], gsp.[groupId], gsp.[name] AS [groupName]';
        $query[] = 'FROM [SupportedPersons] AS sp';
        $query[] = 'JOIN [Customer] AS cust ON cust.[customerId]=sp.[customerId]';
        $query[] = 'JOIN [GroupOfSupportedPersons] AS gsp ON gsp.[groupId]=sp.[groupId]';
        $query = $this->addPeriodFilter($query, 'yearMonth', $since, $to, 'sp');

        if (!$hasAccessToAllPbanks) {
            $query = $this->addCustomerIdFilter($query, $customerId, 'cust');
        }

        $query = $this->addGroupIdFilter($query, $groupId, 'gsp');

        if (!$hasAccessToAllPbanks) {
            array_push($query, 'AND sp.[foodBankId] = %i', $this->foodBankId);
        }

        $query[] = 'ORDER BY [yearMonth], cust.[name] ASC';
        $list = \dibi::query($query);
        return $this->compileListOfCustomersWithNamesOfSupportedPersons($list);
    }

    private function setNumberOfSupportedPersons(&$dataByCustomer, &$dataByCustomerInTotal, $customerId, $groupId, $yearMonth, $numberOfSupportedPersons) {
        $periodDate = \date\Date::createBySqlDate($yearMonth);
        $month = intval($periodDate->getMonth());

        $dataByCustomer[$customerId][$groupId]['sumNumberOfSupportedPersons'][$month] = $numberOfSupportedPersons;
        $dataByCustomer[$customerId][$groupId]['sumNumberOfSupportedPersons']['sum'] += $numberOfSupportedPersons;
        $dataByCustomer[$customerId]['summaryData'][$month] += $numberOfSupportedPersons;
        $dataByCustomer[$customerId]['summaryData']['sum'] += $numberOfSupportedPersons;

        $dataByCustomerInTotal[$groupId]['sumNumberOfSupportedPersons'][$month] = $numberOfSupportedPersons;
        $dataByCustomerInTotal[$groupId]['sumNumberOfSupportedPersons']['sum'] += $numberOfSupportedPersons;
        $dataByCustomerInTotal['summaryData'][$month] += $numberOfSupportedPersons;
        $dataByCustomerInTotal['summaryData']['sum'] += $numberOfSupportedPersons;
    }

    private function compileListOfCustomersWithNamesOfSupportedPersons($list) {
        $dataByCustomerInTotal = array();
        $dataByCustomerInTotal['summaryData'] = $this->returnMonthListInitialized();

        $dataByCustomer = array();

        foreach ($list as $row) {
            $customerId = $row->customerId;
            $yearMonth = substr($row->yearMonth, 0, 10);

            if (!isset($dataByCustomer[$customerId])) {
                $dataByCustomer[$customerId]['summaryData'] = $this->returnMonthListInitialized();
                $dataByCustomer[$customerId]['name'] = $row->customer;
            }

            $groupId = $row->groupId;
            if (!isset($dataByCustomer[$customerId][$groupId])) {
                $dataByCustomer[$customerId][$groupId]['name'] = $row->groupName;
                $dataByCustomer[$customerId][$groupId]['sumNumberOfSupportedPersons'] = $this->returnMonthListInitialized();
            }

            if (!isset($dataByCustomerInTotal[$groupId])) {
                $dataByCustomerInTotal[$groupId]['name'] = $row->groupName;
                $dataByCustomerInTotal[$groupId]['sumNumberOfSupportedPersons'] = $this->returnMonthListInitialized();
            }

            $this->setNumberOfSupportedPersons($dataByCustomer, $dataByCustomerInTotal, $customerId, $groupId, $yearMonth, $row->numberOfSupportedPersons);

        }

        return array('customerListWithNamesOfSupportedPersons' => $dataByCustomer, 'customerListWithNamesOfSupportedPersonsInTotal' => $dataByCustomerInTotal);
    }

    public function getSupportedPersonsByFoodBankOrCustomerListForTheYear($year, $hasAccessToAllPbanks) {

        if ($hasAccessToAllPbanks) {
            return $this->getFoodBankListWithSumNumberOfSupportedPersonsForTheYear($year);
        } else {
            return $this->getCustomerListWithSumNumberOfSupportedPersonsForTheYear($year);
        }
    }

    private function getFoodBankListWithSumNumberOfSupportedPersonsForTheYear($year) {
        $since = \date\DateFormatter::formatToSql(\date\Date::create(1, 1, $year));
        $to = \date\DateFormatter::formatToSql(\date\Date::create(31, 12, $year));

        $query[] = 'SELECT fb.name AS [customerOrFoodBankName], sp.[foodBankId] AS [customerIdOrFoodBankId], sp.[yearMonth], sp.[numberOfSupportedPersons]';
        $query[] = 'FROM [SupportedPersons] AS sp';
        $query[] = 'JOIN [FoodBank] AS fb ON sp.[foodBankId]=fb.[foodBankId]';
        array_push($query, 'WHERE sp.[yearMonth] >= %s AND (sp.[yearMonth] <= %s AND sp.[yearMonth] < fb.[lockoutDateForSupportedPersons])', $since, $to);
        $query[] = 'ORDER BY [yearMonth], fb.[name] ASC';
        $list = \dibi::query($query);

        return $this->getCustomerOrFoodBankListWithSumNumberOfSupportedPersons($list);
    }

    private function getCustomerListWithSumNumberOfSupportedPersonsForTheYear($year) {
        $since = \date\DateFormatter::formatToSql(\date\Date::create(1, 1, $year));
        $to = \date\DateFormatter::formatToSql(\date\Date::create(31, 12, $year));

        $query = array();
        $query[] = 'SELECT cust.name AS [customerOrFoodBankName], sp.[customerId] AS [customerIdOrFoodBankId], sp.[yearMonth], sp.[numberOfSupportedPersons]';
        $query[] = 'FROM [SupportedPersons] AS sp';
        $query[] = 'JOIN [Customer] AS cust ON cust.[customerId]=sp.[customerId]';
        $query = $this->addCustomerGroupFilter($query);
        $query = $this->addPeriodFilter($query, 'yearMonth', $since, $to, 'sp');
        array_push($query, 'AND sp.[foodBankId] = %i', $this->foodBankId);
        $query[] = 'ORDER BY [yearMonth], cust.[name] ASC';
        $list = \dibi::query($query);
        return $this->getCustomerOrFoodBankListWithSumNumberOfSupportedPersons($list);
    }

    private function getCustomerOrFoodBankListWithSumNumberOfSupportedPersons($list) {

        $customerListByCustomerAndYearMonth = array();
        foreach ($list as $row) {
            $customerIdOrFoodBankId = $row->customerIdOrFoodBankId;
            $yearMonth = substr($row->yearMonth, 0, 10);
            $sumNumberOfSupportedPerson = intval($customerListByCustomerAndYearMonth[$customerIdOrFoodBankId][$yearMonth]['sumNumberOfSupportedPersons']) + $row->numberOfSupportedPersons;
            $customerListByCustomerAndYearMonth[$customerIdOrFoodBankId][$yearMonth] = array(
                'customerIdOrFoodBankId' => $customerIdOrFoodBankId,
                'yearMonth' => $yearMonth,
                'sumNumberOfSupportedPersons' => $sumNumberOfSupportedPerson,
                'customerName' => $row->customerOrFoodBankName
            );
        }

        $customerList = array();

        $summaryCustomerList = $this->returnMonthListInitialized();
        foreach ($customerListByCustomerAndYearMonth as $key => $values) {
            $summaryListByCustomer = $this->returnMonthListInitialized();
            foreach ($values as $value) {
                $periodDate = \date\Date::createBySqlDate($value['yearMonth']);
                $month = intval($periodDate->getMonth());
                $summaryListByCustomer[$month] = $value['sumNumberOfSupportedPersons'];
                $summaryListByCustomer['sum'] += $value['sumNumberOfSupportedPersons'];
                $summaryCustomerList[$month] += $value['sumNumberOfSupportedPersons'];
                $summaryCustomerList['sum'] += $value['sumNumberOfSupportedPersons'];

                $customerList[$key]['customerName'] = $value['customerName'];
                $customerList[$key]['sumNumberOfSupportedPersonListAfterMonth'] = $summaryListByCustomer;
            }
        }

        return array('customerList' => $customerList, 'summaryCustomerList' => $summaryCustomerList);
    }

    public function getStockReleaseNotesSummaryByCustomerForPeriod($since, $to, $hasAccessToAllPbanks) {
        $query[] = 'SELECT cust.name AS [customerName], ROUND(SUM(ffc.amount) / 1000, 2) AS amount, ROUND(SUM(ffc.amount * ffc.[pricePerUnit]) / 1000000, 2) AS value';
        $query[] = 'FROM [StockReleaseNoteCommodity] AS ffc';
        $query[] = 'JOIN [StockReleaseNote] AS ff ON ff.[stockReleaseNoteId]=ffc.[stockReleaseNoteId]';
        $query = $this->addCommodityFilterOrCommodityJoin($query);
        $query = $this->addCustomerAndCustomerGroupFilterOrJoin($query);
        $query = $this->addActionFilter($query);
        $query = $this->addPeriodFilter($query, 'issuedAt', $since, $to, 'ff');
        $query = $this->addFoodBankFilter($query, $hasAccessToAllPbanks);
        $query[] = 'GROUP BY cust.name';
        $query[] = 'ORDER BY cust.name';
        return \dibi::query($query)->fetchAll();
    }

    public function getCommoditySummaryBySupplierGroupListForPeriod($since, $to, $hasAccessToAllPbanks) {
        $query[] = 'SELECT su.[groupName], SUM(ffc.amount) / 1000 AS amount, SUM(ffc.amount * ffc.[pricePerUnit]) / 1000000 AS value';
        $query[] = 'FROM [FilledFormCommodity] AS ffc';
        $query[] = 'JOIN [FilledForm] AS ff ON (ff.[filledFormId]=ffc.[filledFormId])';
        $query = $this->addSupplierAndSupplierGroupFilterOrSupplierJoin($query);
        $query = $this->addPeriodFilter($query, 'consumptionDate', $since, $to, 'ff');
        $query = $this->addFoodBankFilter($query, $hasAccessToAllPbanks);
        $query[] = 'GROUP BY su.[groupName]';
        $query[] = 'ORDER BY su.[groupName]';

        $result = \dibi::query($query);

        $commoditySummaryBySupplierGroupList = array();
        $totalAllGroups = array('amount' => 0, 'value' => 0);

        foreach ($result as $row) {
            $commoditySummaryBySupplierGroupList[] = array(
                'name' => $row['groupName'],
                'amount' => $row['amount'],
                'value' => $row['value']
            );

            $totalAllGroups['amount'] += $row['amount'];
            $totalAllGroups['value'] += $row['value'];
        }

        return array('supplierGroupList' => $commoditySummaryBySupplierGroupList, 'totalAllGroups' => $totalAllGroups);
    }

    public function getStatementCommodityWithSupplierForMinistryOfHealth($since, $to, $xlsxExportCommodityIdAndPercentReductionList, $xlsxExportCommodityIdList) {
        $data[] = array('Číslo řádku', 'Kód', 'Datum', 'Číslo příjemky/dokladu', 'Název dárce', 'IČO dárce', 'Druh potravin', 'Čistá hmotnost potravin bez obalu (kg)', 'Hmotnost potravin včetně obalů (kg)');
        if (!empty($xlsxExportCommodityIdList)) {
            $query[] = 'SELECT c.[commodityId], ff.[supplierIC], ff.[supplierName], c.name AS [commodityName], ROUND(CAST(ffc.amount AS numeric) / 1000, 2) AS amount, ROUND(CAST(ffc.amount * ffc.[pricePerUnit] AS numeric) / 1000000, 2) AS value, ff.[stockFilledFormId], ff.[consumptionDate], fb.abbreviation AS [foodBankAbbreviation]';
            $query[] = 'FROM [FilledFormCommodity] AS ffc';
            $query[] = 'JOIN [FilledForm] AS ff ON (ff.[filledFormId]=ffc.[filledFormId])';
            $query[] = 'JOIN [FoodBank] AS fb ON fb.[foodBankId]=ff.[foodBankId]';
            $query = $this->addCommodityFilterOrCommodityJoin($query);
            $query = $this->addPeriodFilter($query, 'consumptionDate', $since, $to, 'ff');
            $query = $this->addFilterExportedCommodity($query, $xlsxExportCommodityIdList);
            $query = $this->addFoodBankFilter($query, false);
            $query[] = 'ORDER BY ff.[consumptionDate], c.[name]';
            $result = \dibi::query($query);

            $numberRow = 0;

            foreach ($result as $row) {
                $row['consumptionDate'] = date('j.n.Y', strtotime($row['consumptionDate']));
                $this->setPercentReduction($xlsxExportCommodityIdAndPercentReductionList[$row['commodityId']]);
                $row['reducedAmount'] = $this->getWeightAfterPercentReduce($row['amount']);
                $numberRow++;
                $data[] = array($numberRow, $row['foodBankAbbreviation'], $row['consumptionDate'], $row['stockFilledFormId'], $row['supplierName'], $row['supplierIC'], $row['commodityName'], number_format($row['reducedAmount'], 2, '.', ''), number_format($row['amount'], 2, '.', ''));
            }
        }
        return $data;
    }

    public function getCommoditySummaryBySupplierGroupForPeriod($since, $to, $hasAccessToAllPbanks, $groupNameAndCommodityIdWhichAreExcludedExport) {
        $query[] = 'SELECT su.[groupName], c.name AS [commodityName], SUM(ffc.amount) / 1000 AS amount, SUM(ffc.amount * ffc.[pricePerUnit]) / 1000000 AS value, c.[code], c.[commodityId], CONCAT(su.[groupName],  "_", c.[commodityId]) AS [excludedIdForExport]';
        $query[] = 'FROM [FilledFormCommodity] AS ffc';
        $query[] = 'JOIN [FilledForm] AS ff ON (ff.[filledFormId]=ffc.[filledFormId])';
        $query = $this->addCommodityFilterOrCommodityJoin($query);
        $query = $this->addSupplierAndSupplierGroupFilterOrSupplierJoin($query);
        $query = $this->addActionFilter($query);
        $query = $this->addPeriodFilter($query, 'consumptionDate', $since, $to, 'ff');
        $query = $this->addFoodBankFilter($query, $hasAccessToAllPbanks);
        $query = $this->addFilterNotExportedCommodityInSupplierGroup($query, $groupNameAndCommodityIdWhichAreExcludedExport);
        $query[] = 'GROUP BY su.[groupName], c.name, c.unit, c.code, c.[commodityId]';
        $query[] = 'ORDER BY c.code';

        $result = \dibi::query($query);

        $commoditySummaryBySupplierGroup = array();
        $totalAllGroups = array('amount' => 0, 'value' => 0);

        foreach ($result as $row) {
            $supplierGroup = $row['groupName'];
            unset($row['groupName']);

            $commoditySummaryBySupplierGroup[$supplierGroup]['supplierGroup'] = $supplierGroup;
            $row->print = true;
            $row['amount'] = $this->getWeightAfterPercentReduce($row['amount']);
            $commoditySummaryBySupplierGroup[$supplierGroup]['commodities'][] = $row;
            $commoditySummaryBySupplierGroup[$supplierGroup]['totalAmount'] += $row['amount'];
            $commoditySummaryBySupplierGroup[$supplierGroup]['totalValue'] += $row['value'];
            $totalAllGroups['amount'] += $row['amount'];
            $totalAllGroups['value'] += $row['value'];
        }

        return array('groups' => $commoditySummaryBySupplierGroup, 'totalAllGroups' => $totalAllGroups);
    }

    public function getCommoditySummaryBySupplierForPeriod($since, $to, $hasAccessToAllPbanks) {
        $query[] = 'SELECT su.[groupName], su.name AS [supplierName], SUM(ffc.amount) / 1000 AS amount, SUM(ffc.amount * ffc.[pricePerUnit]) / 1000000 AS value';
        $query[] = 'FROM [FilledFormCommodity] AS ffc';
        $query[] = 'JOIN [FilledForm] AS ff ON (ff.[filledFormId]=ffc.[filledFormId])';
        $query = $this->addCommodityFilterOrCommodityJoin($query);
        $query = $this->addSupplierAndSupplierGroupFilterOrSupplierJoin($query);
        $query = $this->addActionFilter($query);
        $query = $this->addPeriodFilter($query, 'consumptionDate', $since, $to, 'ff');
        $query = $this->addFoodBankFilter($query, $hasAccessToAllPbanks);
        $query[] = 'GROUP BY su.[groupName], su.name, c.unit';
        $query[] = 'ORDER BY su.name';

        $result = \dibi::query($query);

        $commoditySummaryBySupplier = array();
        $totalAllGroups = array('amount' => 0, 'value' => 0);

        foreach ($result as $row) {
            $supplierGroup = $row['groupName'];
            unset($row['groupName']);
            $commoditySummaryBySupplier[$supplierGroup]['supplierGroup'] = $supplierGroup;
            $commoditySummaryBySupplier[$supplierGroup]['suppliers'][] = $row;
            $commoditySummaryBySupplier[$supplierGroup]['totalAmount'] += $row['amount'];
            $commoditySummaryBySupplier[$supplierGroup]['totalValue'] += $row['value'];
            $totalAllGroups['amount'] += $row['amount'];
            $totalAllGroups['value'] += $row['value'];
        }

        return array('groups' => $commoditySummaryBySupplier, 'totalAllGroups' => $totalAllGroups);
    }

    public function getCommoditySummaryByCustomerForPeriod($since, $to, $hasAccessToAllPbanks) {
        $query[] = 'SELECT cust.name AS [customerName], c.name AS [commodityName], SUM(ffc.amount) / 1000 AS amount, SUM(ffc.amount * ffc.[pricePerUnit]) / 1000000 AS value, c.[code]';
        $query[] = 'FROM [StockReleaseNoteCommodity] AS ffc';
        $query[] = 'JOIN [StockReleaseNote] AS ff ON ff.[stockReleaseNoteId]=ffc.[stockReleaseNoteId]';
        $query = $this->addCommodityFilterOrCommodityJoin($query);
        $query = $this->addCustomerAndCustomerGroupFilterOrJoin($query);
        $query = $this->addActionFilter($query);
        $query = $this->addPeriodFilter($query, 'issuedAt', $since, $to, 'ff');
        $query = $this->addFoodBankFilter($query, $hasAccessToAllPbanks);
        $query[] = 'GROUP BY cust.name, c.name, c.unit, c.code';
        $query[] = 'ORDER BY cust.name, c.code';

        $result = \dibi::query($query);
        $commoditySummaryByCustomer = array();
        $totalAllCustomers = array('amount' => 0, 'value' => 0);

        foreach ($result as $row) {
            $customerName = $row['customerName'];
            unset($row['customerName']);
            $commoditySummaryByCustomer[$customerName]['name'] = $customerName;
            $commoditySummaryByCustomer[$customerName]['commodities'][] = $row;
            $commoditySummaryByCustomer[$customerName]['totalAmount'] += $row['amount'];
            $commoditySummaryByCustomer[$customerName]['totalValue'] += $row['value'];
            $totalAllCustomers['amount'] += $row['amount'];
            $totalAllCustomers['value'] += $row['value'];
        }

        return array('customers' => $commoditySummaryByCustomer, 'totalAllCustomers' => $totalAllCustomers);
    }

    public function getCommoditiesRemovedFromStoreSummaryForPeriod($since, $to, $hasAccessToAllPbanks) {
        $query[] = 'SELECT c.name, ROUND(SUM(ffc.amount) / 1000, 2) AS amount, ROUND(SUM(ffc.amount * ffc.[pricePerUnit]) / 1000000, 2) AS value, c.[code]';
        $query[] = 'FROM [StockReleaseNoteCommodity] AS ffc';
        $query[] = 'JOIN [StockReleaseNote] AS ff ON ff.[stockReleaseNoteId]=ffc.[stockReleaseNoteId]';
        $query = $this->addCommodityFilterOrCommodityJoin($query);
        $query = $this->addCustomerAndCustomerGroupFilter($query);
        $query = $this->addSupplierAndSupplierGroupFilter($query);
        $query = $this->addActionFilter($query);
        $query = $this->addPeriodFilter($query, 'issuedAt', $since, $to, 'ff');
        $query = $this->addFoodBankFilter($query, $hasAccessToAllPbanks);
        $query[] = 'GROUP BY c.name, c.unit, c.code';
        $query[] = 'ORDER BY c.code';

        $result = \dibi::query($query);

        $totalValue = 0;
        $totalAmount = 0;
        $commodities = $result->fetchAll();

        foreach ($commodities as $key => $commodity) {
            $commodities[$key]->print = true;
            $totalValue += $commodity->value;
            $totalAmount += $commodity->amount;
        }

        return array('commodities' => $commodities, 'totalValue' => $totalValue, 'totalAmount' => $totalAmount);
    }

    public function setActionFilter($searchString) {
        $this->action = mb_strtolower($searchString);
    }

    public function setCommodityFilter($searchString) {
        $this->commodity = mb_strtolower($searchString);
    }

    public function setCustomerGroupFilter($searchString) {
        $this->customerGroupName = mb_strtolower($searchString);
    }

    public function setExactCustomerGroupFilter($searchString) {
        $this->exactCustomerGroupName = mb_strtolower($searchString);
    }

    public function setCustomerFilter($searchString) {
        $this->customerName = mb_strtolower($searchString);
    }

    public function setSupplierGroupFilter($searchString) {
        $this->supplierGroupName = mb_strtolower($searchString);
    }

    public function setSupplierFilter($searchString) {
        $this->supplierName = mb_strtolower($searchString);
    }

    public function setFoodBankIdFilter($filterFoodBankId) {
        $this->filterFoodBankId = intval($filterFoodBankId);
    }

    public function setPercentReduction($percentReduction) {
        $this->percentReduction = intval($percentReduction);
    }

    public static function getCsvFileName($reportName, $since, $to) {
        return "PB_" . $reportName . "_" . $since . "_" . $to . "_" . date("YmdHis") . ".csv";
    }

    public static function getXlsxFileName($reportName, $since, $to) {
        return "PB_" . $reportName . "_" . $since . "_" . $to . "_" . date("YmdHis") . ".xlsx";
    }

    public static function sendCsvReport($reportContent, $filename) {
        header('Content-Description: File Transfer');
        header('Content-Type: application/CSV; charset=utf-8');
        header('Content-Disposition: attachment; filename=' . $filename);
        header('Content-Transfer-Encoding: binary');
        header('Expires: 0');
        header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
        header('Pragma: public');
        header('Content-Length: ' . strlen($reportContent));
        echo $reportContent;
    }

    private function addActionFilter($query) {
        if ($this->action) {
            array_push($query, 'JOIN [Form] AS f ON f.[formId]=ff.[formId] AND lower(f.[actionName]) LIKE %like~', $this->action);
        }
        return $query;
    }

    private function addFilterExportedCommodity($query, $commodityIdIsExport) {
        if (!empty($commodityIdIsExport)) {
            array_push($query, ' AND c.[commodityId] IN  %in', $commodityIdIsExport);
        }
        return $query;
    }
    private function addFilterNotExportedCommodity($query, $commodityIdIsExcludedExport) {
        if (!empty($commodityIdIsExcludedExport)) {
            array_push($query, ' AND c.[commodityId] NOT IN  %in', $commodityIdIsExcludedExport);
        }
        return $query;
    }

    private function addFilterNotExportedCommodityInSupplierGroup($query, $groupNameAndCommodityIdWhichAreExcludedExport) {
        if (!empty($groupNameAndCommodityIdWhichAreExcludedExport)) {
            array_push($query, ' AND CONCAT(su.[groupName],  "_", c.[commodityId]) NOT IN  %in', $groupNameAndCommodityIdWhichAreExcludedExport);
        }
        return $query;
    }

    private function addCommodityFilterOrCommodityJoin($query) {
        $query[] = 'JOIN [Commodity] AS c ON c.[commodityId]=ffc.[commodityId]';

        if ($this->commodity) {
            array_push($query, ' AND lower(c.[name]) LIKE %like~', $this->commodity);
        }

        return $query;
    }

    private function addCustomerAndCustomerGroupFilter($query) {
        if (
            empty($this->customerName) &&
            empty($this->customerGroupName) &&
            empty($this->exactCustomerGroupName)
        ) {
            return $query;
        }

        return $this->addCustomerAndCustomerGroupFilterOrJoin($query);
    }

    private function addCustomerAndCustomerGroupFilterOrJoin($query) {
        $query = $this->addCustomerJoin($query);
        $query = $this->addCustomerFilter($query);
        $query = $this->addCustomerGroupFilter($query);

        return $query;
    }

    private function addCustomerJoin($query) {
        $query[] = 'JOIN [Customer] AS cust ON cust.[customerId]=ff.[customerId]';

        return $query;
    }

    private function addCustomerFilter($query) {
        if ($this->customerName) {
            array_push($query, ' AND lower(cust.[name]) LIKE %like~', $this->customerName);
        }
        return $query;
    }

    private function addCustomerGroupFilter($query) {
        if ($this->exactCustomerGroupName) {
            array_push($query, ' AND lower(cust.[groupName]) = %s', $this->exactCustomerGroupName);
        } elseif ($this->customerGroupName) {
            array_push($query, ' AND lower(cust.[groupName]) LIKE %like~', $this->customerGroupName);
        }
        return $query;
    }

    private function addSupplierAndSupplierGroupFilter($query) {
        if (empty($this->supplierName) && empty($this->supplierGroupName)) {
            return $query;
        }
        $query = $this->addSupplierJoin($query);
        $query = $this->addSupplierFilter($query);
        $query = $this->addSupplierGroupFilter($query);

        return $query;
    }

    private function addSupplierAndSupplierGroupFilterOrSupplierJoin($query) {
        $query = $this->addSupplierJoin($query);
        if (empty($this->supplierName) && empty($this->supplierGroupName)) {
            return $query;
        }
        $query = $this->addSupplierFilter($query);
        $query = $this->addSupplierGroupFilter($query);

        return $query;
    }

    private function addSupplierAndSupplierGroupFilterOrJoin($query) {
        $query = $this->addSupplierJoin($query);
        $query = $this->addSupplierFilter($query);
        $query = $this->addSupplierGroupFilter($query);

        return $query;
    }

    private function addSupplierJoin($query) {
        $query[] = 'JOIN [Supplier] AS su ON su.[supplierId]=ff.[supplierId]';
        return $query;
    }

    private function addSupplierFilter($query) {
        if ($this->supplierName) {
            array_push($query, ' AND lower(su.[name]) LIKE %like~', $this->supplierName);
        }
        return $query;
    }

    private function addSupplierGroupFilter($query) {
        if ($this->supplierGroupName) {
            array_push($query, ' AND lower(su.[groupName]) LIKE %like~', $this->supplierGroupName);
        }
        return $query;
    }

    private function addPeriodFilter($query, $columName, $since, $to, $tableAlias) {
        array_push($query, 'WHERE ' . $tableAlias . '.[' . $columName . '] >= %s AND ' . $tableAlias . '.[' . $columName . '] <= %s', $since, $to);
        return $query;
    }

    private function addCustomerIdFilter($query, $customerId, $tableAlias) {
        if ($customerId > 0) {
            array_push($query, ' AND ' . $tableAlias . '.[customerId] = %i', $customerId);
        }
        return $query;
    }

    private function addGroupIdFilter($query, $groupId, $tableAlias) {
        if ($groupId > 0) {
            array_push($query, ' AND ' . $tableAlias . '.[groupId] = %i', $groupId);
        }
        return $query;
    }

    private function addFoodBankFilter($query, $hasAccessToAllPbanks) {
        if ($hasAccessToAllPbanks) {
            if ($this->filterFoodBankId > 0) {
                array_push($query, ' AND ff.[foodBankId] = %i', $this->filterFoodBankId);
            }
        } else {
            array_push($query, ' AND ff.[foodBankId] = %i', $this->foodBankId);
        }
        return $query;
    }

    private function returnMonthListInitialized() {
        return array(1 => 0, 2 => 0, 3 => 0, 4 => 0, 5 => 0, 6 => 0, 7 => 0, 8 => 0, 9 => 0, 10 => 0, 11 => 0, 12 => 0, "sum" => 0);
    }
}
