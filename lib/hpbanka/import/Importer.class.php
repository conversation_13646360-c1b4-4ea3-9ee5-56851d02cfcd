<?php

namespace import;

class Importer {

    private $foodBankId;

    private function __construct($foodBankId) {
        $this->foodBankId = intval($foodBankId);
    }

    public static function createForFoodBank($foodBankId) {
        return new self($foodBankId);
    }

    public function importFrom($stockId, $filepath) {
        $lines = file($filepath, FILE_IGNORE_NEW_LINES);

        if (empty($stockId) || !count($lines)) {
            return;
        }

        foreach ($lines as $line) {
            list($customerId, $created, $commodityCode, $amount) = explode(",", $line);
            if (!empty($customerId) && !empty($created) && !empty($commodityCode) && !empty($amount)) {
                $values = array(
                    "customerId" => $customerId,
                    "created" => $created,
                    "commodityCode" => $commodityCode,
                    "amount" => round($amount * 1000),
                    "stockId" => $stockId,
                    "foodBankId" => $this->foodBankId
                );
                \dibi::insert('ImportStockReleaseCommodity', $values)->execute();
            }
        }
    }

    public function getImportedData($stockId) {

        if (empty($stockId)) {
            return array();
        }

        $importedItems = \dibi::select('i.[itemId], c.[commodityId], i.[commodityCode], c.[name] AS [commodityName], i.[customerId], cc.[name] AS customer, i.[created], i.[amount]')
            ->from('[ImportStockReleaseCommodity] AS i')
            ->leftJoin('[Commodity] AS c')->on('c.[code]=i.[commodityCode]')
            ->leftJoin('[Customer] AS cc')->on('cc.[customerId]=i.[customerId]')
            ->where('i.[foodBankId] = %i AND i.[stockId] = %i', $this->foodBankId, $stockId)
            ->execute();

        foreach ($importedItems as $item) {
            $key = $item->customerId . "_" . $item->created->format('Y-m-d');
            $amount = $item->amount / 1000;

            if ($importedData[$key]) {
                $importedData[$key]['commodities'][] = array('id' => $item->commodityId, 'code' => $item->commodityCode, 'commodityName' => $item->commodityName, 'amount' => $amount);
            } else {
                $commodities = array(array('id' => $item->commodityId, 'code' => $item->commodityCode, 'commodityName' => $item->commodityName, 'amount' => $amount));
                $importedData[$key] = array(
                    'customer' => $item->customer,
                    'customerId' => $item->customerId,
                    'created' => $item->created->format('Y-m-d'),
                    'commodities' => $commodities
                );
            }
        }

        return $importedData;
    }

    public function getCommoditiesFor($stockId, $customerId, $date) {
        $sqlDate = \date\DateFormatter::formatToSql($date);

        $importedItems = \dibi::select('i.[commodityCode], c.[commodityId], i.[amount]')
            ->from('[ImportStockReleaseCommodity] AS i')
            ->leftJoin('[Commodity] AS c')->on('c.[code]=i.[commodityCode]')
            ->where('i.[foodBankId] = %i AND i.[stockId] = %i AND i.[customerId] = %i AND i.[created] = %s', $this->foodBankId, $stockId, $customerId, $sqlDate)
            ->execute();

        $commodities = array();

        foreach ($importedItems as $item) {
            if ($item->commodityId) {
                $commodities['valid'][$item->commodityId] = array('code' => $item->code, 'amount' => $item->amount / 1000);
            } else {
                $commodities['invalid'][] = array('code' => $item->code, 'amount' => $item->amount / 1000);
            }
        }

        return $commodities;
    }

    public function removeImportedDataFor($stockId, $customerId, $created) {
        $sqlDate = \date\DateFormatter::formatToSql($created);

        $deleted = \dibi::delete('ImportStockReleaseCommodity')
            ->where('[foodBankId] = %i AND [stockId] = %i AND [customerId] = %i AND [created] = %s', $this->foodBankId, $stockId, $customerId, $sqlDate)
            ->execute();

        return $deleted;
    }
}
