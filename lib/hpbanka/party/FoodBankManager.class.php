<?php
namespace party;

class FoodBankManager {

    private $foodBankId;

    public function __construct($foodBankId) {
        $this->foodBankId = intval($foodBankId);
    }

    public static function createForFoodBank($foodBankId) {
        $foodBankManager = new self($foodBankId);
        return $foodBankManager;
    }

    public function getFoodBankByFoodBankId() {
        return \dibi::select('*')->from('[FoodBank]')->where('[foodBankId] = %i', $this->foodBankId)->fetch();
    }

    public function getFoodBankList() {
        return \dibi::select('[foodBankId],[name]')->from('[FoodBank]')->fetchAll();
    }

    public function setMoreThanOneStockByCountStock($countStock) {
        $values['moreThanOneStock'] = $countStock > 1;
        \dibi::update('FoodBank', $values)->where("[foodBankId] = %i", $this->foodBankId)->execute();
    }

    public function getDaysToFillOutSupportedPersons() {
        return \dibi::select('[daysToFillOutSupportedPersons]')->from('[FoodBank]')->where("[foodBankId] = %i", $this->foodBankId)->fetch();
    }

    public function setDaysToFillOutSupportedPersons($daysToFillOutSupportedPersons) {
        $values['daysToFillOutSupportedPersons'] = intval($daysToFillOutSupportedPersons);
        \dibi::update('FoodBank', $values)->where("[foodBankId] = %i", $this->foodBankId)->execute();
    }

    public function getLockoutDateForSupportedPersons() {
        $lockoutDateForSupportedPersons = \dibi::select('[lockoutDateForSupportedPersons]')->from('[FoodBank]')->where("[foodBankId] = %i", $this->foodBankId)->fetch();
        if (!is_null($lockoutDateForSupportedPersons->lockoutDateForSupportedPersons)) {
            return $lockoutDateForSupportedPersons->lockoutDateForSupportedPersons->__toString();
        } else {
            return null;
        }
    }

    public function setLockoutDateForSupportedPersons($lockoutDateForSupportedPersons) {
        $values['lockoutDateForSupportedPersons'] = $lockoutDateForSupportedPersons;
        \dibi::update('FoodBank', $values)->where("[foodBankId] = %i", $this->foodBankId)->execute();
    }

    public function setDonationAgreementName($donationAgreementName) {
        $values['donationAgreementName'] = trim($donationAgreementName);
        \dibi::update('FoodBank', $values)->where("[foodBankId] = %i", $this->foodBankId)->execute();
    }

    public function getDonationAgreementName() {
        $donationAgreementName = \dibi::select('[donationAgreementName]')->from('[FoodBank]')->where("[foodBankId] = %i", $this->foodBankId)->fetch();
        return $donationAgreementName->donationAgreementName;
    }

    public function getFoodBankDataToSettingsByFoodBankId() {
        return \dibi::select('[name],[abbreviation],[address],[ic],[dic],[senderEmails],[stampAndSignatureName],[stampInStockRelease]')->from('[FoodBank]')->where("[foodBankId] = %i", $this->foodBankId)->fetch();
    }

    public function saveFoodBank($foodBankData) {
        $values = (array) $foodBankData;
        \dibi::update('FoodBank', $values)->where("[foodBankId] = %i", $this->foodBankId)->execute();
    }

    public function setStampAndSignatureName($stampAndSignatureName) {
        $values['stampAndSignatureName'] = trim($stampAndSignatureName);
        \dibi::update('FoodBank', $values)->where("[foodBankId] = %i", $this->foodBankId)->execute();
    }

}