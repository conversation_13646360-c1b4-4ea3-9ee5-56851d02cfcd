<?php
namespace party;

class SupplierManager {

  private $foodBankId;
  
  public function __construct($foodBankId) {
  
    $this->foodBankId = intval($foodBankId);
  }
  
  public static function createForFoodBank($foodBankId) {
  
    $supplierManager = new self($foodBankId);
    return $supplierManager;
  }
  
	public function getSupplierList() {
	  
	  return \dibi::select('*')->from('Supplier')->where('[foodBankId] = %i', $this->foodBankId)->execute();
	}
	
	public function isSupplierUsed($supplierId) {
		return $this->isSupplierUsedInForm($supplierId) || $this->isSupplierUsedInFilledForm($supplierId);
	}
	
	public function isSupplierUsedInForm($supplierId) {
	  $rowCount = \dibi::select('COUNT(*)')->from('FormSupplier')->where('[supplierId] = %i AND [foodBankId] = %i', $supplierId, $this->foodBankId)->execute()->fetchSingle();
	  return $rowCount ? true : false;
	}
	
	public function isSupplierUsedInFilledForm($supplierId) {
	  $rowCount = \dibi::select('COUNT(*)')->from('FilledForm')->where('[supplierId] = %i AND [foodBankId] = %i', $supplierId, $this->foodBankId)->execute()->fetchSingle();
	  return $rowCount ? true : false;
	}
	
	public function saveSupplier($supplier) {
	  $supplierId = intval($supplier['supplierId']);
	  unset($supplier['supplierId']);
	  $values = (array) $supplier;
	  
	  if ($supplierId > 0) {
	    return \dibi::update('Supplier', $values)->where("[supplierId] = %i", $supplierId)->execute();
	  } else {
	    $values['foodBankId'] = $this->foodBankId;
	    return \dibi::insert('Supplier', $values)->execute();
	  }
	}
	
	public function deleteSupplier($supplierId) {
	  return \dibi::delete('Supplier')->where('[supplierId] = %i AND [foodBankId] = %i', $supplierId, $this->foodBankId)->execute();
	}
	
	
	public function getSupplierListForUse($id) {
	  $formId = intval($id);
	  $foodBankId = $this->foodBankId;
	  return \dibi::select('*')->from('Supplier')->where('[foodBankId]= ' . $foodBankId . ' AND [supplierId] NOT IN (SELECT [supplierId] FROM [FormSupplier] WHERE [formId] = ' . $formId . ')')->execute();
	}
	
	public function getSupplierBySupplierId($supplierId) {
	    $foodBankId = $this->foodBankId;
	    return \dibi::select('*')->from('Supplier')->where('[supplierId] = %i AND [foodBankId] = %i', $supplierId, $this->foodBankId)->fetch();
	}

	public function getSelectedSupplierList($formId) {
	   
	  $result = \dibi::select('*')
	  ->from('[FormSupplier] AS fs')
	  ->join('[Supplier] AS sup')
	  ->on('sup.[supplierId]=fs.[supplierId]')
	  ->where("fs.[formId] = %i AND fs.[foodBankId] = %i", $formId, $this->foodBankId)
	  ->orderBy('fs.[itemOrder] ASC')
	  ->execute();
	   
	  return $result;
	}
	
	
	
}