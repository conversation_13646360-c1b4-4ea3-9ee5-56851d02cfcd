<?php
namespace party;

require_once __DIR__ . '/../../../lib/thirdparty/vendor/tecnickcom/tcpdf/examples/lang/ces.php'; // cz preklady

class HpPDF extends \TCPDF {

    public function Footer() {
        
        if (empty($this->pagegroups)) {
            $numPage = $this->getAliasNumPage();
            $NbPages = $this->getAliasNbPages(); 
            
        } else {
            $numPage = $this->getPageNumGroupAlias();
            $NbPages = $this->getPageGroupAlias();
        }
        
        $this->SetY(-10);
        $this->SetFont('freeserif', 'N', 10);
        $html = '<div style="border-top: 1px solid #000000;"> Strana  {' . $numPage . '} z {' . $NbPages . '}<span>' . PRINT_NAME . ', ' . COPY . '</span> </div>';
        $this->writeHTMLCell(0,0,'', '', $html, 0, 1, 0,  true, 'L', true);
        
    }

    public function setDefaultPDFLayout() {
        $this->SetCreator(PDF_CREATOR);
        $this->SetAuthor(PDF_AUTHOR);
        // SetMargins PDF_MARGIN_LEFT, PDF_MARGIN_TOP, PDF_MARGIN_RIGHT
        $this->SetMargins(20, 25, 20);
        $this->setHeaderMargin(10);
        $this->setFooterMargin(10);
        $this->SetFont('freeserif', '', 12, '', true);
        // PDF_MARGIN_BOTTOM
        $this->SetAutoPageBreak(TRUE, 25);
    }
}
