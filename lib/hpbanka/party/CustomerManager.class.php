<?php

namespace party;

use core\AppUserRoleName;

class CustomerManager {
    private $customerName;
    private $contactPerson;
    private $groupName;
    private $login;
    private $displayCompanyReporters = false;

    public static function createCustomer($customerId) {
        $result = \dibi::select('*')->from('[Customer]')->where('[customerId] = %i', $customerId)->execute();
        return $result->fetch();
    }

    public function getCustomerGroupName($customerId) {
        $customer = self::createCustomer($customerId);

        return $customer->groupName;
    }

    public function getListOfCustomerIdArchivedUsers($foodBankId) {
        $listOfCustomerIdArchivedUsers = array();
        $customerList = $this->getCustomerList($foodBankId);
        foreach ($customerList as $customer) {
            if ($customer->archived == 't') {
                $listOfCustomerIdArchivedUsers[$customer->customerId] = $customer->customerId;
            }
        }
        return $listOfCustomerIdArchivedUsers;
    }

    public function getCustomerList($foodBankId) {
        $query = array();
        $query[] = 'SELECT c.[customerId], c.[name], c.[contactPerson], c.[address], c.[email], c.[phone], c.[groupName], c.[IC],  c.[archived], a.[login], a.[appUserId]';
        $query[] = 'FROM [Customer] AS c';
        $query[] = 'JOIN [AppUser] AS a ON a.[customerId]=c.[customerId]';
        array_push($query, 'WHERE c.[foodBankId] = %i', $foodBankId);
        $query = $this->addCustomerNameFilter($query);
        $query = $this->addContactPersonFilter($query);
        $query = $this->addGroupNameFilter($query);
        $query = $this->addLoginFilter($query);
        $query = $this->addUserTypeFilter($query);
        array_push($query, 'ORDER BY c.[name]');

        return \dibi::query($query);
    }

    public function getSelectedCustomerList($foodBankId) {
        $query = array();
        $query[] = 'SELECT c.[customerId], c.[name], c.[email]';
        $query[] = 'FROM [Customer] AS c';
        $query[] = 'JOIN [AppUser] AS a ON a.[customerId]=c.[customerId]';
        array_push($query, 'WHERE c.[foodBankId] = %i AND c.[archived]=\'f\' ', $foodBankId);
        array_push($query, 'AND a.[userType] = %s', "USER");
        array_push($query, 'ORDER BY c.[name]');

        return \dibi::query($query);
    }

    public function getBasicCustomerListByFoodBankId($foodBankId, $addSummaryItem = true) {
        $result = $this->getSelectedCustomerList($foodBankId);

        $basicCustomerListData = array();
        if ($addSummaryItem) {
            $basicCustomerListData[] = $this->getSummaryItemCustomer();
        }
        foreach ($result as $customer) {
            $basicCustomerListData[] = array('customerId' => $customer->customerId, 'name' => $customer->name);
        }
        return $basicCustomerListData;
    }

    public function getSummaryItemCustomer() {
        return array('customerId' => 0, 'name' => 'Všichni odběratelé');
    }

    public function getCustomerByCustomerId($customerId) {
        return \dibi::select('*')->from('[Customer]')->where('[customerId] = %i', $customerId)->fetch();
    }

    public function getAppUserStartDateForReportingByCustomerId($customerId) {
        $customer = $this->getCustomerByCustomerId($customerId);

        if ($customer && !is_null($customer->startDateForReporting)) {
            return $customer->startDateForReporting->__toString();
        } else {
            return null;
        }
    }

    public function saveCustomer($customer) {
        $customerId = intval($customer['customerId']);
        unset($customer['customerId']);
        $values = (array) $customer;

        if ($customerId > 0) {
            \dibi::update('Customer', $values)->where("[customerId] = %i", $customerId)->execute();
        } else {
            $today = \date\HpCalendar::getToday();
            $values['startDateForReporting'] = \date\DateFormatter::formatToSql($today);
            $customerId = \dibi::query("INSERT INTO [Customer]", $values, " RETURNING [customerId]")->fetchSingle();
        }

        return $customerId;
    }

    public function setCustomerNameFilter($customerName) {
        $this->customerName = mb_strtolower($customerName, "UTF8");
    }

    public function setContactPersonFilter($contactPerson) {
        $this->contactPerson = mb_strtolower($contactPerson, "UTF8");
    }

    public function setGroupNameFilter($groupName) {
        $this->groupName = mb_strtolower($groupName, "UTF8");
    }

    public function setLoginFilter($login) {
        $this->login = mb_strtolower($login, "UTF8");
    }

    public function showCompanyReporters() {
        $this->displayCompanyReporters = true;
    }

    private function addCustomerNameFilter($query) {
        if ($this->customerName) {
            array_push($query, ' AND lower(c.[name]) LIKE %~like~', $this->customerName);
        }

        return $query;
    }

    private function addContactPersonFilter($query) {
        if ($this->contactPerson) {
            array_push($query, ' AND lower(c.[contactPerson]) LIKE %~like~', $this->contactPerson);
        }

        return $query;
    }

    private function addGroupNameFilter($query) {
        if ($this->groupName) {
            array_push($query, ' AND lower(c.[groupName]) LIKE %~like~', $this->groupName);
        }

        return $query;
    }

    private function addLoginFilter($query) {
        if ($this->login) {
            array_push($query, ' AND lower(a.[login]) LIKE %~like~', $this->login);
        }

        return $query;
    }

    private function addUserTypeFilter($query) {
        if (!$this->displayCompanyReporters) {
            array_push($query, ' AND a.[userType] = %s', AppUserRoleName::USER);
        }

        return $query;
    }

    public function deleteCustomer($customerId, $foodBankId) {
        return \dibi::delete('Customer')->where('[customerId] = %i AND [foodBankId] = %i', $customerId, $foodBankId)->execute();
    }

    public function isCustomerUsed($customerId, $foodBankId) {
        return $this->isCustomerUsedInDonationAgreement($customerId, $foodBankId) || $this->isCustomerUsedInStockReleaseNote($customerId, $foodBankId);
    }

    private function isCustomerUsedInDonationAgreement($customerId, $foodBankId) {
        $rowCount = \dibi::select('COUNT(*)')->from('DonationAgreement')->where('[customerId] = %i AND [foodBankId] = %i', $customerId, $foodBankId)->execute()->fetchSingle();

        return $rowCount ? true : false;
    }

    private function isCustomerUsedInStockReleaseNote($customerId, $foodBankId) {
        $rowCount = \dibi::select('COUNT(*)')->from('StockReleaseNote')->where('[customerId] = %i AND [foodBankId] = %i', $customerId, $foodBankId)->execute()->fetchSingle();

        return $rowCount ? true : false;
    }
}
