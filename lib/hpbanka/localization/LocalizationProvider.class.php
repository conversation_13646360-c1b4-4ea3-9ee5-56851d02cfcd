<?php
namespace localization;

class LocalizationProvider {

    public static function getCurrencySymbol() {
        return CURRENCY === 'EUR' ? '€' : 'Kč';
    }

    public static function getLargeLogo() {
        return LOCALE === 'sk_SK' ? 'logoPbLargeSk.jpg' : 'logoPbLargeCz.jpg';
    }

    public static function getMessagesFilename() {
        $filename = APP_BASE_DIR . '/resources/messages.' . LOCALE . '.properties';

        if (!@file_exists($filename)) {
            $filename = APP_BASE_DIR . '/resources/messages.cs_CZ.properties';
        }

        return $filename;
    }
}
