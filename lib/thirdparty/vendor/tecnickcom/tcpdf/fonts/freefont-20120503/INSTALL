                          Installing GNU FreeFont
                          =======================

GNU FreeFont can be used in any modern operating system.

This document explains how to install FreeFont on some common systems.

UNIX/GNU/Linux/BSD Systems
--------------------------

<PERSON>Font works with any system using the free font rasterizer FreeType
<http://www.freetype.org/>.  Some features such as glyph substitution and
positioning may be handled by the text layout library
Pango <http://www.pango.org/>.

Most recent systems using FreeType2 and Pango handle OpenType fonts well,
but on older systems TrueType may perform better.

* Debian GNU/Linux

Users of Debian GNU/Linux system will probably want to use the Debian package,
named 'ttf-freefont', available from the Debian Linux site.

Install the fonts by issuing the command
	apt-get install ttf-freefont


* KDE local installation

Users of KDE can install .ttf files on a per-user basis using the KDE 
Control Center module "kcmfontinst", which may appear in the menu as

	Settings -> System Administration -> Font Installer

This is especially helpful for developers and testers.


* Generic X Window systems

	1) Fetch the freefont-ttf.tar.gz package with Free UCS outline fonts
	   in the TrueType format.

	2) Unpack TrueType fonts into a suitable directory,
	   e.g. /usr/local/share/fonts/default/TrueType/

	3) If you have chosen any other directory, make sure the directory you
	   used to install the fonts is listed in the path searched by the X
	   Font Server by editing the config file in /etc/X11/.

	   In some systems, you list the directory in the item "catalogue="
	   in the file /etc/X11/fs/config.

	4) Run ttmkfdir in the directory where you unpacked the fonts.


Microsoft Windows 95/98/NT/2000/XP; Vista/7
-------------------------------------------

Note that in at least Windows 7, Vista, XP and 2000, the TrueType versions
perform much better than, and are recommended over, the OpenType ones.

For good font smoothing in Windows, Microsoft ClearType must be enabled.
The native Windows web browser must be used to install, enable, and configure
ClearType. A web search for "ClearType Tuner" will find the proper web pages.
Recent versions of the browser raise a security block (a yellow bar at the
top of the window), which you must act upon to allow installation.  A
checkbox in the window turns ClearType on (in Win-speek, "Turn on ClearType").
The change happens immediately.

* Vista, Windows 7:
	1) From the Start menu, open Control Panels
	2) Drag-n-drop font files onto Fonts control panel
           You may get a dialog saying
        	"Windows needs your permission to continue"
	   a) Click Continue

* 95/98/NT:
	The font installation is similar to Vista.

	In order to use OpenType, users of Windows 95, 98 and NT 4.0 can
	install Adobe's 'Type Manager Light', which may be obtained from
	the Adobe web site.

	Otherwise, use the TrueType versions.

Apple Mac OS X
--------------

Support for OpenType on MacOS X started with OS 10.4, and has been improved
gradually in later versions.

Installing on Mac OS X consists of moving the font files to either
	/Library/Fonts/  or  ~/Library/Fonts/
depending on whether they should be available to all users on your system
or just to your own user.

--------------------------------------------------------------------------
$Id: INSTALL,v 1.11 2011-06-12 07:14:12 Stevan_White Exp $
