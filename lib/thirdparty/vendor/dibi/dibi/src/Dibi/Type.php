<?php

/**
 * This file is part of the Dibi, smart database abstraction layer (https://dibiphp.com)
 * Copyright (c) 2005 <PERSON> (https://davidgrudl.com)
 */

declare(strict_types=1);

namespace <PERSON><PERSON>;


/**
 * Data types.
 */
class Type
{
	public const
		TEXT = 's', // as 'string'
		BINARY = 'bin',
		JSON = 'json',
		BOOL = 'b',
		INTEGER = 'i',
		FLOAT = 'f',
		DATE = 'd',
		DATETIME = 'dt',
		TIME = 't',
		TIME_INTERVAL = 'ti';


	final public function __construct()
	{
		throw new \LogicException('Cannot instantiate static class ' . self::class);
	}
}
