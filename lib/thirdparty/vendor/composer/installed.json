{"packages": [{"name": "dibi/dibi", "version": "v5.0.1", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/dg/dibi.git", "reference": "86a71dde28fd1c1b55e4c66a6f7ebfd4efb13e1d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/dg/dibi/zipball/86a71dde28fd1c1b55e4c66a6f7ebfd4efb13e1d", "reference": "86a71dde28fd1c1b55e4c66a6f7ebfd4efb13e1d", "shasum": ""}, "require": {"php": "8.0 - 8.3"}, "replace": {"dg/dibi": "*"}, "require-dev": {"jetbrains/phpstorm-attributes": "^1.0", "nette/di": "^3.1", "nette/tester": "^2.5", "phpstan/phpstan": "^1.0", "tracy/tracy": "^2.9"}, "time": "2023-11-25T13:08:47+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "5.0-dev"}}, "installation-source": "dist", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0-only", "GPL-3.0-only"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}], "description": "Dibi is Database Abstraction Library for PHP", "homepage": "https://dibiphp.com", "keywords": ["access", "database", "dbal", "mssql", "mysql", "odbc", "oracle", "pdo", "postgresql", "sqlite", "sqlsrv"], "support": {"issues": "https://github.com/dg/dibi/issues", "source": "https://github.com/dg/dibi/tree/v5.0.1"}, "install-path": "../dibi/dibi"}, {"name": "tecnickcom/tcpdf", "version": "6.6.5", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/tecnickcom/TCPDF.git", "reference": "5fce932fcee4371865314ab7f6c0d85423c5c7ce"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/tecnickcom/TCPDF/zipball/5fce932fcee4371865314ab7f6c0d85423c5c7ce", "reference": "5fce932fcee4371865314ab7f6c0d85423c5c7ce", "shasum": ""}, "require": {"php": ">=5.3.0"}, "time": "2023-09-06T15:09:26+00:00", "type": "library", "installation-source": "dist", "autoload": {"classmap": ["config", "include", "tcpdf.php", "tcpdf_parser.php", "tcpdf_import.php", "tcpdf_barcodes_1d.php", "tcpdf_barcodes_2d.php", "include/tcpdf_colors.php", "include/tcpdf_filters.php", "include/tcpdf_font_data.php", "include/tcpdf_fonts.php", "include/tcpdf_images.php", "include/tcpdf_static.php", "include/barcodes/datamatrix.php", "include/barcodes/pdf417.php", "include/barcodes/qrcode.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-3.0-or-later"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "TCPDF is a PHP class for generating PDF documents and barcodes.", "homepage": "http://www.tcpdf.org/", "keywords": ["PDFD32000-2008", "TCPDF", "barcodes", "datamatrix", "pdf", "pdf417", "qrcode"], "support": {"issues": "https://github.com/tecnickcom/TCPDF/issues", "source": "https://github.com/tecnickcom/TCPDF/tree/6.6.5"}, "funding": [{"url": "https://www.paypal.com/cgi-bin/webscr?cmd=_donations&currency_code=GBP&business=<EMAIL>&item_name=donation%20for%20tcpdf%20project", "type": "custom"}], "install-path": "../tecnickcom/tcpdf"}], "dev": true, "dev-package-names": []}