<?php
header("Content-Type: text/html; charset=utf-8");

require_once '../conf/commonApp.php';

doInit();
core\AuthenticationManager::verifyTheAuthenticity($_authenticationCode, $_loggedAppUserId);
$authorizationManager = new core\AuthorizationManager($_loggedAppUser);
if (!$authorizationManager->hasRightToPage(core\RightName::donationAgreementList)) { core\HttpUtils::redirectAndExit(); }
$rights = $authorizationManager->getRights();

$donationAgreementManager = form\DonationAgreementManager::createForFoodBank($_loggedAppUser->foodBankId);
$donationAgreementIds = array();
if (!empty($_GET['allDonationAgreementPrint'])) {
    $donationAgreementIdFrom = $_GET['donationAgreementIdFrom'];
    $donationAgreementIdTo = $_GET['donationAgreementIdTo'];
    $donationAgreementIds = $donationAgreementManager->getDonationAgreementIdsByRange($donationAgreementIdFrom, $donationAgreementIdTo);
} else {
    $donationAgreementIds[] = $_GET['donationAgreementId'];
}
$donationAgreementList = array();
if (!empty($donationAgreementIds)) {
    $donationAgreementList = $donationAgreementManager->getDonationAgreementListByDonationAgreementIds($donationAgreementIds);
}
$donationAgreementListData = array('donationAgreementList' => $donationAgreementList);
core\HttpUtils::sendSuccessfulJsonAjaxResponseAndExit($donationAgreementListData);
