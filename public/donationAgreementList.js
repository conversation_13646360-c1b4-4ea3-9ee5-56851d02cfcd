angular.module('PBanka', ['mgcrea.ngStrap', 'hpBanka', 'hpBankaMenu'])

.controller('DonationAgreementListController', function($scope, $modal, $http) {
	var confirmDeleteModal = $modal({scope: $scope, templateUrl: 'lang/cs/modalDeleteConfirm.tpl.html', title: '', content: '', show: false});

	$scope.showModal = function(donationAgreementId) {
		$scope.deleteDonationAgreementId = donationAgreementId;
		confirmDeleteModal.$promise.then(confirmDeleteModal.show);
    };

    $scope.setProcessed = function(donationAgreementId, processed) {
		$http.post('setDonationAgreementProcessingState.php', {donationAgreementId, processed});
	}
});
