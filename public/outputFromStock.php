<?php
header("Content-Type: text/html; charset=utf-8");

require_once '../conf/commonApp.php';

doInit();

core\AuthenticationManager::verifyTheAuthenticity($_authenticationCode, $_loggedAppUserId);
$authorizationManager = new core\AuthorizationManager($_loggedAppUser);

if (!$authorizationManager->hasRightToPage(core\RightName::outputFromStock)) {
    core\HttpUtils::redirectAndExit();
}

$rights = $authorizationManager->getRights();
$fullAccessToOutputFromStock = $authorizationManager->hasFullAccessToPage(core\RightName::outputFromStock);

$customerId = isset($_REQUEST['customerId']) ? intval($_REQUEST['customerId']) : 0;
$issuedAtFromImport = (isset($_REQUEST['issuedAt']) && $_REQUEST['issuedAt'])?date\Date::createBySqlDate(strval($_REQUEST['issuedAt'])) : null;
$import = isset($_REQUEST['import']) ? intval($_REQUEST['import']) : 0;
$save = isset($_POST['save']);
$customerManager = new party\CustomerManager();

if ($import) {
    $importer = import\Importer::createForFoodBank($_loggedAppUser->foodBankId);
}

if ($save and $fullAccessToOutputFromStock) {
    logger\SimpleLogger::log(date("Y-m-d H:i:s") . ": " . var_export($_POST, true)); // TODO smazat logovani, az se budou korektne vytvaret vydejky

    $outputFromStock = json_decode($_POST['outputFromStock']);
    $stockReleaseNoteManager = form\StockReleaseNote::createForFoodBank($_loggedAppUser->foodBankId);
    $formId = intval($outputFromStock->formId);
    $formManager = form\FormManager::createForFoodBank($_loggedAppUser->foodBankId);

    $foodBankManager = party\FoodBankManager::createForFoodBank($_loggedAppUser->foodBankId);
    $foodBank = $foodBankManager->getFoodBankByFoodBankId();
    $customer = $customerManager->getCustomerByCustomerId($customerId);

    $stockReleaseNoteToBeSaved = array(
        'issuedAt' => date('Y-m-d', strtotime($outputFromStock->issuedAt)),
        'customerId' => $customerId,
        'formId' => $formId,
        'stockId' => intval($_POST['stockId']), //$formManager->getStockIdByFormId($formId)
        'stockReleaseNoteId' => 0,
        'appUserId' => $_loggedAppUserId,
        'customerName' => $customer->name,
        'customerAddress' => $customer->address,
        'customerIC' => $customer->IC,
        'foodBankName' => $foodBank->name,
        'foodBankAddress' => $foodBank->address,
        'foodBankIC' => $foodBank->ic,
        'note' => trim($outputFromStock->note),
        'totalAmount' => ($outputFromStock->totalAmount * 1000)
    );

    $stockReleaseNote = $stockReleaseNoteManager->saveStockReleaseNote($stockReleaseNoteToBeSaved);
    $stockReleaseNoteId = $stockReleaseNote->stockReleaseNoteId;
    logger\SimpleLogger::log(date("Y-m-d H:i:s") . ": vydejka ID=" . $stockReleaseNoteId); // TODO smazat logovani, az se budou korektne vytvaret vydejky

    $commodityList = json_decode($_POST['commodityList']);
    $commodityToBeSaved = array();

    if (!empty($commodityList)) {

        foreach ($commodityList as $commodity) {
            $commodity = (object) $commodity;
            $amount = core\HpNumberFormatter::replaceCommaToDotInNumber($commodity->amount);
            $commodity->amount = $amount;
            $commodity->stockReleaseNoteId = $stockReleaseNoteId;
            $commodityToBeSaved[] = $commodity;
        }

        $stockReleaseNoteCommodity = form\StockReleaseNoteCommodity::createForFoodBank($_loggedAppUser->foodBankId);
        $stockReleaseNoteCommodity->saveStockReleaseNoteCommodity($commodityToBeSaved);
    }

    if ($import) {
        $importer->removeImportedDataFor($_loggedAppUser->currentStockId, $customerId, $issuedAtFromImport);
    }

    header('Location: stockReleaseNoteList.php');
    exit;
}

$t = new ui\Template("./lang/cs");
$t->set_file("tmt", "outputFromStock.tmt");
$formManager = form\FormManager::createForFoodBank($_loggedAppUser->foodBankId);
$formId = $_GET['formId'];
$form = $formManager->getForm($formId);

$importedCommodities = $import ? $importer->getCommoditiesFor($_loggedAppUser->currentStockId, $customerId, $issuedAtFromImport) : array();

$commodityManager = new form\CommodityManager();
$formCommodities = $commodityManager->getSelectedCommodityList($formId);
$commodities = $commodityManager->getCommodityList();

$commodityIds = [];

foreach ($formCommodities as $commodity) {
    $commodityIds[$commodity->commodityId] = $commodity->commodityId;
}
;

$dateTo = date('Y-m-d'); //TODO k jakemu datu se to ma kontrolovat k datu kdy realizuje nebo datumu vyskladnovani
$reportManager = report\ReportManager::createForFoodBank($_loggedAppUser->foodBankId);
$commoditySummary = $reportManager->getStocks($dateTo, $commodityIds, $_loggedAppUser->currentStockId);

foreach ($formCommodities as $commodity) {
    $commodityListData[] = array(
        'commodityId' => $commodity->commodityId,
        'name' => $commodity->name,
        'unit' => $commodity->unit,
        'amount' => isset($importedCommodities['valid'][$commodity->commodityId]) ? $importedCommodities['valid'][$commodity->commodityId]['amount'] : '',
        'availableAmount' => empty($commoditySummary['commodities'][$commodity->commodityId]->amount) ? 0 : $commoditySummary['commodities'][$commodity->commodityId]->amount,
        'pricePerUnit' => $commodity->pricePerUnit,
        'code' => $commodity->code
    );
}

$importedCommoditiesNotOnForm = array();

if (!empty($importedCommodities)) {
    foreach ($importedCommodities['valid'] as $commodityId => $commodity) {
        if ($formCommodities[$commodityId]) {
            continue;
        }

        $importedCommoditiesNotOnForm[] = array(
            'code' => $commodity['code'],
            'name' => $commodities[$commodityId]->name,
            'amount' => $commodity['amount']
        );
    }

    foreach ($importedCommodities['invalid'] as $commodity) {
        $importedCommoditiesNotOnForm[] = array(
            'code' => $commodity['code'],
            'name' => 'NENALEZENA',
            'amount' => $commodity['amount']
        );
    }
}

$stockManager = form\StockManager::createForFoodBank($_loggedAppUser->foodBankId);
$customersResult = $customerManager->getSelectedCustomerList($_loggedAppUser->foodBankId);
$issuedAt = $issuedAtFromImport ? $issuedAtFromImport : date\HpCalendar::getToday();

$outputFromStockData = array(
    'actionName' => $form->actionName,
    'formId' => $form->formId,
    'issuedAt' => date\DateFormatter::formatToSql($issuedAt),
    'nameStock' => $stockManager->getNameStockByStockId($_loggedAppUser->currentStockId)
);

$t->set_var(array(
    'commodityListData' => htmlspecialchars(json_encode($commodityListData)),
    'importedCommoditiesNotOnForm' => htmlspecialchars(json_encode($importedCommoditiesNotOnForm)),
    'customerId' => $customerId,
    'import' => $import,
    'customerListData' => htmlspecialchars(json_encode($customersResult->fetchAll())),
    'outputFromStockData' => htmlspecialchars(json_encode($outputFromStockData)),
    'currentStockId' => $_loggedAppUser->currentStockId
));

$t->set_global_var_to_template();
$t->pparse("out", "tmt");
exit;
