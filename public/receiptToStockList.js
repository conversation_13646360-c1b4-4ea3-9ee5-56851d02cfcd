angular.module('PBanka', ['mgcrea.ngStrap', 'hpBanka', 'hpBankaMenu', 'stockSelectList'])

	.controller('ReceiptToStockListController', function ($scope, $http, $timeout, $window) {

		$scope.showEdit = false;
		$scope.totalAmount = 0;
		$scope.totalGiftValue = 0;
		$scope.currencySymbol = '€';
		$scope.regNumber = /^(0|[1-9][0-9]*)((\.[0-9]*)|(\,[0-9]*))?$/;

		$scope.countAmount = function () {
			var formTotalAmount = 0.0;
			angular.forEach($scope.commodityList, function (commodity) {
				var amount = commodity.amount.toString().replace(/,/, ".");
				if (!Number.isNaN(parseFloat(amount))) {
					formTotalAmount += parseFloat(amount);
				}
			});
			$scope.formFillerList.formTotalAmount = formTotalAmount;
		};


		$scope.showFirstReceivedForm = function (filledFormId) {
			if (filledFormId > 0) {
				$scope.editReceivedForm(false, filledFormId);
			}
		};

		$scope.editReceivedForm = function (isCustomerArchived, filledFormId) {
			if (!isCustomerArchived) {
				$http.get('getReceiptToStockList.php?filledFormId=' + encodeURIComponent(filledFormId))
					.success(function (result) {
						$scope.formFillerList = result.formFillerList;
						$scope.commodityList = result.commodityList;
						$scope.supplierList = result.supplierList;

						$scope.countAmount();
						$scope.current = {supplier: $scope.formFillerList.supplierId};
					})
					.error(function (data) {
					});

				$scope.showEdit = true;
			}
		};

		$scope.showReceivedForm = function (isCustomerArchived, filledFormId) {
			$scope.editReceivedForm(isCustomerArchived, filledFormId);
		};

		$scope.canEdit = function () {
			return $scope.rightList.receiptToStockList.fullAccess;
		};

		$scope.canSave = function () {
			return $scope.formFillerForm.$valid;
		};

		$scope.countTotalItems = function () {
			var receivedFormTotalAmount = 0.0;
			var receivedFormTotalGiftValue = 0.0;
			angular.forEach($scope.receivedFormList, function (receivedForm) {
				receivedFormTotalAmount += parseFloat(receivedForm.totalAmount);
				receivedFormTotalGiftValue += parseFloat(receivedForm.giftValue);
			});

			$scope.totalAmount = receivedFormTotalAmount;
			$scope.totalGiftValue = receivedFormTotalGiftValue;
		};

		$scope.removeReceiptToStock = function (receivedForm) {

			if ($scope.rightList.receiptToStockList.fullAccess) {

				var filledFormId = receivedForm.filledFormId;
				$http.get('removeReceiptToStock.php?filledFormId=' + encodeURIComponent(filledFormId))
					.success(function (result) {

						var selectedReceivedFormList = [];
						angular.forEach($scope.receivedFormList, function (receivedForm) {
							if (receivedForm.filledFormId !== filledFormId) {
								selectedReceivedFormList.push(receivedForm);
							}
						});
						$scope.receivedFormList = selectedReceivedFormList;
						$scope.countTotalItems();
					})
					.error(function (data) {
					});
			}
		};

		$scope.saveReceiptToStock = function () {
			var request = $http({
				method: "POST",
				url: "saveReceiptToStock.php",
				data: {
					formFillerList: $scope.formFillerList,
					commodityList: $scope.commodityList,
					filterList: $scope.filterList,
					supplierId: $scope.current.supplier
				},
				headers: {'Content-Type': 'application/x-www-form-urlencoded'}
			});

			request.success(function (result) {
				$scope.receivedFormList = result.receivedFormListData;
				$scope.showEdit = false;
			}).
				error(function (data) {
				});
		};

		$scope.changeStock = function (newCurrentStock) {

			var requestChangeStock = $http({
				method: "POST",
				url: "saveCurrentStock.php",
				data: {currentStockId: newCurrentStock.stockId},
				headers: {'Content-Type': 'application/x-www-form-urlencoded'}
			});

			requestChangeStock.success(function (result) {
				var request = $http({
					method: "POST",
					url: "getReceiptToStock.php",
					data: {
						filterList: $scope.filterList
					},
					headers: {'Content-Type': 'application/x-www-form-urlencoded'}
				});

				request.success(function (result) {
					$scope.receivedFormList = result.receivedFormListData;
					$scope.showEdit = false;
				}).
					error(function (data) {
					});
			});
			requestChangeStock.error(function (data) {
			});

		}
	});