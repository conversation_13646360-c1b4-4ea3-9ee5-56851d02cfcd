body {
  background-color: #FAFCCF;
}

.cursor-move {
  cursor: move;
}

.cursor-pointer {
  cursor: pointer;
}

.show-message {
  position: absolute;
  left: 0px;
  top: 0px;
  width: 100%;
  z-index: 1;
  text-align: center;
}

.form-horizontal .control-label.text-left {
  text-align: left;
}

.form-filler-caption tr td {
  padding-bottom: 10px;
}

.sord,
.sord:hover {
  color: black;
}

.filter-height {
  height: 1.8em;
}

.text-justify {
  text-align: justify;
}

.table>thead>tr.button+tr>th {
  padding: 8px;
  line-height: 1.42857143;
  vertical-align: top;
  border-top: 0 none;
}

.modal-backdrop.am-fade {
  opacity: .5;
  transition: opacity .15s linear;
}

.modal-backdrop.am-fade.ng-enter {
  opacity: 0;
}

.modal-backdrop.am-fade.ng-enter.ng-enter-active {
  opacity: .5;
}

.modal-backdrop.am-fade.ng-leave {
  opacity: .5;
}

.modal-backdrop.am-fade.ng-leave.ng-leave-active {
  opacity: 0;
}

.my-drop-zone {
  border: dotted 3px lightgray;
  height: 13em;
}

.table>tbody>tr>td.no-border-top {
  border-top: 0px;
}

li.active a {
  font-weight: bold;
}

.infoIconDanger {
  padding-left: 10px;
  color: red;
}

.infoIconOk {
  padding-left: 10px;
  color: green;
}

.infoAction {
  padding: 15px;
}

.donation-agreement.table tr td,
.donation-agreement.table tr th {
  border-top: 2px;
}

.navbar-menu {
  margin-bottom: 0px;
}

.navbar-submenu {
  position: relative;
  min-height: 0px;
  border: 1px solid transparent;
  background-color: #E7E7E7;
}

.submenu {
  font-size: 80%;
  margin: 7.5px -15px;
}

.navbar-submenu {
  display: none;
}

.submenu-mobil {
  list-style: none;
}

.submenu-mobil>li>a {
  display: block;
  padding: 3px 20px;
  clear: both;
  font-weight: normal;
  line-height: 1.42857143;
  color: #333;
  white-space: nowrap;
}

.submenu-mobil>li>a:hover,
.submenu-mobil>li>a:focus {
  color: #262626;
  text-decoration: none;
  background-color: #f5f5f5;
}

.submenu-mobil>.active>a,
.submenu-mobil>.active>a:hover,
.submenu-mobil>.active>a:focus {
  text-decoration: none;
  font-weight: bold;
}

@media (max-width: 767px) {
  .navbar-nav .submenu-mobil {
    position: static;
    float: none;
    width: auto;
    margin-top: 0;
    background-color: transparent;
    border: 0;
    -webkit-box-shadow: none;
    box-shadow: none;
  }

  .navbar-nav .submenu-mobil>li>a,
  .navbar-nav .submenu-mobil {
    padding: 5px 15px 5px 25px;
  }

  .navbar-nav .submenu-mobil>li>a {
    line-height: 20px;
  }

  .navbar-nav .submenu-mobil>li>a:hover,
  .navbar-nav .submenu-mobil>li>a:focus {
    background-image: none;
    font-weight: bold;
  }
}

@media screen and (min-width: 768px) {
  .navbar-submenu {
    display: block;
  }

  .submenu {
    float: left;
    margin: 0;
  }

  .submenu>li {
    float: left;
  }

  .submenu>li>a {
    padding-top: 5px;
    padding-bottom: 5px;
  }

  .submenu-mobil {
    display: none;
  }
}

#logoHpBanka {
  float: left;
}

#namePBanka {
  float: right;
  line-height: 83px;
  font-size: 1.5em;
  font-weight: bold;
}

#logoPBanka {
  margin-left: 0.5em;
  float: right;
}

@media print {
  a[role="button"] {
    display: none;
  }

  .pageBreakInsideAvoid {
    page-break-inside: avoid;
  }

  div[role="logos"] {
    display: none;
  }
}