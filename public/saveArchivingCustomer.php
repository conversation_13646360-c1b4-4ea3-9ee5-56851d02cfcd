<?php
header("Content-Type: text/html; charset=utf-8");
require_once '../conf/commonApp.php';
doInit();
core\AuthenticationManager::verifyTheAuthenticity($_authenticationCode, $_loggedAppUserId);
$authorizationManager = new core\AuthorizationManager($_loggedAppUser);
$fullAccessToCustomerList = $authorizationManager->hasFullAccessToPage(core\RightName::customerList);

if ($fullAccessToCustomerList) {
    $customerId = intval($_GET['customerId']);
    $archived = $_GET['archived'];
    $values = array( 'archived' => $archived);
    \dibi::update('Customer', $values)->where('[customerId]=' . $customerId)->execute();
    core\HttpUtils::sendSuccessfulEmptyAjaxResponseAndExit();
}