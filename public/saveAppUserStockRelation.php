<?php
header("Content-Type: text/html; charset=utf-8");

require_once '../conf/commonApp.php';
doInit();
core\AuthenticationManager::verifyTheAuthenticity($_authenticationCode, $_loggedAppUserId);
$authorizationManager = new core\AuthorizationManager($_loggedAppUser);
$fullAccessToAppUserList = $authorizationManager->hasFullAccessToPage(core\RightName::appUserList);

if ($fullAccessToAppUserList) {
  $foodBankId = $_loggedAppUser->foodBankId;
  $stockId = intval($_GET['stockId']);
  $appUserId = intval($_GET['appUserId']);
  $appUserStockRelation = form\AppUserStockRelation::createForFoodBank($_loggedAppUser->foodBankId);
  $appUserStockRelation->saveStockByAppUser($stockId, $appUserId);
  
  core\HttpUtils::sendSuccessfulEmptyAjaxResponseAndExit();
}
