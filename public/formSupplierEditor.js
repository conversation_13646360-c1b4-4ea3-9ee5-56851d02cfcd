angular.module('PBanka', ['ui.sortable', 'hpBankaMenu'])

.controller('FormSupplierEditorController', function($scope, $http, $timeout) {
	
	$scope.showErrorMessage = false;  
    $scope.showSaveMessage = false;
    $scope.showRemoveMessage = false; 

	$scope.canEdit = function() {
		return $scope.rightList.formList.fullAccess;
	};
	
    $scope.sortableOption = {
    		axis: "y",
            cursor: "move", 
            handle: ".handleForSorting", 
            stop: function(e, ui) {
             // po presunuti se zde vzdy zastavi
              $scope.sortedItemId = []; // zde je aktualne serazeny seznam idecek
              itemList = $scope.selectedSuppliers;
              angular.forEach(itemList, function(item) {
                $scope.sortedItemId.push(item.supplierId);
                formId = item.formId;
              });
              orderedIdList = $scope.sortedItemId;
              
              $http.get('saveSuppliersOrder.php?orderedIdList=' + encodeURIComponent(orderedIdList) + '&formId=' + encodeURIComponent(formId))
              
              .success(function(result) {
              })
              
              .error(function(data) {
                $scope.showErrorMessage = true;
                ui.item.parent().sortable('cancel'); // pri chybe vrat puvodni razeni
              });
            }
     };
    
    
    $scope.removeSelectedSuppliers = function(supplier) {
    	
    	if ($scope.rightList.formList.fullAccess && !supplier.isUsedSupplier)  {    	
	    	var supplierId = supplier.supplierId;
	    	var formId = supplier.formId;
	    	$scope.availableSuppliers.push(supplier);
	    	var selectedSuppliers = $scope.selectedSuppliers;
	    	$scope.selectedSuppliers = [];
	    	
	    	angular.forEach(selectedSuppliers, function(selectedSupplier) {
	    		if (selectedSupplier.supplierId !== supplier.supplierId) {
	    			$scope.selectedSuppliers.push(selectedSupplier);
	    		}
	    	});
	    	
	    	$http.get('removeSupplier.php?formId=' + encodeURIComponent(formId) + '&supplierId=' + encodeURIComponent(supplierId))
	        .success(function(result) {
	        	$scope.showRemoveMessage = true;
	        	$timeout(function() {
	        		$scope.showRemoveMessage = false; 
	            }, 1500);
	        })
	        .error(function(data) {
	        });
    	}
    	
    };   
    
    
    $scope.addToSelectedSuppliers = function(supplier) {
    	
    	if ($scope.rightList.formList.fullAccess)  {        	
	    	var supplierId = supplier.supplierId;
	    	var formId = supplier.formId;
	    	$scope.selectedSuppliers.push(supplier);
	    	var availableSuppliers = $scope.availableSuppliers;
	    	$scope.availableSuppliers = [];
	    	
	    	angular.forEach(availableSuppliers, function(availableSupplier) {
	    		if (availableSupplier.supplierId !== supplier.supplierId) {
	    			$scope.availableSuppliers.push(availableSupplier);
	    		}
	    	});
	    	
	    	$http.get('saveSupplier.php?formId=' + encodeURIComponent(formId) + '&supplierId=' + encodeURIComponent(supplierId))
	        .success(function(result) {
	        	$scope.showSaveMessage = true;
	        	$timeout(function() {
	        		$scope.showSaveMessage = false; 
	            }, 1500);
	        })
	        .error(function(data) {
	        });
    	}
    };   
});


