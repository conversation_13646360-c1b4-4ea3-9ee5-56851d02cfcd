angular.module('PBanka', ['mgcrea.ngStrap', 'hpBanka', 'hpBankaMenu','stockSelectList'])

.controller('SupplyController', function($scope, $window, $http) {
	
	$scope.commoditySum = function() {
		$scope.summary = 0;
		angular.forEach($scope.commoditySummary, function(commodity) {
		      $scope.summary += commodity.value;
		    });
	}
	
	$scope.changeStock = function(newCurrentStock) {
		
		var requestChangeStock = $http({
            method: "POST",
            url: "saveCurrentStock.php",
            data: {currentStockId : newCurrentStock.stockId},
			headers : {'Content-Type': 'application/x-www-form-urlencoded'}
        });
		
        requestChangeStock.success(function(result) {
        	$http.get('getCommoditySummaryByStockId.php?stockId=' + encodeURIComponent(newCurrentStock.stockId))
            .success(function(result) {
            	$scope.commoditySummary = result.commoditySummary;
            })
            .error(function(data) {
            });
        })
        requestChangeStock.error(function(data) {
        });
	}	
	
});


