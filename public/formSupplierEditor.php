<?php
header("Content-Type: text/html; charset=utf-8");

require_once '../conf/commonApp.php';

doInit();
core\AuthenticationManager::verifyTheAuthenticity($_authenticationCode, $_loggedAppUserId);
$authorizationManager = new core\AuthorizationManager($_loggedAppUser);
if (!$authorizationManager->hasRightToPage(core\RightName::formList)) { core\HttpUtils::redirectAndExit(); }
$rights = $authorizationManager->getRights();

if (isset($_POST['backFormList'])) {
  header('Location: formList.php');
  exit;
}

$formManager = form\FormManager::createForFoodBank($_loggedAppUser->foodBankId);

$formId = $_GET['formId'];
$form = $formManager->getForm($formId);

$t = new ui\Template("./lang/cs");
$t->set_file("tmt", "formSupplierEditor.tmt");


$supplierManager = party\SupplierManager::createForFoodBank($_loggedAppUser->foodBankId);

$supplierListData = array();
$suppliers = $supplierManager->getSupplierListForUse($formId);
foreach ($suppliers as $supplier) {

  $supplierListData[] = array(
       'supplierId' => $supplier->supplierId
      ,'name' => $supplier->name
      ,'formId' => $formId
      ,'isUsedSupplier' => false
  );
};

$selectedSupplierListData = array();
$selectedSuppliers = $supplierManager->getSelectedSupplierList($formId);
$filledForm = form\FilledForm::createForFoodBank($_loggedAppUser->foodBankId);
$usedSupplierIdList = $filledForm->getUsedSupplierIdListByFormId($formId);

foreach ($selectedSuppliers as $supplier) {
  $supplierId = $supplier->supplierId;
  $selectedSupplierListData[] = array(
       'supplierId' => $supplierId
      ,'name' => $supplier->name
      ,'formId' => $formId
      ,'isUsedSupplier' => array_key_exists($supplierId, $usedSupplierIdList)
  );
}

$t->set_var(array(
     'actionName' => $form->actionName
    ,'validFrom' => date('j.n.Y', strtotime($form->validFrom))
    ,'validTo' => date('j.n.Y', strtotime($form->validTo))
    ,'availableSupplierListData' => htmlspecialchars(json_encode($supplierListData))
    ,'selectedSupplierListData' => htmlspecialchars(json_encode($selectedSupplierListData))
));

$t->set_global_var_to_template();
$t->pparse("out", "tmt");
