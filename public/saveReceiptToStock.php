<?php
header("Content-Type: text/html; charset=utf-8");

require_once '../conf/commonApp.php';

doInit();

core\AuthenticationManager::verifyTheAuthenticity($_authenticationCode, $_loggedAppUserId);
$authorizationManager = new core\AuthorizationManager($_loggedAppUser);
if (!$authorizationManager->hasRightToPage(core\RightName::receiptToStockList)) {
    core\HttpUtils::redirectAndExit();
}
$fullAccessToReceiptToStockList = $authorizationManager->hasFullAccessToPage(core\RightName::receiptToStockList);

if ($fullAccessToReceiptToStockList) {
    $postData = json_decode(file_get_contents('php://input'), true);

    //prijemce je Potravinova banka
    $foodBankManager = party\FoodBankManager::createForFoodBank($_loggedAppUser->foodBankId);
    $foodBank = $foodBankManager->getFoodBankByFoodBankId();

    //dodavatel je retezec supplier
    $supplierManager = party\SupplierManager::createForFoodBank($_loggedAppUser->foodBankId);

    $filledFormManager = form\FilledForm::createForFoodBank($_loggedAppUser->foodBankId);
    $formFillerList = (object) $postData['formFillerList'];
    $filledFormId = intval($formFillerList->filledFormId);
    $supplierId = intval($postData['supplierId']);
    $supplier = $supplierManager->getSupplierBySupplierId($supplierId);
    $filledFormToBeSaved = array(
        'consumptionDate' => $formFillerList->consumptionDate,
        'supplierId' => $supplierId,
        'giftValue' => $formFillerList->giftValue,
        'formId' => intval($formFillerList->formId),
        'stockId' => intval($formFillerList->stockId),
        'filledFormId' => $filledFormId,
        'appUserId' => $_loggedAppUserId,
        'supplierName' => $supplier->name,
        'supplierAddress' => $supplier->address,
        'supplierIC' => $supplier->IC,
        'foodBankName' => $foodBank->name,
        'foodBankAddress' => $foodBank->address,
        'foodBankIC' => $foodBank->ic,
        'note' => trim($formFillerList->note),
        'totalAmount' => $formFillerList->formTotalAmount * 1000
    );
    $filledFormManager->saveFilledForm($filledFormToBeSaved);

    $stockReleaseNote = form\StockReleaseNote::createForFoodBank($_loggedAppUser->foodBankId);
    $ids = $stockReleaseNote->getStockReleaseNoteIdsByFilledFormId($filledFormId);

    $commodityList = (object) $postData['commodityList'];
    $commoditiesToBeSaved = array();

    foreach ($commodityList as $commodity) {
        $commodity = (object) $commodity;
        if (empty($commodity->amount)) {
            continue;
        }
        $amount = core\HpNumberFormatter::replaceCommaToDotInNumber($commodity->amount);
        $commodity->amount = $amount;
        $commodity->filledFormId = $filledFormId;
        $commodity->stockReleaseNoteId = $ids->stockReleaseNoteId;
        $commoditiesToBeSaved[] = $commodity;
    }

    $filledFormCommodity = form\FilledFormCommodity::createForFoodBank($_loggedAppUser->foodBankId);
    $filledFormCommodity->removeFilledFormCommodityByFilledFormId($filledFormId);
    $filledFormCommodity->saveFilledFormCommodity($commoditiesToBeSaved);

    if ($ids->stockReleaseNoteId) {
        $stockReleaseNoteCommodity = form\StockReleaseNoteCommodity::createForFoodBank($_loggedAppUser->foodBankId);
        $stockReleaseNoteCommodity->removeStockReleaseNoteCommodityByStockReleaseNoteId($ids->stockReleaseNoteId);
        $stockReleaseNoteCommodity->saveStockReleaseNoteCommodity($commoditiesToBeSaved);

        $stockReleaseNote = form\StockReleaseNote::createForFoodBank($_loggedAppUser->foodBankId);
        $stockReleaseNote->updateFilledFormNote(trim($formFillerList->note), $ids->stockReleaseNoteId);
        $stockReleaseNote->updateTotalAmount($formFillerList->formTotalAmount * 1000, $ids->stockReleaseNoteId);
    }

    $filterList = $postData['filterList'];
    $filledFormManager->setStockByAppUser($_loggedAppUserId);
    $filledFormManager->setSupplierNameFilter($filterList['supplier']);
    $filledFormManager->setDateFromFilter($filterList['dateFrom']);
    $filledFormManager->setDateToFilter($filterList['dateTo']);
    $receivedFormListData = $filledFormManager->getReceivedFormList();

    $result = array('receivedFormListData' => $receivedFormListData);
    core\HttpUtils::sendSuccessfulJsonAjaxResponseAndExit($result);
}
