angular.module('PBanka', ['hpBankaMenu', 'stockSelectList'])

    .controller('IncomePerStockListController', function ($scope, $http, $window) {

        $scope.changeStock = function (newCurrentStock) {

            var requestChangeStock = $http({
                method: "POST",
                url: "saveCurrentStock.php",
                data: {currentStockId: newCurrentStock.stockId},
                headers: {'Content-Type': 'application/x-www-form-urlencoded'}
            });

            requestChangeStock.success(function (result) {
            });
            requestChangeStock.error(function (data) {
            });
        };

        $scope.canFill = function () {
            return ($scope.currentStock.stockId > 0);
        };
    });
