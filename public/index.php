<?php
// př<PERSON>lašovací stránka pro odběratele umožňující zároveň obnovení zapomenutého hesla
// přihlášení pro potravinovou banku
header("Content-Type: text/html; charset=utf-8");

require_once '../conf/commonApp.php';

doInit();

core\AuthenticationManager::logOut($_SESSION['loggedAppUserId']);

if (isset($_POST['enterToApp'])) {
    core\AuthenticationManager::verify($_POST['login'], $_POST['passwordHash']);

    if (isset($_SESSION['loggedAppUserId']) and ($_SESSION['loggedAppUserId'] > 0)) {
        $_loggedAppUserId = $_SESSION['loggedAppUserId'];
        $_loggedAppUser = core\AppUserManager::createAppUser($_loggedAppUserId);

        if (
            $_loggedAppUser->userType === core\AppUserRoleName::ADMIN or
            $_loggedAppUser->userType === core\AppUserRoleName::SUPERADMIN
        ) {
            // pbanka
            header('Location: outputFromStockList.php');
            exit;
        } elseif (
            $_loggedAppUser->userType === core\AppUserRoleName::REPORTER or
            $_loggedAppUser->userType === core\AppUserRoleName::COMPANY_REPORTER or
            $_loggedAppUser->userType === core\AppUserRoleName::FEDERACE
        ) {
            // reporter
            header('Location: reports/reportList.php');
            exit;
        } elseif (
            $_loggedAppUser->userType === core\AppUserRoleName::STOREKEEPER
        ) {
            // skladnik
            header('Location: incomePerStockList.php');
            exit;
        } else {
            // customer
            $customer = \party\CustomerManager::createCustomer($_loggedAppUser->customerId);

            if ($customer->archived === true) {
                throw \exception\ExceptionFactory::createHpAuthenticationException();
            }

            header('Location: activeFormSelector.php');
            exit;
        }
    }
}

$t = new ui\Template("./lang/cs");

if (isset($_GET['authenticationError'])) {
    $t->set_var('errorLogin', 1); // zobrazi hlasku o chybnem prihlaseni
}

$applicationSettings = \dibi::select('[databaseVersion]')->from('[ApplicationSettings]')->where('[applicationSettingsId] = %i',  1)->fetch();
$t->set_var(array('databaseVersion' => $applicationSettings['databaseVersion']));

$t->set_file("tmt", "index.tmt");

$t->set_global_var_to_template();
$t->pparse("out", "tmt");
