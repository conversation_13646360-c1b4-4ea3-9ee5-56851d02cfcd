<?php

use ui\Visibility;

require_once '../conf/commonApp.php';

require_once '../lib/thirdparty/vendor/autoload.php';

doInit();
core\AuthenticationManager::verifyTheAuthenticity($_authenticationCode, $_loggedAppUserId);
$authorizationManager = new core\AuthorizationManager($_loggedAppUser);
if (!$authorizationManager->hasRightToPage(core\RightName::donationAgreementList)) {
    core\HttpUtils::redirectAndExit();
}
$donationAgreementManager = form\DonationAgreementManager::createForFoodBank($_loggedAppUser->foodBankId);
$donationAgreementIds = array();
if (!empty($_GET['allDonationAgreementPrint'])) {
    $donationAgreementIdFrom = $_GET['donationAgreementIdFrom'];
    $donationAgreementIdTo = $_GET['donationAgreementIdTo'];
    $donationAgreementIds = $donationAgreementManager->getDonationAgreementIdsByRange($donationAgreementIdFrom, $donationAgreementIdTo);
} else {
    $donationAgreementIds[] = $_GET['donationAgreementId'];
}
$donationAgreementList = array();
if (!empty($donationAgreementIds)) {
    $donationAgreementList = $donationAgreementManager->getDonationAgreementListByDonationAgreementIds($donationAgreementIds);
}

$t = new ui\Template("./lang/cs");
$t->set_file("tmt", "donationAgreement2PDF.tmt");
$t->set_block("tmt", "stockReleaseNote", "{tmtstockReleaseNote}");
$t->set_block("tmt", "stockReleaseNotes", "{tmtstockReleaseNotes}");
$t->set_block("tmt", "donationAgreement", "{tmtdonationAgreement}");
$t->set_block("tmt", "donationAgreements", "{tmtdonationAgreements}");

foreach ($donationAgreementList as $donationAgreement) {

    $donationAgreement = (object) $donationAgreement;

    if ($authorizationManager->isUser() && !$authorizationManager->isTheSameCustomerAs($donationAgreement->customerId)) {
        continue;
    }

    $t->set_var(array(
        'foodBankDonationAgreementId' => $donationAgreement->foodBankDonationAgreementId,
        'donationAgreementName' => $donationAgreement->donationAgreementName,
        'donorName' => $donationAgreement->donorName,
        'donorAddress' => $donationAgreement->donorAddress,
        'donorIc' => $donationAgreement->donorIc,
        'doneeName' => $donationAgreement->doneeName,
        'doneeAddress' => $donationAgreement->doneeAddress,
        'doneeIc' => $donationAgreement->doneeIC,
        'dateFrom' => date\DateFormatter::formatToDateFormatAppUserOrEmptyString($donationAgreement->dateFrom),
        'dateTo' => date\DateFormatter::formatToDateFormatAppUserOrEmptyString($donationAgreement->dateTo),
        'totalCalculatedValue' => core\HpNumberFormatter::toMoney($donationAgreement->totalCalculatedValue),
        'totalAmount' => core\HpNumberFormatter::toAmount($donationAgreement->totalAmount)
    ));

    $addRow = false;
    foreach ($donationAgreement->stockReleaseNotes as $stockReleaseNote) {
        $t->set_var(array(
            'stockStockReleaseNoteId' => $stockReleaseNote['stockStockReleaseNoteId'],
            'issuedAt' => date\DateFormatter::formatToDateFormatAppUserOrEmptyString($stockReleaseNote['issuedAt']),
            'actionName' => $stockReleaseNote['actionName'],
            'stockFilledFormId' => $stockReleaseNote['stockFilledFormId'] ? $stockReleaseNote['stockFilledFormId'] : '--',
            'amount' => core\HpNumberFormatter::toAmount($stockReleaseNote['amount']),
            'calculatedValue' => core\HpNumberFormatter::toMoney($stockReleaseNote['calculatedValue'])
        ));
        $t->parse("tmtstockReleaseNotes", "stockReleaseNote", $addRow);
        $addRow = true;
    }
    $t->parse("tmtdonationAgreements", "donationAgreement", true);

}

$t->set_var(array(
    'stampAndSignaturePath' => STAMP_AND_SIGNATURE_PATH,
    'stampAndSignatureName' => $_foodBank->stampAndSignatureName,
    'showStampAndSignature' => !empty(trim($_foodBank->stampAndSignatureName)) ? Visibility::show : Visibility::hide
));

$t->set_global_var_to_template();
$donationAgreementInHTML = $t->finish($t->parse("out", "tmt"));

$pdf = new party\DonationAgreement2PDF(PDF_PAGE_ORIENTATION, PDF_UNIT, PDF_PAGE_FORMAT, true, 'UTF-8', false);
$pdf->setLanguageArray($l);
$pdf->setHeaderData($_foodBank->logoName);
$pdf->setPrintHeader(true);
$pdf->setDefaultPDFLayout();

$html = '<html><head></head><body><style>' . file_get_contents(__DIR__ . '/style/print2PDF.css') . '</style>';
$html .= $donationAgreementInHTML;
$pdf->writeHTML($html, true, false, true, false);

if (!empty($_GET['allDonationAgreementPrint'])) {
    $date = new DateTime();
    $pdfFilename = 'darovaci_smlouvy_' . $date->format('Y-m-d H:i:s') . '.pdf';
} else {
    $donationAgreementNumber = str_pad($donationAgreement->foodBankDonationAgreementId, numberLengthInFilename, "0", STR_PAD_LEFT);
    $pdfFilename = 'darovaci_smlouva_' . $donationAgreementNumber . '.pdf';
}

$pdf->Output($pdfFilename, "I");
exit;
