<?php
// přehled darovacich smluv
header("Content-Type: text/html; charset=utf-8");

require_once '../conf/commonApp.php';
doInit();
core\AuthenticationManager::verifyTheAuthenticity($_authenticationCode, $_loggedAppUserId);
$authorizationManager = new core\AuthorizationManager($_loggedAppUser);
if (!$authorizationManager->hasRightToPage(core\RightName::donationAgreementList)) {
    core\HttpUtils::redirectAndExit();
}
$rights = $authorizationManager->getRights();
$fullAccessToDonationAgreementList = $authorizationManager->hasFullAccessToPage(core\RightName::donationAgreementList);
$donationAgreementManager = form\DonationAgreementManager::createForFoodBank($_loggedAppUser->foodBankId);

if (isset($_GET['delete']) and $fullAccessToDonationAgreementList) {
    $donationAgreementId = $_GET['donationAgreementId'];
    $donationAgreementManager->deleteDonationAgreementByDonationAgreementId($donationAgreementId);
}

if (isset($_POST['filter'])) {
    $filterListData = array(
        'foodBankDonationAgreementId' => $_POST['filterByFoodBankDonationAgreementId'],
        'customerName' => $_POST['filterByCustomerName']
    );
} else {
    $filterListData = array(
        'foodBankDonationAgreementId' => '',
        'customerName' => ''
    );
}

$t = new ui\Template("./lang/cs");
$t->set_file("tmt", "donationAgreementList.tmt");
$donationAgreementManager->setFoodBankDonationAgreementIdFilter($filterListData['foodBankDonationAgreementId']);
$donationAgreementManager->setCustomerNameFilter($filterListData['customerName']);
$customerId = $authorizationManager->isUser() ? $_loggedAppUser->customerId : null;
$donationAgreementListData = $donationAgreementManager->getDonationAgreementList($customerId);
$t->set_var(array(
    'donationAgreementListData' => htmlspecialchars(json_encode($donationAgreementListData)),
    'filterListData' => htmlspecialchars(json_encode($filterListData)),
    'isUser' => $authorizationManager->isUser()?ui\Visibility::show : ui\Visibility::hide
));
$t->set_global_var_to_template();
$t->pparse("out", "tmt");
exit;