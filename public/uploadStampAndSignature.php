<?php
header("Content-Type: text/html; charset=utf-8");

require_once '../conf/commonApp.php';

doInit();
core\AuthenticationManager::verifyTheAuthenticity($_authenticationCode, $_loggedAppUserId);
$authorizationManager = new core\AuthorizationManager($_loggedAppUser);
if (!$authorizationManager->hasRightToPage(core\RightName::settingFoodBank)) {core\HttpUtils::redirectAndExit();}
$rights = $authorizationManager->getRights();
$fullAccessToSettingFoodBank = $authorizationManager->hasFullAccessToPage(core\RightName::settingFoodBank);
$uploadOkStampAndSignature = false;

if (!empty($_FILES) and $fullAccessToSettingFoodBank) {

    $fileName = 'stampAndSignature_' . $_loggedAppUser->foodBankId . '.' . pathinfo($_FILES['file']['name'], PATHINFO_EXTENSION);
    $targetFileStampAndSignature = STAMP_AND_SIGNATURE_PATH . '/' . $fileName;

    if ($_FILES['file']['size'] < MAX_FILE_SIZE) {
        $checkFileStampAndSignature = getimagesize($_FILES['file']['tmp_name']);
        if ($checkFileStampAndSignature !== false) {
            if (move_uploaded_file($_FILES['file']['tmp_name'], $targetFileStampAndSignature)) {
                $foodBankManager = party\FoodBankManager::createForFoodBank($_loggedAppUser->foodBankId);
                $foodBankManager->setStampAndSignatureName($fileName);
                $uploadOkStampAndSignature = true;
            }
        }
    }
}

core\HttpUtils::sendSuccessfulJsonAjaxResponseAndExit(['uploadOkStampAndSignature' => $uploadOkStampAndSignature]);
