<?php
header("Content-Type: text/html; charset=utf-8");
require_once '../conf/commonApp.php';

doInit();
core\AuthenticationManager::verifyTheAuthenticity($_authenticationCode, $_loggedAppUserId);
$authorizationManager = new core\AuthorizationManager($_loggedAppUser);

if (!$authorizationManager->hasRightToPage(core\RightName::settingDonationAgreements)) {
    core\HttpUtils::redirectAndExit();
}

$rights = $authorizationManager->getRights();
$fullAccessToSettingDonationAgreements = $authorizationManager->hasFullAccessToPage(core\RightName::settingDonationAgreements);
$foodBankManager = party\FoodBankManager::createForFoodBank($_loggedAppUser->foodBankId);

if (isset($_POST['saveSettings']) and $fullAccessToSettingDonationAgreements) {
    $foodBankManager->setDonationAgreementName($_POST['donationAgreementName']);
}

$foodBankData = array('donationAgreementName' => $foodBankManager->getDonationAgreementName());

$t = new ui\Template("./lang/cs");
$t->set_file("tmt", "settingDonationAgreements.tmt");
$t->set_var('foodBankData', htmlspecialchars(json_encode($foodBankData)));
$t->set_global_var_to_template();
$t->pparse("out", "tmt");
