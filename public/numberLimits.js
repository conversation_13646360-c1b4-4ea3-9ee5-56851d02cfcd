angular.module('numberLimits', [])

.directive('ngMax', function () {
	 return {
	     restrict: 'A',
	     require: 'ngModel',
	     link: function (scope, elem, attr, ctrl) {
	     	ctrl.$validators.maxValidator = function (value) {
	     		var max = attr.ngMax !== undefined ? attr.ngMax : Infinity;
	     		value = value.toString().replace(/,/, ".");
	             if (!isEmpty(value) && value > max) {
	                 ctrl.$setValidity('ngMax', false);
	                 return undefined;
	             } else {
	                 ctrl.$setValidity('ngMax', true);
	                 return value;
	             }
	         };
	     }
	 };
	}
)

.directive('ngMin', function () {
	 return {
	     restrict: 'A',
	     require: 'ngModel',
	     link: function (scope, elem, attr, ctrl) {
	     	ctrl.$validators.minValidator = function (value) {
	     		var min = attr.ngMin !== undefined ? attr.ngMin : -Infinity;
	     		value = value.toString().replace(/,/, ".");
	     		 if (value < min) {
	                 ctrl.$setValidity('ngMin', false);
	                 return undefined;
	             } else {
	                 ctrl.$setValidity('ngMin', true);
	                 return value;
	             }
	         };
	     }
	 };
	}
);

function isEmpty(value) {
	return angular.isUndefined(value) || value === '' || value === null || value !== value;
}