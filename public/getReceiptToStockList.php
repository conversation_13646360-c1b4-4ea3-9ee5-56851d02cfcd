<?php
header("Content-Type: text/html; charset=utf-8");

require_once '../conf/commonApp.php';

doInit();
core\AuthenticationManager::verifyTheAuthenticity($_authenticationCode, $_loggedAppUserId);
$authorizationManager = new core\AuthorizationManager($_loggedAppUser);
if (!$authorizationManager->hasRightToPage(core\RightName::receiptToStockList)) { core\HttpUtils::redirectAndExit(); }
$rights = $authorizationManager->getRights();

$filledFormId = $_GET['filledFormId'];
$filledForm = form\FilledForm::createForFoodBank($_loggedAppUser->foodBankId);
$formFillerListData = $filledForm->getSentFormByFilledFormId($filledFormId);
$formId = $formFillerListData['formId'];

$supplierManager = party\SupplierManager::createForFoodBank($_loggedAppUser->foodBankId);
$suppliersResult = $supplierManager->getSelectedSupplierList($formId);
$supplierListData = $suppliersResult->fetchAll();

$filledFormCommodity = form\FilledFormCommodity::createForFoodBank($_loggedAppUser->foodBankId);
$commodityListData = $filledFormCommodity->getCommodityListByFormIdAndFilledFormId($formId, $filledFormId);

$receivedFormListData = array('formFillerList' => $formFillerListData, 'commodityList' => $commodityListData, 'supplierList' => $supplierListData);
core\HttpUtils::sendSuccessfulJsonAjaxResponseAndExit($receivedFormListData);
