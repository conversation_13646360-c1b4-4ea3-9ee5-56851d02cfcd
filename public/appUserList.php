<?php
header("Content-Type: text/html; charset=utf-8");

require_once '../conf/commonApp.php';

doInit();
core\AuthenticationManager::verifyTheAuthenticity($_authenticationCode, $_loggedAppUserId);
$authorizationManager = new core\AuthorizationManager($_loggedAppUser);
if (!$authorizationManager->hasRightToPage(core\RightName::appUserList)) {
    core\HttpUtils::redirectAndExit();
}
$rights = $authorizationManager->getRights();
$fullAccessToAppUserList = $authorizationManager->hasFullAccessToPage(core\RightName::appUserList);

$appUserManager = new core\AppUserManager();

if ($_GET['delete'] and $fullAccessToAppUserList) {
    $appUserManager->deleteAppUser($_GET['appUserId'], $_loggedAppUser->foodBankId);
}

if (isset($_POST['save']) and $fullAccessToAppUserList) {
    $password = trim($_POST['password']);

    $appUserToBeSaved = array(
        'login' => $_POST['login'],
        'appUserId' => $_POST['appUserId'],
        'foodBankId' => $_loggedAppUser->foodBankId,
        'userType' => $_POST['userType']
    );
    if (!empty($password)) {
        $appUserToBeSaved['password'] = md5($password);
    }
    $appUserManager->saveAppUser($appUserToBeSaved);
}

$t = new ui\Template("./lang/cs");
$t->set_file("tmt", "appUserList.tmt");
$userTypeListData = core\UserTypeManager::getUserTypeList($_loggedAppUser->userType);
$appUserList = $appUserManager->getAppUserList($_loggedAppUser);

foreach ($appUserList as $appUser) {
    $appUserId = $appUser->appUserId;
    $appUserListData[] = array(
        'appUserId' => $appUserId,
        'login' => $appUser->login,
        'password' => '',
        'userType' => $appUser->userType,
        'userTypeName' => core\UserTypeManager::getUserTypeName($appUser->userType),
        'isStorekeeper' => $appUser->userType === core\AppUserRoleName::STOREKEEPER ? true : false,
        'usedItem' => $appUserManager->isAppUserUsed($appUserId, $_loggedAppUser->foodBankId)
    );
}

$t->set_var(array(
    'appUserListData' => htmlspecialchars(json_encode($appUserListData)),
    'userTypeListData' => htmlspecialchars(json_encode($userTypeListData))
));

$t->set_global_var_to_template();
$t->pparse("out", "tmt");
