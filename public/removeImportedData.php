<?php
header("Content-Type: text/html; charset=utf-8");

require_once '../conf/commonApp.php';

doInit();

core\AuthenticationManager::verifyTheAuthenticity($_authenticationCode, $_loggedAppUserId);
$authorizationManager = new core\AuthorizationManager($_loggedAppUser);

if (!$authorizationManager->hasRightToPage(core\RightName::import)) {
    core\HttpUtils::redirectAndExit();
}

$hasAccessAllStocks = $authorizationManager->hasFullAccessToPage('allStocks');

$postData = json_decode(file_get_contents('php://input'), true);
$customerId = $postData['customerId'];
$issuedAt = date\Date::createBySqlDate($postData['issuedAt']);
$importer = import\Importer::createForFoodBank($_loggedAppUser->foodBankId);
$deleted = $importer->removeImportedDataFor($_loggedAppUser->currentStockId, $customerId, $issuedAt);

if ($deleted) {
    core\HttpUtils::sendSuccessfulEmptyAjaxResponseAndExit();
}

core\HttpUtils::sendErrorResponseAndExit();
exit;
