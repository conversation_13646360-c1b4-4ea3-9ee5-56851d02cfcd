<?php
header("Content-Type: text/html; charset=utf-8");

require_once '../conf/commonApp.php';
doInit();
core\AuthenticationManager::verifyTheAuthenticity($_authenticationCode, $_loggedAppUserId);
$authorizationManager = new core\AuthorizationManager($_loggedAppUser);
$fullAccessToFormList = $authorizationManager->hasFullAccessToPage(core\RightName::formList);

if ($fullAccessToFormList) {
  $foodBankId = $_loggedAppUser->foodBankId;
  $supplierId = intval($_GET['supplierId']);
  $formId = intval($_GET['formId']);
  \dibi::delete('FormSupplier')->where('[supplierId]=' . $supplierId . ' AND [formId]=' . $formId . ' AND [foodBankId]=' . $foodBankId)->execute();
  core\HttpUtils::sendSuccessfulEmptyAjaxResponseAndExit();
}
