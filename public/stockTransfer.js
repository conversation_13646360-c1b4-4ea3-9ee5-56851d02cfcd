angular.module('PBanka', ['mgcrea.ngStrap', 'hpBanka', 'hpBankaMenu', 'transferCommodityAmount', 'numberLimits'])

.controller('StockTransferController', function($scope) {
	//$scope.showEdit = false;
	$scope.totalAmount = 0;
	
	$scope.countAmount = function() {
		
		var totalAmount = 0.0;
		angular.forEach($scope.stockTransfer.transferCommodityList, function(commodity) {
			var amount = commodity.amount !== undefined ? commodity.amount.toString().replace(/,/, ".") : '0.0'; 
			if (!Number.isNaN(parseFloat(amount))) {
				totalAmount += parseFloat(amount);
			}
		});
		$scope.totalAmount = totalAmount;
	};

	
	$scope.canSave = function() {
		return ($scope.stockTransferForm.$valid && $scope.totalAmount > 0);
	};
	
	$scope.$watch("stockTransfer.commoditySummary.length", function () {
		  $scope.countAmount();
	});
});