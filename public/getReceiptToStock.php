<?php
header("Content-Type: text/html; charset=utf-8");

require_once '../conf/commonApp.php';
doInit();
core\AuthenticationManager::verifyTheAuthenticity($_authenticationCode, $_loggedAppUserId);
$authorizationManager = new core\AuthorizationManager($_loggedAppUser);
if (!$authorizationManager->hasRightToPage(core\RightName::receiptToStockList)) { core\HttpUtils::redirectAndExit(); }

$postData = json_decode(file_get_contents('php://input'), true);
$filledFormManager = form\FilledForm::createForFoodBank($_loggedAppUser->foodBankId);
$filledFormManager->setStockByAppUser($_loggedAppUserId);
$filterList = $postData['filterList'];
$filledFormManager->setSupplierNameFilter($filterList['supplier']);
$filledFormManager->setDateFromFilter($filterList['dateFrom']);
$filledFormManager->setDateToFilter($filterList['dateTo']);
$receivedFormListData = $filledFormManager->getReceivedFormList();

$result = array('receivedFormListData' => $receivedFormListData);
core\HttpUtils::sendSuccessfulJsonAjaxResponseAndExit($result);
