<?php
header("Content-Type: text/html; charset=utf-8");
require_once '../conf/commonApp.php';

doInit();
core\AuthenticationManager::verifyTheAuthenticity($_authenticationCode, $_loggedAppUserId);
$authorizationManager = new core\AuthorizationManager($_loggedAppUser);
if (!$authorizationManager->hasRightToPage(core\RightName::appUserList)) { core\HttpUtils::redirectAndExit(); }

$appUserId = !empty($_GET['appUserId']) ? $_GET['appUserId'] : 0;
$appUserStockRelation = form\AppUserStockRelation::createForFoodBank($_loggedAppUser->foodBankId);
$result = $appUserStockRelation->retriveStockByAppUser($appUserId);

core\HttpUtils::sendSuccessfulJsonAjaxResponseAndExit($result);
