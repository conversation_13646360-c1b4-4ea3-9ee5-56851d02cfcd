<?php
header("Content-Type: text/html; charset=utf-8");

require_once '../conf/commonApp.php';

doInit();
core\AuthenticationManager::verifyTheAuthenticity($_authenticationCode, $_loggedAppUserId);
$authorizationManager = new core\AuthorizationManager($_loggedAppUser);
if (!$authorizationManager->hasRightToPage(core\RightName::sentFormList)) { core\HttpUtils::redirectAndExit(); }
$rights = $authorizationManager->getRights();

$stockReleaseNoteId = $_GET['stockReleaseNoteId'];
$stockReleaseNoteCommodity = form\StockReleaseNoteCommodity::createForFoodBank($_loggedAppUser->foodBankId);
$data = $stockReleaseNoteCommodity->getStockReleaseNoteCommodityByStockReleaseNoteId($stockReleaseNoteId);
$stockReleaseNote = form\StockReleaseNote::createForFoodBank($_loggedAppUser->foodBankId);
$sentFormData = $stockReleaseNote->getStockReleaseNoteByStockReleaseNoteId($stockReleaseNoteId);
$sentFormData['totalKg'] = $data['totalAmount'];
$sentFormData['priceCalculation'] = $data['priceCalculation'];

$receivedFormListData = array('stockReleaseNoteCommodityData' => $data['stockReleaseNoteCommodityListData'], 'formData' => $sentFormData);
core\HttpUtils::sendSuccessfulJsonAjaxResponseAndExit($receivedFormListData);
