<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="description" content="">
    <meta name="author" content="">
    <link rel="icon" href="../../favicon.ico">
    <title>Potravinová banka</title>
    <!-- Bootstrap core CSS -->
    <link href="bootstrap/css/bootstrap.min.css?hpbankaversion={hpBankaVersion}" rel="stylesheet">
    <link href="bootstrap-additions/bootstrap-additions.min.css?hpbankaversion={hpBankaVersion}" rel="stylesheet">
    <!-- Custom styles for this template -->
    <link href="style/hpbanka.css?hpbankaversion={hpBankaVersion}" rel="stylesheet">
    <!-- HTML5 shim and Respond.js for IE8 support of HTML5 elements and media queries -->
    <!--[if lt IE 9]>
      <script src="https://oss.maxcdn.com/html5shiv/3.7.2/html5shiv.min.js?hpbankaversion={hpBankaVersion}"></script>
      <script src="https://oss.maxcdn.com/respond/1.4.2/respond.min.js?hpbankaversion={hpBankaVersion}"></script>
    <![endif]-->
    <script src="js/angular/angular.min.js?hpbankaversion={hpBankaVersion}"></script>
    <script src="js/angular/i18n/angular-locale_cs-cz.js?hpbankaversion={hpBankaVersion}"></script>
    <script src="js/jquery.min.js?hpbankaversion={hpBankaVersion}"></script>
    <script src="bootstrap/js/bootstrap.min.js?hpbankaversion={hpBankaVersion}"></script>
    <script src="js/angular-strap/angular-strap.min.js?hpbankaversion={hpBankaVersion}"></script>
    <script src="js/angular-strap/angular-strap.tpl.min.js?hpbankaversion={hpBankaVersion}"></script>
    <script type="text/javascript" src="hpBanka.js?hpbankaversion={hpBankaVersion}"></script>
    <script type="text/javascript" src="hpBankaMenu.js?hpbankaversion={hpBankaVersion}"></script>
    <script type="text/javascript" src="transferCommodityAmount.js?hpbankaversion={hpBankaVersion}"></script>
    <script type="text/javascript" src="numberLimits.js?hpbankaversion={hpBankaVersion}"></script>
    <script type="text/javascript" src="stockTransfer.js?hpbankaversion={hpBankaVersion}"></script>
  </head>

  <body ng-app="PBanka" ng-controller="StockTransferController" ng-init="basicInformation={basicInformationData}; rightList={rightListData}; stockTransfer={stockTransferData};">
  <div >
    <div class="container">
      <img src="images/logo_hpbanka.png" alt="Potravinová banka" id="logoHpBanka">
      <img src="images/logo/{logoName}" alt="Potravinová banka" id="logoPBanka">
      <div id="namePBanka">{pbName}</div>
    </div>
  </div>
  <hp-banka-admin-menu ng-init="activeMainMenu='stock'; activeSubMenu='stockTransfersList'; showSubmenuStock=true;"></hp-banka-admin-menu>

  <div class="container">
  <div class="page-header">
    <h2>Převod ze skladu</h2>
  </div>
  <form method="post" name="stockTransferForm" class="form-horizontal" action="stockTransfer.php" novalidate>
  <table class="form-filler-caption">
    <tr>
      <td colspan="2"><label>Vydávající sklad:&nbsp;{{ stockTransfer.nameStock }}</label></td>
    </tr>
    <tr>
      <td><label for="issuedAt">Převedeno dne&nbsp;<span class="glyphicon glyphicon-star"></span></label>&nbsp;&nbsp;&nbsp;</td>
      <td ng-class="{true:'has-error', false:''}[(stockTransferForm.issuedAt.$error.required || stockTransferForm.issuedAt.$error.date) && !stockTransferForm.issuedAt.$pristine]">
        <input type="text" class="form-control" id="issuedAt" ng-model="stockTransfer.issuedAt" bs-datepicker required>
        <label class="control-label" for="" ng-show="stockTransferForm.issuedAt.$error.required && !stockTransferForm.issuedAt.$pristine">Tento údaj je povinný.</label>
        <label class="control-label" for="" ng-show="stockTransferForm.issuedAt.$error.date && !stockTransferForm.issuedAt.$pristine">Zadejte datum ve formátu DD.MM.YYYY.</label>
      </td>
      <td class="col-sm-1"></td>
      <td><label for="customer">Přijímající sklad&nbsp;<span class="glyphicon glyphicon-star"></span></label>&nbsp;&nbsp;&nbsp;</td>
      <td>
        <select class="form-control" ng-model="stockTransfer.currentTargetStock" ng-options="targetStock.name for targetStock in stockTransfer.stockList" required></select>
      </td>
    </tr>
    <tr>
      <td ><label for="note">Poznámka&nbsp;</label></td>
      <td colspan="3"><textarea id="note" rows="1" class="form-control" ng-model="stockTransfer.note" maxlength="30"></textarea>
    </tr>
  </table>
  <h4>Uveďte množství převedených komodit:&nbsp;<span class="glyphicon glyphicon-star"></span></h4>
  <div class="form-group" ng-repeat="commodity in stockTransfer.transferCommodityList">
    <transfer-commodity-amount commodity="commodity" form="stockTransferForm" id="$index + 1" count-amount-fn="countAmount()"></transfer-commodity-amount>
  </div>

  <div class="col-sm-7 text-right"><span class="glyphicon glyphicon-star"></span> Povinné údaje.</div><div class="col-sm-5 text-right">&nbsp;</div>
  <h5>Hmotnost vydaného zboží celkem: <b>{{ totalAmount | number:3 }} kg</b>.</h5>
  <div class="form-group">
    <div class="col-sm-5">
      <input type="hidden" name="stockTransferData" value="{{ stockTransfer}}">
      <input type="hidden" name="issuedAt" value="{{ stockTransferCommodity.issuedAt }}">
      <button ng-if="rightList.stockTransfersList.fullAccess" name="save" class="btn btn-success" title="uložit" ng-disabled="!canSave();"><span class="glyphicon glyphicon-save"></span>&nbsp;&nbsp;Uložit</button>
      <a class="btn btn-success" title="zpět na seznam" href="stockTransfersList.php"><span class="glyphicon glyphicon-hand-left"></span>&nbsp;&nbsp;zpět na seznam</a>
    </div>
  </div>
  </form>
  <hr>
  <footer>
    <p>{{ basicInformation.copyright }}</p>
  </footer>
  </div>
  </body>
</html>
