<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="description" content="">
    <meta name="author" content="">
    <link rel="icon" href="../../favicon.ico">
    <title>Potravinová banka</title>
    <!-- Bootstrap core CSS -->
    <link href="bootstrap/css/bootstrap.min.css?hpbankaversion={hpBankaVersion}" rel="stylesheet">
    <link href="bootstrap-additions/bootstrap-additions.min.css?hpbankaversion={hpBankaVersion}" rel="stylesheet">
    <!-- Custom styles for this template -->
    <link href="style/hpbanka.css?hpbankaversion={hpBankaVersion}" rel="stylesheet">
    <!-- HTML5 shim and Respond.js for IE8 support of HTML5 elements and media queries -->
    <!--[if lt IE 9]>
      <script src="https://oss.maxcdn.com/html5shiv/3.7.2/html5shiv.min.js?hpbankaversion={hpBankaVersion}"></script>
      <script src="https://oss.maxcdn.com/respond/1.4.2/respond.min.js?hpbankaversion={hpBankaVersion}"></script>
    <![endif]-->
    <script src="js/angular/angular.min.js?hpbankaversion={hpBankaVersion}"></script>
    <script src="js/angular/i18n/angular-locale_cs-cz.js?hpbankaversion={hpBankaVersion}"></script>
    <script src="js/jquery.min.js?hpbankaversion={hpBankaVersion}"></script>
    <script src="bootstrap/js/bootstrap.min.js?hpbankaversion={hpBankaVersion}"></script>
    <script type="text/javascript" src="js/upload/angular-file-upload.min.js?hpbankaversion={hpBankaVersion}"></script>
    <script type="text/javascript" src="hpBanka.js?hpbankaversion={hpBankaVersion}"></script>
    <script type="text/javascript" src="hpBankaMenu.js?hpbankaversion={hpBankaVersion}"></script>
    <script type="text/javascript" src="settingFoodBank.js?hpbankaversion={hpBankaVersion}"></script>
</head>

<body ng-app="PBanka" ng-controller="SettingFoodBankController" nv-file-drop="" uploader="uploader"
    filters="queueLimit, imageFilter" ng-init="basicInformation={basicInformationData}; rightList={rightListData};">
    <div>
        <div class="container">
            <img src="images/logo_hpbanka.png" alt="Potravinová banka" id="logoHpBanka">
            <img src="images/logo/{logoName}" alt="Potravinová banka" id="logoPBanka">
            <div id="namePBanka">{pbName}</div>
        </div>
    </div>
    <hp-banka-admin-menu
        ng-init="activeMainMenu='formList'; activeSubMenu='settingFoodBank'; showSubmenuSettings=true;"></hp-banka-admin-menu>
    <div class="container" ng-init="stampAndSignatureExists={stampAndSignatureExistsData}">
        <form method="post" action="settingFoodBank.php" name="settingFoodBankForm" novalidate class="form-horizontal"
            ng-init="foodBank={foodBankData}">
            <div class="form-group">
                <label for="inputName" class="col-sm-2 control-label">Název&nbsp;<span
                        class="glyphicon glyphicon-star"></span></label>
                <div class="col-sm-8"
                    ng-class="{true:'has-error', false:''}[settingFoodBankForm.name.$error.required  && !settingFoodBankForm.name.$pristine]">
                    <input type="text" name="name" class="form-control" id="inputName" placeholder="Název"
                        ng-model="foodBank.name" required>
                    <label class="control-label" for="inputName"
                        ng-show="settingFoodBankForm.name.$error.required && !settingFoodBankForm.name.$pristine">Tento
                        údaj je povinný.</label>
                </div>
            </div>
            <div class="form-group">
                <label for="inputAddress" class="col-sm-2 control-label">Adresa</label>
                <div class="col-sm-8">
                    <input type="text" name="address" class="form-control" id="inputAddress" placeholder="Adresa"
                        ng-model="foodBank.address">
                </div>
            </div>
            <div class="form-group">
                <label for="inputIC" class="col-sm-2 control-label">IČ</label>
                <div class="col-sm-8">
                    <input type="text" name="IC" class="form-control" id="inputIC" placeholder="IČ"
                        ng-model="foodBank.ic">
                </div>
            </div>
            <div class="form-group">
                <label for="inputDIC" class="col-sm-2 control-label">DIČ</label>
                <div class="col-sm-8">
                    <input type="text" name="DIC" class="form-control" id="inputDIC" placeholder="DIČ"
                        ng-model="foodBank.dic">
                </div>
            </div>

            <div class="form-group">
                <label for="abbreviation" class="col-sm-2 control-label">Zkratka</label>
                <div class="col-sm-8">
                    <input type="text" name="abbreviation" class="form-control" id="abbreviation" placeholder="Zkratka"
                        ng-model="foodBank.abbreviation">
                </div>
            </div>

            <div class="form-group">
                <label for="inputSenderEmails" class="col-sm-2 control-label">Email odesilatele&nbsp;<span
                        class="glyphicon glyphicon-star"></span></label>
                <div class="col-sm-8"
                    ng-class="{true:'has-error', false:''}[settingFoodBankForm.senderEmails.$error.required && !settingFoodBankForm.senderEmails.$pristine]">
                    <input type="text" name="senderEmails" class="form-control" id="inputSenderEmails"
                        placeholder="Email odesílatele" ng-model="foodBank.senderEmails" required>
                    <label class="control-label" for="inputSenderEmails"
                        ng-show="settingFoodBankForm.senderEmails.$error.required && !settingFoodBankForm.senderEmails.$pristine">Tento
                        údaj je povinný.</label>
                </div>
            </div>

            <div class="form-group">
                <label class="col-sm-4 control-label">Vkládat razítko do výdejek pokud je zde
                    vloženo&nbsp;</label>
                <div class="col-sm-4">
                    <label class="checkbox-inline" for="stampInStockRelease">
                        <input type="checkbox" name="stampInStockRelease"
                            ng-disabled="!stampAndSignatureExists.stampAndSignatureExists" id="stampInStockRelease"
                            ng-model="foodBank.stampInStockRelease">&nbsp;
                    </label>
                </div>
            </div>

            <div class="col-sm-10 text-right"><span class="glyphicon glyphicon-star"></span> Povinné údaje.</div>

            <div class="form-group">
                <div class="col-sm-offset-2 col-sm-8">
                    <button ng-if="rightList.settingFoodBank.fullAccess" type="submit" name="saveSettings"
                        class="btn btn-success" title="upravit" ng-disabled="!canSave();"><span
                            class="glyphicon glyphicon-save"></span>&nbsp;&nbsp;uložit</button>
                </div>
            </div>
            <input type="hidden" name="foodBank" value="{{ foodBank }}" />

            <div class="form-group" ng-show="showStampAndSignatureSave">
                <div class="col-sm-offset-2 col-sm-8">
                    <p class="bg-warning infoAction"><button type="button" class="close" aria-label="Close">
                            <span aria-hidden="true"
                                ng-click="showStampAndSignatureSave=false">&times;</span></button>Soubor s razítkem a
                        podpisem byl uložen.</p>
                </div>
            </div>

            <div class="form-group" ng-show="showStampAndSignatureErrorSave">
                <div class="col-sm-offset-2 col-sm-8">
                    <p class="bg-danger infoAction"><button type="button" class="close" aria-label="Close">
                            <span aria-hidden="true"
                                ng-click="showStampAndSignatureErrorSave=false">&times;</span></button>Soubor s
                        razítkem a podpisem nebyl uložen.</p>
                </div>
            </div>

            <div class="form-group" ng-show="stampAndSignatureExists.stampAndSignatureExists">
                <label for="" class="col-sm-2 control-label">Razítko s podpis</label>
                <div class="col-sm-8">
                    <img src="images/signature/{{ foodBank.stampAndSignatureName }}" class="img-responsive"
                        alt="Razítko s podpisem" />
                </div>
            </div>

            <div class="form-group" ng-show="stampAndSignatureExists.stampAndSignatureExists">
                <div class="col-sm-offset-2 col-sm-8">
                    <button ng-if="rightList.settingFoodBank.fullAccess" type="submit" name="removeStampAndSignature"
                        class="btn btn-success" title="smazat razítko s podpis"><span
                            class="glyphicon glyphicon-trash"></span>&nbsp;&nbsp;Smazat razítko s podpisem</button>
                </div>
            </div>

            <div class="form-group" ng-show="!stampAndSignatureExists.stampAndSignatureExists">
                <div class="col-sm-offset-2 col-sm-8">
                    <div ng-show="uploader.isHTML5">
                        <div class="well my-drop-zone" nv-file-over="" uploader="uploader">Přetáhněte obrázek s
                            razítkem a podpisem na toto místo. Povolené formáty obrázku jsou: jpeg, png a velikost
                            max. 1 MB.
                            Doporučený poměr šírky k výšce je 5:3.
                            Velikost v pixelech je na šířku 300 a na výšku 180.
                        </div>
                    </div>
                </div>
            </div>
            <div class="form-group" ng-show="!stampAndSignatureExists.stampAndSignatureExists">
                <label class="col-sm-2 control-label"></label>
                <div class="col-sm-8">

                    <table class="table">
                        <thead>
                            <tr>
                                <th width="50%">Jméno souboru</th>
                                <th>Status</th>
                                <th>Akce</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr ng-repeat="item in uploader.queue">
                                <td><strong>{{ item.file.name }}</strong></td>
                                <td class="text-center">
                                    <span ng-show="item.isError"><i class="glyphicon glyphicon-remove"></i></span>
                                </td>
                                <td>
                                    <button type="button" class="btn btn-success btn-xs" ng-click="item.remove()">
                                        <span class="glyphicon glyphicon-trash"></span> smazat
                                    </button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                    <div>
                        <button type="button" class="btn btn-success btn-s" ng-click="uploader.uploadAll()"
                            ng-disabled="!uploader.getNotUploadedItems().length">
                            <span class="glyphicon glyphicon-upload"></span> načíst
                        </button>
                        <button type="button" class="btn btn-success btn-s" ng-click="uploader.clearQueue()"
                            ng-disabled="!uploader.queue.length">
                            <span class="glyphicon glyphicon-trash"></span> smazat
                        </button>
                    </div>
                </div>
            </div>
        </form>

        <hr>
        <footer>
            <p>{copyright}</p>
        </footer>
    </div> <!-- /container -->
</body>

</html>