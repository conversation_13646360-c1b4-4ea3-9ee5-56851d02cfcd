<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="description" content="">
    <meta name="author" content="">
    <link rel="icon" href="../../favicon.ico">
    <title>Potravinová banka</title>
    <!-- Bootstrap core CSS -->
    <link href="bootstrap/css/bootstrap.min.css?hpbankaversion={hpBankaVersion}" rel="stylesheet">
    <link href="bootstrap-additions/bootstrap-additions.min.css?hpbankaversion={hpBankaVersion}" rel="stylesheet">
    <!-- Custom styles for this template -->
    <link href="style/hpbanka.css?hpbankaversion={hpBankaVersion}" rel="stylesheet">
    <!-- HTML5 shim and Respond.js for IE8 support of HTML5 elements and media queries -->
    <!--[if lt IE 9]>
      <script src="https://oss.maxcdn.com/html5shiv/3.7.2/html5shiv.min.js?hpbankaversion={hpBankaVersion}"></script>
      <script src="https://oss.maxcdn.com/respond/1.4.2/respond.min.js?hpbankaversion={hpBankaVersion}"></script>
    <![endif]-->
    <script src="js/angular/angular.min.js?hpbankaversion={hpBankaVersion}"></script>
    <script src="js/angular/angular-animate.min.js?hpbankaversion={hpBankaVersion}"></script>
    <script src="js/angular/i18n/angular-locale_cs-cz.js?hpbankaversion={hpBankaVersion}"></script>
    <script src="js/jquery.min.js?hpbankaversion={hpBankaVersion}"></script>
    <script src="bootstrap/js/bootstrap.min.js?hpbankaversion={hpBankaVersion}"></script>
    <script src="js/angular-strap/angular-strap.min.js?hpbankaversion={hpBankaVersion}"></script>
    <script src="js/angular-strap/angular-strap.tpl.min.js?hpbankaversion={hpBankaVersion}"></script>
    <script type="text/javascript" src="hpBanka.js?hpbankaversion={hpBankaVersion}"></script>
    <script type="text/javascript" src="hpBankaMenu.js?hpbankaversion={hpBankaVersion}"></script>
    <script type="text/javascript" src="stockSelectList.js?hpbankaversion={hpBankaVersion}"></script>
    <script type="text/javascript" src="incomePerStock.js?hpbankaversion={hpBankaVersion}"></script>
</head>

<body ng-app="PBanka" ng-controller="IncomePerStockController"
    ng-init="basicInformation={basicInformationData}; rightList={rightListData}; incomePerStock={incomePerStockData};">
    <div>
        <div class="container">
            <img src="images/logo_hpbanka.png" alt="Potravinová banka" id="logoHpBanka">
            <img src="images/logo/{logoName}" alt="Potravinová banka" id="logoPBanka">
            <div id="namePBanka">{pbName}</div>
        </div>
    </div>
    <hp-banka-admin-menu ng-init="activeMainMenu='stock'; activeSubMenu='incomePerStockList'; showSubmenuStock=true;">
    </hp-banka-admin-menu>
    <div class="container">
        <form method="post" name="incomePerStockForm" class="form-horizontal" action="incomePerStock.php" novalidate>
            <table class="form-filler-caption">
                <tr>
                    <td><label for="dateOfCollection">Naskladněno dne&nbsp;<span
                                class="glyphicon glyphicon-star"></span></label>&nbsp;&nbsp;&nbsp;</td>
                    <td
                        ng-class="{true:'has-error', false:''}[(incomePerStockForm.consumptionDate.$error.required || incomePerStockForm.consumptionDate.$error.date) && !incomePerStockForm.consumptionDate.$pristine]">
                        <input type="text" class="form-control" id="dateOfCollection"
                            ng-model="incomePerStock.consumptionDate" bs-datepicker required>
                        <label class="control-label" for=""
                            ng-show="incomePerStockForm.consumptionDate.$error.required && !incomePerStockForm.consumptionDate.$pristine">Tento
                            údaj je povinný.</label>
                        <label class="control-label" for=""
                            ng-show="incomePerStockForm.consumptionDate.$error.date && !incomePerStockForm.consumptionDate.$pristine">Zadejte
                            datum ve formátu DD.MM.YYYY.</label>
                    </td>
                    <td class="col-sm-1"></td>
                    <td colspan="2"><label>Název skladu: {{ incomePerStock.nameStock}}</label></td>
                </tr>
                <tr>
                    <td><label for="supplier">Dodavatel&nbsp;<span
                                class="glyphicon glyphicon-star"></span></label>&nbsp;&nbsp;&nbsp;</td>
                    <td ng-init="supplierList={supplierListData}">
                        <select class="form-control" ng-model="currentSupplier"
                            ng-options="supplier.name for supplier in supplierList"
                            ng-init="inicializeSelectedSupplier({supplierId});" required></select>
                        <input type="hidden" name="supplierId" value="{{ currentSupplier.supplierId }}">
                    </td>
                    <td class="col-sm-1"></td>
                    <td><label for="giftValue">Hodnota daru uvedená dodavatelem v {currencySymbol}&nbsp;<span
                                class="glyphicon glyphicon-star"></span></label>&nbsp;&nbsp;&nbsp;</td>
                    <td
                        ng-class="{true:'has-error', false:''}[(incomePerStockForm.giftValue.$error.required || incomePerStockForm.giftValue.$error.pattern) && !incomePerStockForm.giftValue.$pristine]">
                        <input name="giftValue" type="text" class="form-control" id="giftValue"
                            ng-model="incomePerStock.giftValue" ng-pattern="regNumber" required>
                        <label class="control-label" for=""
                            ng-show="incomePerStockForm.giftValue.$error.required && !incomePerStockForm.giftValue.$pristine">Tento
                            údaj je povinný.</label>
                        <label class="control-label" for=""
                            ng-show="incomePerStockForm.giftValue.$error.pattern && !incomePerStockForm.giftValue.$pristine">Prosím,
                            zadejte kladné číslo.</label>
                    </td>
                </tr>
                <tr>
                    <td><label for="note">Poznámka</label></td>
                    <td><textarea class="form-control" rows="2" ng-model="incomePerStock.note"
                            maxlength="30"></textarea></td>
                </tr>
            </table>
            <br>
            <h4 ng-init="commodityList={commodityListData};">Uveďte množství odebraných komodit:&nbsp;<span
                    class="glyphicon glyphicon-star"></span></h4>
            <div class="form-group" ng-repeat="commodity in commodityList">
                <label class="col-sm-5 control-label text-left" for="name{{ $index+1 }}">{{ commodity.code }} &nbsp; {{
                    commodity.name }}</label>
                <div ng-class="{true:'has-error', false:''}[incomePerStockForm.name{{ $index+1 }}.$error.pattern && !incomePerStockForm.name{{ $index+1 }}.$pristine]"
                    class="input-group col-sm-2">
                    <input type="text" class="form-control" name="name{{ $index+1 }}" id="name{{ $index+1 }}"
                        placeholder="např. 1.005" ng-change="countAmount();" ng-pattern="regNumber"
                        ng-model="commodity.amount">
                    <div class="input-group-addon">{{ commodity.unit }}</div>
                </div>
                <strong class="col-sm-7 control-label text-right text-danger"
                    ng-show="incomePerStockForm.name{{ $index+1 }}.$error.pattern">Prosím, zadejte kladné
                    číslo.</strong>
            </div>
            <div class="col-sm-7 text-right"><span class="glyphicon glyphicon-star"></span> Povinné údaje.</div>
            <div class="col-sm-5 text-right">&nbsp;</div>
            <h5>Hmotnost zboží celkem: <b>{{ incomePerStock.totalAmount | number:3 }} kg</b>.</h5>

            <div class="form-group">
                <div class="col-sm-5">
                    <input type="hidden" name="commodityList" value="{{ commodityList }}">
                    <input type="hidden" name="consumptionDate" value="{{ consumptionDate }}">
                    <input type="hidden" name="incomePerStock" value="{{ incomePerStock }}">
                    <input type="hidden" name="stockId" value="{currentStockId}">
                    <button name="save" class="btn btn-success" title="uložit" ng-disabled="!canSave();"><span
                            class="glyphicon glyphicon-save"></span>&nbsp;&nbsp;Uložit</button>
                    <a class="btn btn-success" href="incomePerStockList.php" title="zpět na seznam"><span
                            class="glyphicon glyphicon-hand-left"></span>&nbsp;&nbsp;zpět na seznam</a>
                </div>
            </div>
        </form>
        <hr>
        <footer>
            <p>{copyright}</p>
        </footer>
    </div> <!-- /container -->
</body>

</html>