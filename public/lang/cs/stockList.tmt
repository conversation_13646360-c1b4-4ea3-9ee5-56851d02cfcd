<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="description" content="">
    <meta name="author" content="">
    <link rel="icon" href="../../favicon.ico">
    <title>Potravinová banka</title>
    <!-- Bootstrap core CSS -->
    <link href="bootstrap/css/bootstrap.min.css?hpbankaversion={hpBankaVersion}" rel="stylesheet">
    <!-- Custom styles for this template -->
    <link href="style/hpbanka.css?hpbankaversion={hpBankaVersion}" rel="stylesheet"> 
    <!-- HTML5 shim and Respond.js for IE8 support of HTML5 elements and media queries -->
    <!--[if lt IE 9]>
      <script src="https://oss.maxcdn.com/html5shiv/3.7.2/html5shiv.min.js?hpbankaversion={hpBankaVersion}"></script>
      <script src="https://oss.maxcdn.com/respond/1.4.2/respond.min.js?hpbankaversion={hpBankaVersion}"></script>
    <![endif]-->
    <script src="js/angular/angular.min.js?hpbankaversion={hpBankaVersion}"></script>
    <script src="js/angular/i18n/angular-locale_cs-cz.js?hpbankaversion={hpBankaVersion}"></script>
    <script src="js/jquery.min.js?hpbankaversion={hpBankaVersion}"></script>
    <script src="bootstrap/js/bootstrap.min.js?hpbankaversion={hpBankaVersion}"></script> 
    <script type="text/javascript" src="hpBankaMenu.js?hpbankaversion={hpBankaVersion}"></script>    
    <script type="text/javascript" src="stockList.js?hpbankaversion={hpBankaVersion}"></script>
  </head>

  <body ng-app="PBanka" ng-controller="StockListController" ng-init="basicInformation={basicInformationData}; rightList={rightListData}; stockList={stockListData};">
  <div >
    <div class="container">
      <img src="images/logo_hpbanka.png" alt="Potravinová banka" id="logoHpBanka">
      <img src="images/logo/{logoName}" alt="Potravinová banka" id="logoPBanka">
      <div id="namePBanka">{pbName}</div>
    </div>
  </div>
  <hp-banka-admin-menu ng-init="activeMainMenu='formList'; activeSubMenu='stockList'; showSubmenuSettings=true;"></hp-banka-admin-menu>
  <div class="container">
  <br>
  <table ng-show="!showEdit" class="table">
    <thead>
      <tr>
        <th><a class="sord" ng-click="order('name')">Název skladu<span ng-show="sordBy === 'name'" class="glyphicon" ng-class="{true:' glyphicon-menu-up', false:' glyphicon-menu-down'}[reverse]" ></span></a></th>
        <th>Akce</th>
      </tr> 
      <tr ng-if="rightList.stockList.fullAccess">
        <td></td>
        <td><button type="submit" name="save" class="btn btn-warning" ng-click="addStock();"><span class="glyphicon glyphicon-plus"></span>&nbsp;&nbsp;přidat</button></td>
      </tr>
    </thead> 
    <tbody> 
    <tr ng-repeat="stock in stockList | orderBy:sordBy:reverse">
	    <td>{{ stock.name }}</td>
	    <td nowrap>
	      <a ng-if="rightList.stockList.fullAccess" class="btn btn-success" href="#" title="upravit" ng-click="editStock(stock.stockId);" ><span class="glyphicon glyphicon-edit"></span>&nbsp;&nbsp;upravit</a>
	      <a ng-if="!stock.isUsedStockId && rightList.stockList.fullAccess" class="btn btn-success" href="stockList.php?stockId={{ stock.stockId }}&delete=1" title="smazat"><span class="glyphicon glyphicon-trash"></span>&nbsp;&nbsp;smazat</a>
	    </td>
    </tr> 
    </tbody> 
    <tr ng-if="rightList.stockList.fullAccess">
    <td></td>
    <td><button type="submit" name="save" class="btn btn-warning" ng-click="addStock();"><span class="glyphicon glyphicon-plus"></span>&nbsp;&nbsp;přidat</button></td>
    </tr>
  </table>
  
  <form ng-show="showEdit" method="post" action="stockList.php" name="stockListForm" novalidate class="form-horizontal">
  <div class="form-group">
    <label for="inputName" class="col-sm-2 control-label">Název skladu&nbsp;<span class="glyphicon glyphicon-star"></span></label>
    <div class="col-sm-8" ng-class="{true:'has-error', false:''}[stockListForm.name.$error.required && !stockListForm.name.$pristine]">
      <input type="text" name="name" class="form-control" id="inputName" ng-model="stock.name" placeholder="Název skladu, např. hlavní sklad Ostrava" required>
      <label class="control-label" for="inputName" ng-show="stockListForm.name.$error.required && !stockListForm.name.$pristine">Tento údaj je povinný.</label>
    </div>
  </div>
  <div class="col-sm-10 text-right"><span class="glyphicon glyphicon-star"></span> Povinné údaje.</div>  
  <div class="form-group">
    <div class="col-sm-offset-2 col-sm-8">
      <button type="submit" name="save" class="btn btn-success" title="upravit" ng-disabled="!canSave();"><span class="glyphicon glyphicon-save"></span>&nbsp;&nbsp;uložit</button>
      <a class="btn btn-success" title="zpět na seznam" ng-click="showEdit=false;"><span class="glyphicon glyphicon-hand-left"></span>&nbsp;&nbsp;zpět na seznam</a>
    </div>
  </div>
  <input type="hidden" name="stockId" ng-model="stock.stockId" value="{{ stock.stockId }}"/>
  </form>
  <hr>
  <footer>
    <p>{copyright}</p>
  </footer>
  </div> <!-- /container -->
  </body>
</html>