<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="description" content="">
    <meta name="author" content="">
    <link rel="icon" href="../../favicon.ico">
    <title>Potravinová banka</title>
    <!-- Bootstrap core CSS -->
    <link href="bootstrap/css/bootstrap.min.css?hpbankaversion={hpBankaVersion}" rel="stylesheet">
    <link href="bootstrap-additions/bootstrap-additions.min.css?hpbankaversion={hpBankaVersion}" rel="stylesheet">
    <!-- Custom styles for this template -->
    <link href="style/hpbanka.css?hpbankaversion={hpBankaVersion}" rel="stylesheet">
    <!-- HTML5 shim and Respond.js for IE8 support of HTML5 elements and media queries -->
    <!--[if lt IE 9]>
      <script src="https://oss.maxcdn.com/html5shiv/3.7.2/html5shiv.min.js?hpbankaversion={hpBankaVersion}"></script>
      <script src="https://oss.maxcdn.com/respond/1.4.2/respond.min.js?hpbankaversion={hpBankaVersion}"></script>
    <![endif]-->
    <script src="js/angular/angular.min.js?hpbankaversion={hpBankaVersion}"></script>
    <script src="js/angular/i18n/angular-locale_cs-cz.js?hpbankaversion={hpBankaVersion}"></script>
    <script src="js/jquery.min.js?hpbankaversion={hpBankaVersion}"></script>
    <script src="bootstrap/js/bootstrap.min.js?hpbankaversion={hpBankaVersion}"></script>
    <script type="text/javascript" src="stockSelectList.js?hpbankaversion={hpBankaVersion}"></script>
    <script type="text/javascript" src="hpBankaMenu.js?hpbankaversion={hpBankaVersion}"></script>
    <script type="text/javascript" src="incomePerStockList.js?hpbankaversion={hpBankaVersion}"></script>
</head>

<body ng-app="PBanka" ng-controller="IncomePerStockListController"
    ng-init="basicInformation={basicInformationData}; rightList={rightListData}; incomePerStockList={incomePerStockListData};">
    <div>
        <div class="container">
            <img src="images/logo_hpbanka.png" alt="Potravinová banka" id="logoHpBanka">
            <img src="images/logo/{logoName}" alt="Potravinová banka" id="logoPBanka">
            <div id="namePBanka">{pbName}</div>
        </div>
    </div>
    <hp-banka-admin-menu ng-init="activeMainMenu='stock'; activeSubMenu='incomePerStockList'; showSubmenuStock=true;">
    </hp-banka-admin-menu>
    <div class="container"
        ng-init="stockList={stockListData}; initCurrentStockId={currentStockId};currentStock=[];summaryItem={summaryItem};">

        <div ng-show="directConsumptionData.showMessage" ng-init="directConsumptionData={directConsumptionData}"
            class="alert alert-success" role="alert">
            <button type="button" class="close" ng-click="directConsumptionData.showMessage=false;"><span
                    aria-hidden="true">&times;</span></button>
            V rámci přímého odběru byla vytvořena příjemka č. {{ directConsumptionData.stockFilledFormId }} a výdejka č.
            {{ directConsumptionData.stockStockReleaseNoteId }}..
        </div>

        <h3 ng-show="initCurrentStockId==null">Nemáte nastavený sklad, kontakujte administrátora.</h3>
        <stock-select-list ng-show="initCurrentStockId!=null" stock-list="stockList" current-stock="currentStock"
            init-current-stock-id="initCurrentStockId" summary-item="summaryItem"
            change-stock-fn="changeStock(newCurrentStock)"></stock-select-list>
        <table class="table" ng-show="initCurrentStockId!=null">
            <thead>
                <tr>
                    <th>Č.</th>
                    <th>Název formuláře</th>
                    <th>Přístupný do</th>
                    <th>Akce</th>
                </tr>
            </thead>
            <tbody>
                <tr ng-repeat="incomePerStock in incomePerStockList">
                    <th scope="row">{{ incomePerStock.order }}</th>
                    <td>{{ incomePerStock.actionName }}</td>
                    <td>{{ incomePerStock.validTo }}</td>
                    <td>
                        <a class="btn btn-success"
                            ng-href="{{ currentStock.stockId && incomePerStock.incomePerStock ? 'incomePerStock.php?formId=' + incomePerStock.formId : '' }}"
                            title="nový příjem" ng-disabled="!canFill() || !incomePerStock.incomePerStock"><span
                                class="glyphicon glyphicon-grain"></span>&nbsp;&nbsp;vyplnit příjem</a>
                        <a class="btn btn-success"
                            ng-href="{{ incomePerStock.directConsumption ? 'formFiller.php?indirectly=1&formId=' + incomePerStock.formId : '' }}"
                            ng-disabled="!incomePerStock.directConsumption" title="přímý odběr"><span
                                class="glyphicon glyphicon-grain"></span>&nbsp;&nbsp;přímý
                            odběr</a>
                    </td>
                </tr>
            </tbody>
        </table>
        <hr>
        <footer>
            <p>{copyright}</p>
        </footer>
    </div> <!-- /container -->
</body>

</html>
