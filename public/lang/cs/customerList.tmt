<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="description" content="">
    <meta name="author" content="">
    <link rel="icon" href="../../favicon.ico">
    <title>Potravinová banka</title>
    <!-- Bootstrap core CSS -->
    <link href="bootstrap/css/bootstrap.min.css?hpbankaversion={hpBankaVersion}" rel="stylesheet">
    <!-- Custom styles for this template -->
    <link href="style/hpbanka.css?hpbankaversion={hpBankaVersion}" rel="stylesheet">
    <!-- HTML5 shim and Respond.js for IE8 support of HTML5 elements and media queries -->
    <!--[if lt IE 9]>
      <script src="https://oss.maxcdn.com/html5shiv/3.7.2/html5shiv.min.js?hpbankaversion={hpBankaVersion}"></script>
      <script src="https://oss.maxcdn.com/respond/1.4.2/respond.min.js?hpbankaversion={hpBankaVersion}"></script>
    <![endif]-->
    <script src="js/angular/angular.min.js?hpbankaversion={hpBankaVersion}"></script>
    <script src="js/jquery.min.js?hpbankaversion={hpBankaVersion}"></script>
    <script src="bootstrap/js/bootstrap.min.js?hpbankaversion={hpBankaVersion}"></script>
    <script type="text/javascript" src="hpBankaMenu.js?hpbankaversion={hpBankaVersion}"></script>
    <script type="text/javascript" src="customerList.js?hpbankaversion={hpBankaVersion}"></script>
    <script>
        $(function () {
            $('[data-toggle="tooltip"]').tooltip({container: 'body'})
        })
    </script>
</head>

<body ng-app="PBanka" ng-controller="CustomerListController"
    ng-init="basicInformation={basicInformationData}; rightList={rightListData}; customerList={customerListData}; isSuperAdmin={isLoggedUserSuperAdmin};">
    <div>
        <div class="container">
            <img src="images/logo_hpbanka.png" alt="Potravinová banka" id="logoHpBanka">
            <img src="images/logo/{logoName}" alt="Potravinová banka" id="logoPBanka">
            <div id="namePBanka">{pbName}</div>
        </div>
    </div>
    <hp-banka-admin-menu ng-init="activeMainMenu='customerList';"></hp-banka-admin-menu>
    <div class="container">
        <form method="post" name="formCustomer" action="customerList.php" novalidate>
            <br>
            <table ng-show="!showEdit" class="table" ng-init="filterList={filterListData};">
                <thead>
                    <tr>
                        <th>Č.</th>
                        <th><a class="sord" ng-click="order('name')">Název <span ng-show="sordBy === 'name'"
                                    class="glyphicon"
                                    ng-class="{true:' glyphicon-menu-up', false:' glyphicon-menu-down'}[reverse]"></span></a>
                        </th>
                        <th nowrap="nowrap">Kontaktní osoba</th>
                        <th>Email</th>
                        <th>Telefon</th>
                        <th>Organizace</th>
                        <th nowrap><a class="sord" ng-click="order('login')">Přihlašovací jméno <span
                                    ng-show="sordBy === 'login'" class="glyphicon"
                                    ng-class="{true:' glyphicon-menu-up', false:' glyphicon-menu-down'}[reverse]"></span></a>
                        </th>
                        <th>Akce</th>
                    </tr>
                    <tr ng-if="rightList.customerList.fullAccess" class="button">
                        <td colspan="7"></td>
                        <td class="text-right">
                            <a class="btn btn-success" href="customersList2PDF.php" title="čárové kódy"
                                target="_blank"><span class="glyphicon glyphicon-barcode"></span>&nbsp;&nbsp;čárové
                                kódy</a>
                            <button type="button" name="addCustomer" class="btn btn-warning"
                                ng-click="addCustomer();"><span
                                    class="glyphicon glyphicon-plus"></span>&nbsp;&nbsp;přidat</button>
                        </td>
                    </tr>
                    <tr>
                        <td></td>
                        <td><input type="text" size="8" name="filterByName" ng-model="filterList.name"></td>
                        <td><input type="text" size="8" name="filterByContactPerson"
                                ng-model="filterList.contactPerson"></td>
                        <td></td>
                        <td></td>
                        <td><input type="text" size="8" name="filterByGroupName" ng-model="filterList.groupName"></td>
                        <td><input type="text" size="8" name="filterByLogin" ng-model="filterList.login"></td>
                        <td nowrap="nowrap">
                            <button name="filter" class="btn btn-warning" title="vybrat"><span
                                    class="glyphicon glyphicon-filter"></span>&nbsp;&nbsp;vybrat</button>
                            <a href="customerList.php" class="btn btn-warning" title="zrušit filtr"><span
                                    class="glyphicon glyphicon-erase"></span>&nbsp;&nbsp;zrušit filtr</a>
                        </td>
                    </tr>
                </thead>

                <tbody>
                    <tr ng-repeat="customer in customerList | orderBy:sordBy:reverse">
                        <th scope="row">{{ $index + 1 }}</th>
                        <td data-toggle="tooltip" data-placement="right" title="Adresa: {{ customer.address }}">{{
                            customer.name }}</td>
                        <td>{{ customer.contactPerson }}</td>
                        <td>{{ customer.email }}</td>
                        <td>{{ customer.phone }}</td>
                        <td>{{ customer.groupName }}</td>
                        <td>{{ customer.login }}</td>
                        <td nowrap>
                            <a ng-show="rightList.customerList.fullAccess && !customer.archived" class="btn btn-success"
                                href="#" title="upravit" ng-click="editCustomer(customer.customerId);"><span
                                    class="glyphicon glyphicon-edit"></span>&nbsp;&nbsp;upravit</a>
                            <a ng-show="!rightList.customerList.fullAccess && !customer.archived"
                                class="btn btn-success" href="#" title="zobrazit"
                                ng-click="showCustomer(customer.customerId);"><span
                                    class="glyphicon glyphicon-eye-open"></span>&nbsp;&nbsp;zobrazit</a>
                            <a ng-if="rightList.customerList.fullAccess && !customer.archived" class="btn btn-success"
                                href="#" title="změna hesla" ng-click="editPassword(customer.customerId);"><span
                                    class="glyphicon glyphicon-edit"></span>&nbsp;&nbsp;změna hesla</a>
                            <a ng-if="rightList.customerList.fullAccess && !customer.usedItem && !customer.archived"
                                class="btn btn-success"
                                href="customerList.php?customerId={{ customer.customerId }}&delete=1"
                                title="smazat"><span class="glyphicon glyphicon-trash"></span>&nbsp;&nbsp;smazat</a>
                            <a ng-if="rightList.customerList.fullAccess && !customer.archived" class="btn btn-success"
                                href="#" title="archivovat"
                                ng-click="customerArchive(customer.customerId); customer.archived=true;"><span
                                    class="glyphicon glyphicon-save"></span>&nbsp;&nbsp;archivovat</a>
                            <a ng-if="rightList.customerList.fullAccess && customer.archived" class="btn btn-success"
                                href="#" title="obnovit z archivu"
                                ng-click="customerUnArchive(customer.customerId); customer.archived=false;"><span
                                    class="glyphicon glyphicon-open"></span>&nbsp;&nbsp;obnovit z archívu</a>
                        </td>
                    </tr>
                    <tr ng-if="rightList.customerList.fullAccess">
                        <td colspan="7"></td>
                        <td class="text-right">
                            <a class="btn btn-success" href="customersList2PDF.php" title="čárové kódy"
                                target="_blank"><span class="glyphicon glyphicon-barcode"></span>&nbsp;&nbsp;čárové
                                kódy</a>
                            <button type="button" name="addCustomer" class="btn btn-warning"
                                ng-click="addCustomer();"><span
                                    class="glyphicon glyphicon-plus"></span>&nbsp;&nbsp;přidat</button>
                        </td>
                    </tr>
                </tbody>
            </table>
        </form>
        <form ng-show="showEdit" method="post" action="customerList.php" name="customerListForm" novalidate
            class="form-horizontal">
            <div class="form-group">
                <label for="inputName" class="col-sm-2 control-label">Název</label>
                <div class="col-sm-8">
                    <input ng-disabled="!canEdit();" type="text" name="name" class="form-control" id="inputName"
                        placeholder="Název" ng-model="customer.name">
                </div>
            </div>
            <div class="form-group">
                <label for="inputContactPerson" class="col-sm-2 control-label">Kontaktní osoba</label>
                <div class="col-sm-8">
                    <input ng-disabled="!canEdit();" type="text" name="contactPerson" class="form-control"
                        id="inputContactPerson" placeholder="Kontaktní osoba" ng-model="customer.contactPerson">
                </div>
            </div>
            <div class="form-group">
                <label for="inputAddress" class="col-sm-2 control-label">Adresa</label>
                <div class="col-sm-8">
                    <input ng-disabled="!canEdit();" type="text" name="address" class="form-control" id="inputAddress"
                        placeholder="Adresa" ng-model="customer.address">
                </div>
            </div>
            <div class="form-group">
                <label for="inputEmail" class="col-sm-2 control-label">Email&nbsp;<span
                        class="glyphicon glyphicon-star"></span></label>
                <div class="col-sm-8"
                    ng-class="{true:'has-error', false:''}[(customerListForm.email.$error.required || customerListForm.email.$error.email) && !customerListForm.email.$pristine]">
                    <input ng-disabled="!canEdit();" type="email" name="email" class="form-control" id="inputEmail"
                        placeholder="Email" ng-model="customer.email" required>
                    <label class="control-label" for="inputEmail"
                        ng-show="customerListForm.email.$error.required && !customerListForm.email.$pristine">Tento údaj
                        je povinný.</label>
                    <label class="control-label" for="inputEmail"
                        ng-show="customerListForm.email.$error.email && !customerListForm.email.$pristine">Prosím,
                        zadejte platný formát emailu.</label>
                </div>
            </div>
            <div class="form-group">
                <label for="inputPhone" class="col-sm-2 control-label">Telefon</label>
                <div class="col-sm-8">
                    <input ng-disabled="!canEdit();" type="text" name="phone" class="form-control" id="inputPhone"
                        placeholder="Telefon" ng-model="customer.phone">
                </div>
            </div>
            <div class="form-group">
                <label for="inputGroupName" class="col-sm-2 control-label">Organizace</label>
                <div class="col-sm-8">
                    <input ng-disabled="!canEdit();" type="text" name="groupName" class="form-control"
                        id="inputGroupName" placeholder="Organizace" ng-model="customer.groupName">
                </div>
            </div>
            <div class="form-group">
                <label for="inputIC" class="col-sm-2 control-label">IČ</label>
                <div class="col-sm-8">
                    <input ng-disabled="!canEdit();" type="text" name="IC" class="form-control" id="inputIC"
                        placeholder="IČ" ng-model="customer.IC">
                </div>
            </div>
            <div class="form-group">
                <label for="inputLogin" class="col-sm-2 control-label">Přihlašovací jméno&nbsp;<span
                        class="glyphicon glyphicon-star"></span></label>
                <div class="col-sm-8"
                    ng-class="{true:'has-error', false:''}[(customerListForm.login.$error.required || customerListForm.login.$error.username) && !customerListForm.login.$pristine]">
                    <input ng-disabled="!canEdit();" type="text" name="login" class="form-control" id="inputLogin"
                        placeholder="Přihlašovací jméno" username ng-model="customer.login" required>
                    <label class="control-label" for="inputLogin"
                        ng-show="customerListForm.login.$error.required && !customerListForm.login.$pristine">Tento údaj
                        je povinný.</label>
                    <label class="control-label" for="inputLogin"
                        ng-show="customerListForm.login.$error.username && !customerListForm.login.$pristine">Přihlašovací
                        jméno nelze použít.</label>
                </div>
            </div>

            <div ng-if="customer.customerId===0 || showPassword">
                <div class="form-group">
                    <label for="inputPassword" class="col-sm-2 control-label"> Heslo&nbsp;<span
                            class="glyphicon glyphicon-star"></span></label>
                    <div class="col-sm-8"
                        ng-class="{true:'has-error', false:''}[(customerListForm.password.$error.required || customerListForm.password.$error.minlength || customerListForm.password.$error.sameasusername) && !customerListForm.password.$pristine]">
                        <input ng-disabled="!canEdit();" type="password" name="password" class="form-control"
                            id="inputPassword" placeholder="Heslo" minlength="5" sameasusername
                            ng-model="customer.password" required>
                        <label class="control-label" for="inputPassword"
                            ng-show="customerListForm.password.$error.required && !customerListForm.password.$pristine">Tento
                            údaj je povinný.</label>
                        <label class="control-label" for="inputPassword"
                            ng-show="customerListForm.password.$error.minlength && !customerListForm.password.$pristine">Heslo
                            je příliš krátké.</label>
                        <label class="control-label" for="inputPassword"
                            ng-show="customerListForm.password.$error.sameasusername && !customerListForm.password.$pristine">Heslo
                            nesmí být stejné jako přihlašovací jméno.</label>
                        <span id="helpBlock" class="help-block">Heslo nesmí být kratší než 5 znaků nebo stejné jako
                            přihlašovací jméno.</span>
                    </div>
                </div>
                <div class="form-group">
                    <label for="inputPassword2" class="col-sm-2 control-label">Zopakujte heslo&nbsp;<span
                            class="glyphicon glyphicon-star"></span></label>
                    <div class="col-sm-8"
                        ng-class="{true:'has-error', false:''}[(customerListForm.password2.$error.required || customerListForm.password2.$error.pattern) && !customerListForm.password2.$pristine]">
                        <input ng-disabled="!canEdit();" type="password" name="password2" class="form-control"
                            id="inputPassword2" placeholder="Heslo" ng-model="password2" ng-pattern="customer.password"
                            required>
                        <label class="control-label" for="inputPassword2"
                            ng-show="customerListForm.password2.$error.required && !customerListForm.password2.$pristine">Tento
                            údaj je povinný.</label>
                        <label class="control-label" for="inputPassword2"
                            ng-show="customerListForm.password2.$error.pattern && !customerListForm.password2.$pristine">Hesla
                            se neshodují.</label>
                    </div>
                </div>
                <div class="form-group" ng-if="isSuperAdmin">
                    <label for="companyReporter" class="col-sm-2 control-label">Reportér organizace</label>
                    <div class="col-sm-2">
                        <label class="checkbox-inline" for="companyReporter">
                            <input type="checkbox" name="companyReporter" id="companyReporter"
                                ng-model="companyReporter"> &nbsp;
                        </label>
                    </div>
                </div>
            </div>
            <div class="col-sm-10 text-right"><span class="glyphicon glyphicon-star"></span> Povinné údaje.</div>
            <div class="form-group">
                <div class="col-sm-offset-2 col-sm-8">
                    <button ng-if="rightList.customerList.fullAccess" type="submit" name="save" class="btn btn-success"
                        title="upravit" ng-disabled="!canSave();"><span
                            class="glyphicon glyphicon-save"></span>&nbsp;&nbsp;uložit</button>
                    <a class="btn btn-success" title="zpět na seznam" ng-click="showEdit=false;"><span
                            class="glyphicon glyphicon-hand-left"></span>&nbsp;&nbsp;zpět na seznam</a>
                </div>
            </div>
            <input type="hidden" name="customerId" ng-model="customer.customerId" value="{{ customer.customerId }}" />
            <input type="hidden" name="appUserId" ng-model="customer.appUserId" value="{{ customer.appUserId }}" />
        </form>
        <hr>
        <footer>
            <p>{copyright}</p>
        </footer>
    </div> <!-- /container -->
</body>

</html>