<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="description" content="">
    <meta name="author" content="">
    <link rel="icon" href="../../favicon.ico">
    <title>Potravinová banka</title>
    <!-- Bootstrap core CSS -->
    <link href="bootstrap/css/bootstrap.min.css?hpbankaversion={hpBankaVersion}" rel="stylesheet">
    <link href="bootstrap-additions/bootstrap-additions.min.css?hpbankaversion={hpBankaVersion}" rel="stylesheet">
    <link href="bootstrap-select/css/bootstrap-select.min.css?hpbankaversion={hpBankaVersion}" rel="stylesheet">
    <!-- Custom styles for this template -->
    <link href="style/hpbanka.css?hpbankaversion={hpBankaVersion}" rel="stylesheet">
    <!-- HTML5 shim and Respond.js for IE8 support of HTML5 elements and media queries -->
    <!--[if lt IE 9]>
      <script src="https://oss.maxcdn.com/html5shiv/3.7.2/html5shiv.min.js?hpbankaversion={hpBankaVersion}"></script>
      <script src="https://oss.maxcdn.com/respond/1.4.2/respond.min.js?hpbankaversion={hpBankaVersion}"></script>
    <![endif]-->
    <script src="js/jquery.min.js?hpbankaversion={hpBankaVersion}"></script>
    <script src="bootstrap/js/bootstrap.min.js?hpbankaversion={hpBankaVersion}"></script>
    <script src="js/angular/angular.min.js?hpbankaversion={hpBankaVersion}"></script>
    <script src="js/angular/i18n/angular-locale_cs-cz.js?hpbankaversion={hpBankaVersion}"></script>

    <script src="bootstrap-select/js/bootstrap-select.min.js?hpbankaversion=={hpBankaVersion}"></script>
    <script src="bootstrap-select/js/i18n/defaults-cs_CZ.min.js?hpbankaversion=={hpBankaVersion}"></script>
    <script src="js/angular-strap/angular-strap.min.js?hpbankaversion={hpBankaVersion}"></script>
    <script src="js/angular-strap/angular-strap.tpl.min.js?hpbankaversion={hpBankaVersion}"></script>
    <script type="text/javascript" src="hpBanka.js?hpbankaversion={hpBankaVersion}"></script>
    <script type="text/javascript" src="hpBankaMenu.js?hpbankaversion={hpBankaVersion}"></script>
    <script type="text/javascript" src="js/angular/selectPicker.js?hpbankaversion={hpBankaVersion}"></script>
    <script type="text/javascript" src="commodityAmount.js?hpbankaversion={hpBankaVersion}"></script>
    <script type="text/javascript" src="numberLimits.js?hpbankaversion={hpBankaVersion}"></script>
    <script type="text/javascript" src="stockSelectList.js?hpbankaversion={hpBankaVersion}"></script>
    <script type="text/javascript" src="stockReleaseNoteList.js?hpbankaversion={hpBankaVersion}"></script>
    <script>
        $(function () {
            $('[data-toggle="tooltip"]').tooltip({container: 'body'})
        })
    </script>
</head>

<body ng-app="PBanka" ng-controller="StockReleaseNoteListController"
    ng-init="basicInformation={basicInformationData}; rightList={rightListData}; stockReleaseNoteList={stockReleaseNoteListData};">
    <div>
        <div class="container">
            <img src="images/logo_hpbanka.png" alt="Potravinová banka" id="logoHpBanka">
            <img src="images/logo/{logoName}" alt="Potravinová banka" id="logoPBanka">
            <div id="namePBanka">{pbName}</div>
        </div>
    </div>
    <hp-banka-admin-menu ng-init="activeMainMenu='stock'; activeSubMenu='stockReleaseNoteList'; showSubmenuStock=true;">
    </hp-banka-admin-menu>

    <div ng-show="showStockReleaseNoteList" class="container"
        ng-init="stockList={stockListData}; initCurrentStockId={currentStockId};currentStock=[];summaryItem={summaryItem}; showFirstStockReleaseNote({stockReleaseNoteId})">
        <h3 ng-show="initCurrentStockId==null">Nemáte nastavený sklad, kontakujte administrátora.</h3>
        <stock-select-list ng-show="!showEdit && !showInfoAboutCreateDonationAgreement && initCurrentStockId!=null"
            stock-list="stockList" current-stock="currentStock" init-current-stock-id="initCurrentStockId"
            summary-item="summaryItem" change-stock-fn="changeStock(newCurrentStock)"></stock-select-list>
        <br />
        <div ng-hide="showEdit" class="well well-sm"><strong>Množství celkem: {{ totalAmount | number:3 }} kg.</strong>
        </div>
        <div ng-show="showInfoAboutCreateDonationAgreement"
            ng-init="showInfoAboutCreateDonationAgreement={showInfoAboutCreateDonationAgreement}" class="container">
            <h2>Darovací smlouvy byly vytvořeny</h2>
            <p>&nbsp;</p>
            <p>Nyní můžete přejít na <a href="donationAgreementList.php">přehled darovacích smluv</a>.</p>
        </div>
        <form ng-show="!showEdit && !showInfoAboutCreateDonationAgreement && initCurrentStockId!=null" method="post"
            action="stockReleaseNoteList.php" novalidate>
            <table class="table" ng-init="filterList={filterListData};">
                <thead>
                    <tr>
                        <th colspan="2">Číslo výdejky /<br>číslo příjemky</th>
                        <th>Odběratel</th>
                        <th>Název formuláře</th>
                        <th>Vyskladněno dne</th>
                        <th class="text-right">Množství v kg</th>
                        <th></th>
                        <th>Akce</th>
                    </tr>
                    <tr>

                        <td colspan="2"><input type="text" size="5" name="filterByStockStockReleaseNoteId"
                                ng-model="filterList.stockStockReleaseNoteId"></td>
                        <td><input type="text" name="filterByCustomer" ng-model="filterList.customer"></td>
                        <td><input type="text" size="5" name="filterByActionName" ng-model="filterList.actionName"></td>
                        <td nowrap="nowrap"><input type="text" size="10" name="filterByDateFrom"
                                ng-model="filterList.dateFrom" bs-datepicker>&nbsp;/&nbsp;<input type="text" size="10"
                                name="filterByDateTo" ng-model="filterList.dateTo" bs-datepicker></td>
                        <td nowrap="nowrap" title="Výdejky, které nejsou zahrnuty na žádné darovací smlouvě."><input
                                ng-model="filterList.withoutDonationAgreement" type="checkbox"
                                name="withoutDonationAgreement" />
                            <label for="withoutDonationAgreement">bez smlouvy</label>
                        </td>
                        <td></td>
                        <td nowrap="nowrap">
                            <button name="filter" class="btn btn-warning" title="vybrat"><span
                                    class="glyphicon glyphicon-filter"></span>&nbsp;&nbsp;vybrat</button>
                            <a href="stockReleaseNoteList.php" class="btn btn-warning" title="zrušit výběr"><span
                                    class="glyphicon glyphicon-erase"></span>&nbsp;&nbsp;výchozí období</a>
                        </td>
                    </tr>
                </thead>
                <tbody>
                    <tr ng-repeat="stockReleaseNote in stockReleaseNoteList" ng-init="countTotalItems();">
                        <td><span ng-show="stockReleaseNote.isNote" data-toggle="tooltip" data-placement="top"
                                title="{{ stockReleaseNote.note }}" class="glyphicon glyphicon-comment"></span></td>
                        <td nowrap="nowrap" scope="row">{{ stockReleaseNote.stockStockReleaseNoteId }} / {{
                            stockReleaseNote.stockFilledFormId }}</td>
                        <td ng-click="filterList.customer=stockReleaseNote.name" role="button"><span
                                class="glyphicon glyphicon-copy"></span> {{ stockReleaseNote.name }}</td>
                        <td>{{ stockReleaseNote.actionName }}</td>
                        <td>{{ stockReleaseNote.issuedAt | date: fullDate }}</td>
                        <td class="text-right">{{ stockReleaseNote.totalAmount | number:3 }}</td>
                        <td></td>
                        <td nowrap="nowrap">
                            <a ng-if="stockReleaseNote.stockReleaseNoteId > 0" class="btn btn-success" target="_blank"
                                href="stockReleaseNote2PDF.php?stockReleaseNoteId={{ stockReleaseNote.stockReleaseNoteId }}"
                                title="tisk" role="button"><span
                                    class="glyphicon glyphicon-print"></span>&nbsp;&nbsp;tisk výdejky</a>
                            <a ng-if="stockReleaseNote.donationAgreementId > 0" class="btn btn-success" target="_blank"
                                href="donationAgreement2PDF.php?donationAgreementId={{ stockReleaseNote.donationAgreementId }}"
                                title="tisk" role="button"><span
                                    class="glyphicon glyphicon-print"></span>&nbsp;&nbsp;tisk smlouvy</a>
                            <a ng-if="stockReleaseNote.donationAgreementId <= 0 && rightList.stockReleaseNoteList.fullAccess"
                                class="btn btn-success" href="#" ng-disabled="stockReleaseNote.isCustomerArchived"
                                title="upravit"
                                ng-click="editStockReleaseNote(stockReleaseNote.isCustomerArchived, stockReleaseNote.stockReleaseNoteId);"><span
                                    class="glyphicon glyphicon-edit"></span>&nbsp;&nbsp;upravit</a>
                            <a ng-if="stockReleaseNote.donationAgreementId <= 0 && !rightList.stockReleaseNoteList.fullAccess"
                                class="btn btn-success" href="#" title="zobrazit"
                                ng-click="showStockReleaseNote(stockReleaseNote.isCustomerArchived,stockReleaseNote.stockReleaseNoteId);"><span
                                    class="glyphicon glyphicon-eye-open"></span>&nbsp;&nbsp;zobrazit</a>
                            <a ng-if="rightList.stockReleaseNoteList.fullAccess && stockReleaseNote.isDirectConsumption && stockReleaseNote.donationAgreementId <= 0"
                                class="btn btn-success" ng-disabled="stockReleaseNote.isCustomerArchived"
                                ng-href="{{ stockReleaseNote.isCustomerArchived ? '' : 'receiptToStockList.php?filledFormId=' + stockReleaseNote.filledFormId }}"
                                title="příjemka"><span class="glyphicon glyphicon-edit"></span>&nbsp;&nbsp;příjemka</a>
                        </td>
                    </tr>
                    <tr>
                        <td colspan="2"></td>
                        <td colspan="5"><textarea placeholder="Poznámka do darovací smlouvy" class="form-control"
                                rows="1" name="note" maxlength="30"></textarea></td>
                        <td nowrap="nowrap">
                            <input type="hidden" name="stockReleaseNoteList" value="{{ stockReleaseNoteList }}" />
                            <input type="hidden" name="filterList" value="{{ filterList }}" />
                            <button ng-if="rightList.stockReleaseNoteList.fullAccess" name="createDonationAgreement"
                                class="btn btn-warning" title="vybrat" ng-disabled="canCreateDonationAreement();"><span
                                    class="glyphicon glyphicon-plus"></span>&nbsp;&nbsp;vytvořit darovací
                                smlouvu</button>
                        </td>
                    </tr>
                </tbody>
            </table>
        </form>
        <form ng-show="showEdit" method="post" name="stockReleaseNoteForm" novalidate class="form-horizontal">
            <h2>Výdej ze skladu: {{ receivedStockReleaseNote.actionName }}</h2>
            <table class="form-filler-caption">
                <tr>
                    <td colspan="2"><label>Název skladu: {{ receivedStockReleaseNote.nameStock}}</label></td>
                </tr>
                <tr>
                    <td><label for="issuedAt">Vyskladněno dne&nbsp;<span
                                class="glyphicon glyphicon-star"></span></label>&nbsp;&nbsp;&nbsp;</td>
                    <td
                        ng-class="{true:'has-error', false:''}[(stockReleaseNoteForm.issuedAt.$error.required || stockReleaseNoteForm.issuedAt.$error.date) && !stockReleaseNoteForm.issuedAt.$pristine]">
                        <input ng-disabled="!canEdit();" name="issuedAt" type="text" class="form-control" id="issuedAt"
                            ng-model="receivedStockReleaseNote.issuedAt" bs-datepicker required>
                        <label class="control-label" for=""
                            ng-show="stockReleaseNoteForm.issuedAt.$error.required && !stockReleaseNoteForm.issuedAt.$pristine">Tento
                            údaj je povinný.</label>
                        <label class="control-label" for=""
                            ng-show="stockReleaseNoteForm.issuedAt.$error.date && !stockReleaseNoteForm.issuedAt.$error.min && !stockReleaseNoteForm.issuedAt.$pristine">Zadejte
                            datum ve formátu DD.MM.YYYY.</label>
                    </td>
                    <td class="col-sm-1"></td>
                    <td><label for="customer">Odběratel&nbsp;<span
                                class="glyphicon glyphicon-star"></span></label>&nbsp;&nbsp;&nbsp;</td>
                    <td>

                        <select ng-disabled="!canEdit();" class="form-control selectpicker" data-live-search="true"
                            name="customerId" ng-model="current.customer"
                            ng-options="customer as customer.name for customer in customerList track by customer.customerId"
                            select-picker></select>
                    </td>
                </tr>
                <tr>
                    <td><label for="note">Poznámka</label></td>
                    <td><textarea class="form-control" rows="2" ng-model="receivedStockReleaseNote.note"
                            maxlength="30"></textarea></td>
                </tr>
            </table>
            <h4>Uveďte množství odebraných komodit:</h4>
            <div class="form-group" ng-repeat="commodity in commodityList">
                <commodity-amount commodity="commodity" form="stockReleaseNoteForm" id="$index + 1"
                    count-amount-fn="countAmount()"></commodity-amount>
            </div>
            <div class="col-sm-7 text-right"><span class="glyphicon glyphicon-star"></span> Povinné údaje.</div>
            <div class="col-sm-5 text-right">&nbsp;</div>
            <h5>Hmotnost vydaného zboží celkem: <b>{{ receivedStockReleaseNote.formTotalAmount | number:3 }} kg</b>.
            </h5>

            <div class="form-group">
                <div class="col-sm-5">
                    <button ng-if="rightList.stockReleaseNoteList.fullAccess" name="save" ng-click="saveReleaseNote();"
                        class="btn btn-success" title="uložit" ng-disabled="!canSave();"><span
                            class="glyphicon glyphicon-save"></span>&nbsp;&nbsp;Uložit</button>
                    <a class="btn btn-success" title="zpět na seznam" ng-click="showEdit=false;"><span
                            class="glyphicon glyphicon-hand-left"></span>&nbsp;&nbsp;zpět na seznam</a>
                </div>
            </div>

            <input type="hidden" name="receivedStockReleaseNote" value="{{ receivedStockReleaseNote }}" />
            <input type="hidden" name="commodityList" value="{{ commodityList  }}" />
        </form>
        <hr>
        <footer>
            <p>{{ basicInformation.copyright }}</p>
        </footer>
    </div>
</body>

</html>