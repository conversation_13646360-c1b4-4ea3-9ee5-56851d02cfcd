<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="description" content="">
    <meta name="author" content="">
    <link rel="icon" href="../../favicon.ico">
    <title>Potravinová banka</title>
    <!-- Bootstrap core CSS -->
    <link href="bootstrap/css/bootstrap.min.css?hpbankaversion={hpBankaVersion}" rel="stylesheet">
    <!-- Custom styles for this template -->
    <link href="style/hpbanka.css?hpbankaversion={hpBankaVersion}" rel="stylesheet"> 
    <!-- HTML5 shim and Respond.js for IE8 support of HTML5 elements and media queries -->
    <!--[if lt IE 9]>
      <script src="https://oss.maxcdn.com/html5shiv/3.7.2/html5shiv.min.js?hpbankaversion={hpBankaVersion}"></script>
      <script src="https://oss.maxcdn.com/respond/1.4.2/respond.min.js?hpbankaversion={hpBankaVersion}"></script>
    <![endif]-->
	  <script type="text/javascript" src="js/jquery.min.js?hpbankaversion={hpBankaVersion}"></script>
	  <script type="text/javascript" src="js/angular/angular.min.js?hpbankaversion={hpBankaVersion}"></script>
	  <script type="text/javascript" src="formSupplierEditor.js?hpbankaversion={hpBankaVersion}"></script>
	  <script type="text/javascript" src="js/jquery-ui.min.js?hpbankaversion={hpBankaVersion}"></script>
	  <script type="text/javascript" src="bootstrap/js/bootstrap.min.js?hpbankaversion={hpBankaVersion}"></script>
	  <script type="text/javascript" src="hpBankaMenu.js?hpbankaversion={hpBankaVersion}"></script>
	  <script type="text/javascript" src="js/angular/sortable.js?hpbankaversion={hpBankaVersion}"></script>    
  </head>

  <body ng-app="PBanka" ng-controller="FormSupplierEditorController" ng-init="basicInformation={basicInformationData}; rightList={rightListData};">
  <div >
    <div class="container">
      <img src="images/logo_hpbanka.png" alt="Potravinová banka">
    </div>
  </div>
  <hp-banka-admin-menu ng-init="activeMainMenu='formList'; activeSubMenu='formList'; showSubmenuSettings=true;"></hp-banka-admin-menu>
  <div class="container">
  <form method="post" action="formSupplierEditor.php" name="formSupplierEditor" novalidate>
  <div class="page-header">
    <h4>Sestavení formuláře</h4>
  </div>
  <div ng-show="showSaveMessage" class="alert alert-success show-message" role="alert">Byla přidána položka.</div>
  <div ng-show="showRemoveMessage" class="alert alert-success show-message" role="alert">Byla smazána položka.</div>
  <div ng-show="showErrorMessage" class="alert alert-danger show-message" role="alert">Nepodařilo se uložit řazení.</div>
  <div class="panel panel-default">
  <!-- Default panel contents -->
  <div class="panel-heading">Název akce / formuláře: {actionName}, platnost od: {validFrom}, platnost do: {validTo}</div>
  <div class="panel-body" ng-init="selectedSuppliers = {selectedSupplierListData}">
    <p>Seznam dodavatelů nabízených ve formuláři</p>
    <div ui-sortable="sortableOption" ng-model="selectedSuppliers">
      <div ng-repeat="supplier in selectedSuppliers" class="form-inline">
	      <fieldset ng-disabled="!canEdit() || supplier.isUsedSupplier">
	        <div ng-show="canEdit();" class="form-group handleForSorting cursor-move"><span class="glyphicon glyphicon-resize-vertical"></span>&nbsp;</div>
	        <div class="checkbox" ng-click="removeSelectedSuppliers(supplier);"><label><input type="checkbox" checked="checked">&nbsp;&nbsp;{{supplier.name}}</label></div>
	      </fieldset>  
	    </div>
	  </div>  
  </div>
  
  <div class="panel-body" ng-init="availableSuppliers = {availableSupplierListData};">
    <p>Přidejte do seznamu z nabídky dostupných dodavatelů</p>
    <div  ng-model="availableSuppliers">
		  <div ng-repeat="supplier in availableSuppliers" class="form-inline">
			  <fieldset ng-disabled="!canEdit()">
			    <div class="checkbox" ng-click="addToSelectedSuppliers(supplier);"><label><input type="checkbox">&nbsp;&nbsp;{{supplier.name}}</label></div>
			  </fieldset>  
		  </div>
	  </div>
  </div>
  </div>
  <button type="submit" name="backFormList" class="btn btn-success" title="upravit"><span class="glyphicon glyphicon-hand-left"></span>&nbsp;&nbsp;zpět na formuláře</button>
  <input type="hidden" name="formList" value="{{ formList }}" />
  </form>
  <hr>
  <footer>
    <p>{copyright}</p>
  </footer>
  </div> <!-- /container -->
  </body>
</html>