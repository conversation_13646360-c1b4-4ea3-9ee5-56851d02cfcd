<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="description" content="">
    <meta name="author" content="">
    <link rel="icon" href="../../favicon.ico">
    <title>Potravinová banka</title>
    <!-- Bootstrap core CSS -->
    <link href="bootstrap/css/bootstrap.min.css?hpbankaversion={hpBankaVersion}" rel="stylesheet">
    <!-- Custom styles for this template -->
    <link href="style/hpbanka.css?hpbankaversion={hpBankaVersion}" rel="stylesheet"> 
    <!-- HTML5 shim and Respond.js for IE8 support of HTML5 elements and media queries -->
    <!--[if lt IE 9]>
      <script src="https://oss.maxcdn.com/html5shiv/3.7.2/html5shiv.min.js?hpbankaversion={hpBankaVersion}"></script>
      <script src="https://oss.maxcdn.com/respond/1.4.2/respond.min.js?hpbankaversion={hpBankaVersion}"></script>
    <![endif]-->
    <script src="js/angular/angular.min.js?hpbankaversion={hpBankaVersion}"></script>
    <script src="js/jquery.min.js?hpbankaversion={hpBankaVersion}"></script>
    <script src="bootstrap/js/bootstrap.min.js?hpbankaversion={hpBankaVersion}"></script>
    <script type="text/javascript" src="hpBankaMenu.js?hpbankaversion={hpBankaVersion}"></script>
    <script type="text/javascript" src="supplierList.js?hpbankaversion={hpBankaVersion}"></script>
  </head>

  <body ng-app="PBanka" ng-controller="SupplierListController" ng-init="basicInformation={basicInformationData}; rightList={rightListData}; supplierList={supplierListData};">
  <div >
    <div class="container">
      <img src="images/logo_hpbanka.png" alt="Potravinová banka" id="logoHpBanka">
      <img src="images/logo/{logoName}" alt="Potravinová banka" id="logoPBanka">
      <div id="namePBanka">{pbName}</div>
    </div>
  </div>
  <hp-banka-admin-menu ng-init="activeMainMenu='supplierList';"></hp-banka-admin-menu>
  <div class="container">
  <br>
  <table ng-show="!showEdit" class="table">
    <thead>
      <tr>
        <th>Č.</th>
        <th><a class="sord" ng-click="order('name')">Název <span ng-show="sordBy === 'name'" class="glyphicon" ng-class="{true:' glyphicon-menu-up', false:' glyphicon-menu-down'}[reverse]" ></span></a></th>
        <th>Kontaktní osoba</th>
        <th>Adresa</th>
        <th>Email</th>
        <th>Telefon</th>
        <th><a class="sord" ng-click="order('groupName')">Organizace <span ng-show="sordBy === 'groupName'" class="glyphicon" ng-class="{true:' glyphicon-menu-up', false:' glyphicon-menu-down'}[reverse]" ></span></a></th>
        <th>Akce</th>
      </tr> 
      <tr class="button" ng-if="rightList.supplierList.fullAccess">
        <td colspan="7"></td>
        <td><button type="submit" name="save" class="btn btn-warning" ng-click="addSupplier();"><span class="glyphicon glyphicon-plus"></span>&nbsp;&nbsp;přidat</button></td> 
      </tr>
    </thead> 
    <tbody> 
    <tr ng-repeat="supplier in supplierList | orderBy:sordBy:reverse">
	    <th scope="row">{{ $index + 1 }}</th>
	    <td>{{ supplier.name }}</td>
	    <td>{{ supplier.contactPerson }}</td>
	    <td>{{ supplier.address }}</td>
	    <td>{{ supplier.email }}</td>
	    <td>{{ supplier.phone }}</td>
	    <td>{{ supplier.groupName }}</td>
	    <td nowrap>
	      <a ng-show="rightList.supplierList.fullAccess" class="btn btn-success" href="#" title="upravit" ng-click="editSupplier(supplier.supplierId);"><span class="glyphicon glyphicon-edit"></span>&nbsp;&nbsp;upravit</a>
	      <a ng-show="!rightList.supplierList.fullAccess" class="btn btn-success" href="#" title="zobrazit" ng-click="showSupplier(supplier.supplierId);"><span class="glyphicon glyphicon-eye-open"></span>&nbsp;&nbsp;zobrazit</a>
	      <a ng-if="rightList.supplierList.fullAccess && !supplier.usedItem" class="btn btn-success" href="supplierList.php?supplierId={{ supplier.supplierId }}&delete=1" title="smazat"><span class="glyphicon glyphicon-trash"></span>&nbsp;&nbsp;smazat</a>
	    </td>
    </tr> 
    <tr ng-if="rightList.supplierList.fullAccess">
    <td colspan="7"></td>
    <td><button type="submit" name="save" class="btn btn-warning" ng-click="addSupplier();"><span class="glyphicon glyphicon-plus"></span>&nbsp;&nbsp;přidat</button></td>
    </tr>
    </tbody> 
  </table>
  
  <form ng-show="showEdit" method="post" action="supplierList.php" name="supplierListForm" novalidate class="form-horizontal">
  <div class="form-group">
    <label for="inputName" class="col-sm-2 control-label">Název</label>
    <div class="col-sm-8">
      <input ng-disabled="!canEdit();" type="text" name="name" class="form-control" id="inputName" placeholder="Název" ng-model="supplier.name">
    </div>
  </div>
  <div class="form-group">
    <label for="inputContactPerson" class="col-sm-2 control-label">Kontaktní osoba</label>
    <div class="col-sm-8">
      <input ng-disabled="!canEdit();" type="text" name="contactPerson" class="form-control" id="inputContactPerson" placeholder="Kontaktní osoba" ng-model="supplier.contactPerson">
    </div>
  </div>
  <div class="form-group">
    <label for="inputAddress" class="col-sm-2 control-label">Adresa</label>
    <div class="col-sm-8">
      <input ng-disabled="!canEdit();" type="text" name="address"class="form-control" id="inputAddress" placeholder="Adresa" ng-model="supplier.address">
    </div>
  </div>
  <div class="form-group">
    <label for="inputEmail" class="col-sm-2 control-label">Email&nbsp;<span class="glyphicon glyphicon-star"></span></label>
    <div class="col-sm-8" ng-class="{true:'has-error', false:''}[(supplierListForm.email.$error.required || supplierListForm.email.$error.email) && !supplierListForm.email.$pristine]">
      <input ng-disabled="!canEdit();" type="email" name="email" class="form-control" id="inputEmail" placeholder="Email" ng-model="supplier.email" required>
      <label class="control-label" for="inputEmail" ng-show="supplierListForm.email.$error.required && !supplierListForm.email.$pristine">Tento údaj je povinný.</label>
      <label class="control-label" for="inputEmail" ng-show="supplierListForm.email.$error.email && !supplierListForm.email.$pristine">Prosím, zadejte platný formát emailu.</label>
    </div>
  </div>
  <div class="form-group">
    <label for="inputPhone" class="col-sm-2 control-label">Telefon</label>
    <div class="col-sm-8">
      <input ng-disabled="!canEdit();" type="text" name="phone" class="form-control" id="inputPhone" placeholder="Telefon" ng-model="supplier.phone">
    </div>
  </div>
  <div class="form-group">
    <label for="inputGroupName" class="col-sm-2 control-label">Organizace</label>
    <div class="col-sm-8">
      <input ng-disabled="!canEdit();" type="text" name="groupName" class="form-control" id="inputGroupName" placeholder="Organizace" ng-model="supplier.groupName">
    </div>
  </div>
  <div class="form-group">
    <label for="inputIC" class="col-sm-2 control-label">IČ</label>
    <div class="col-sm-8">
      <input ng-disabled="!canEdit();" type="text" name="IC" class="form-control" id="inputIC" placeholder="IČ" ng-model="supplier.IC">
    </div>
  </div>
  
  <div class="col-sm-10 text-right"><span class="glyphicon glyphicon-star"></span> Povinné údaje.</div>
  
  <div class="form-group">
    <div class="col-sm-offset-2 col-sm-8">
      <button ng-if="rightList.supplierList.fullAccess" type="submit" name="save" class="btn btn-success" title="upravit" ng-disabled="!canSave();"><span class="glyphicon glyphicon-save"></span>&nbsp;&nbsp;uložit</button>
      <a class="btn btn-success" title="zpět na seznam" ng-click="showEdit=false;"><span class="glyphicon glyphicon-hand-left"></span>&nbsp;&nbsp;zpět na seznam</a>
    </div>
  </div>
  <input type="hidden" name="supplierId" ng-model="supplier.supplierId" value="{{ supplier.supplierId }}"/>
  </form>
  <hr>
  <footer>
    <p>{copyright}</p>
  </footer>
  </div> <!-- /container -->
  </body>
</html>