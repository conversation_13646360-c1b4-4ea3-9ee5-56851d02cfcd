<div class="row">
  <div class="col-sm-7">
    <label class="col-sm-6 control-label text-left" for="amount{{ id }}">{{ commodity.code }} &nbsp; {{ commodity.name }}</label>
		<div  class="input-group text-left" ng-class="{true:'has-error', false:''}[(form.amount{{ id }}.$error.pattern || form.amount{{ id }}.$error.ngMax) && !form.amount{{ id }}.$pristine]">
		  <input type="text" class="form-control" name="amount{{ id }}" id="amount{{ id }}" placeholder="max. {{ commodity.availableAmount | number: 3 }}" ng-change="count();" ng-pattern="regNumber" ng-max="{{ commodity.availableAmount }}" ng-min="{{ commodity.minTransferAmount }}" ng-model="commodity.amount">
		  <span class="input-group-addon">{{ commodity.unit }}</span>
		</div>
  </div>
  <div class="col-sm-5" ng-show="!commodity.minTransferAmount">maximum k převodu: {{ commodity.availableAmount | number: 3 }} {{ commodity.unit }} za cenu: {{ commodity.pricePerUnit / 1000 | number:2 }} {{ commodity.currencySymbol }} / {{ commodity.unit }}</div>
  <div class="col-sm-5" ng-show="commodity.minTransferAmount">převod v rozsahu: {{ commodity.minTransferAmount | number: 3  }} - {{ commodity.availableAmount | number: 3 }} {{ commodity.unit }} za cenu: {{ commodity.pricePerUnit / 1000 | number:2 }} {{ commodity.currencySymbol }} / {{ commodity.unit }}</div>
  <strong class="col-sm-7 control-label text-right text-danger" ng-show="form.amount{{ id }}.$error.pattern">Prosím, zadejte kladné číslo.</strong>
  <strong class="col-sm-7 control-label text-right text-danger" ng-show="form.amount{{ id }}.$error.ngMax">Není možné převést více než je na skladě.</strong>
  <strong class="col-sm-7 control-label text-right text-danger" ng-show="form.amount{{ id }}.$error.ngMin">Není možné převést méně než bylo převedeno na jiný sklad.</strong>
</div>
