<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="description" content="">
    <meta name="author" content="">
    <link rel="icon" href="../../favicon.ico">
    <title>Potravinová banka</title>
    <!-- Bootstrap core CSS -->
    <link href="bootstrap/css/bootstrap.min.css?hpbankaversion={hpBankaVersion}" rel="stylesheet">
    <link href="bootstrap-additions/bootstrap-additions.min.css?hpbankaversion={hpBankaVersion}" rel="stylesheet">
    <!-- Custom styles for this template -->
    <link href="style/hpbanka.css?hpbankaversion={hpBankaVersion}" rel="stylesheet"> 
    <!-- HTML5 shim and Respond.js for IE8 support of HTML5 elements and media queries -->
    <!--[if lt IE 9]>
      <script src="https://oss.maxcdn.com/html5shiv/3.7.2/html5shiv.min.js?hpbankaversion={hpBankaVersion}"></script>
      <script src="https://oss.maxcdn.com/respond/1.4.2/respond.min.js?hpbankaversion={hpBankaVersion}"></script>
    <![endif]-->
    <script src="js/angular/angular.min.js?hpbankaversion={hpBankaVersion}"></script>
    <script src="js/angular/i18n/angular-locale_cs-cz.js?hpbankaversion={hpBankaVersion}"></script>
    <script src="js/jquery.min.js?hpbankaversion={hpBankaVersion}"></script>
    <script src="bootstrap/js/bootstrap.min.js?hpbankaversion={hpBankaVersion}"></script> 
    <script src="js/angular-strap/angular-strap.min.js?hpbankaversion={hpBankaVersion}"></script>
    <script src="js/angular-strap/angular-strap.tpl.min.js?hpbankaversion={hpBankaVersion}"></script>
    <script type="text/javascript" src="hpBanka.js?hpbankaversion={hpBankaVersion}"></script> 
    <script type="text/javascript" src="hpBankaMenu.js?hpbankaversion={hpBankaVersion}"></script>    
    <script type="text/javascript" src="groupOfSupportedPersonList.js?hpbankaversion={hpBankaVersion}"></script>
  </head>

  <body ng-app="PBanka" ng-controller="GroupOfSupportedPersonListController" ng-init="basicInformation={basicInformationData}; rightList={rightListData}; groupOfSupportedPersonList={groupOfSupportedPersonListData};">
  <div >
    <div class="container">
      <img src="images/logo_hpbanka.png" alt="Potravinová banka" id="logoHpBanka">
      <img src="images/logo/{logoName}" alt="Potravinová banka" id="logoPBanka">
      <div id="namePBanka">{pbName}</div>
    </div>
  </div>
  <hp-banka-admin-menu ng-init="activeMainMenu='formList'; activeSubMenu='groupOfSupportedPersonList'; showSubmenuSettings=true;"></hp-banka-admin-menu>
  <div class="container">
  
  <div ng-show="!showEdit" class="well well-sm" ng-init="foodBank={foodBankData}">
  <form method="post" action="groupOfSupportedPersonList.php" name="groupOfSupportedPersonForm" novalidate class="form-inline">
  
  <table class="form-filler-caption">
    <tr>
      <td class="col-sm-2 text-right"><strong>Počet dnů:</strong></td>
      <td><input type="text" class="form-control" name="daysToFillOutSupportedPersons" ng-model="foodBank.daysToFillOutSupportedPersons" placeholder="např. 5"></td>
      <td>&nbsp;&nbsp;<span class="text-muted">Počet dnů do kdy odběratel musí vyplnit počet podpořených osob za minulý měsíc.</span></td>
    </tr>
    <tr>
      <td class="col-sm-2 text-right"><strong>Datum zamknutí:</strong></td>
      <td><input type="text" class="form-control" name="lockoutDateForSupportedPersons" ng-model="foodBank.lockoutDateForSupportedPersons" placeholder="DD.MM.YYYY" bs-datepicker></td>
      <td>&nbsp;&nbsp;<span class="text-muted">Záznamy o počtu podpořených osob k nastavenému datumu nebude možné v odběratelském rozhraní upravovat.</span></td>
    </tr>
    <tr>
      <td></td>
      <td class="text-right"><button type="submit" name="saveSettings" class="btn btn-success"><span class="glyphicon glyphicon-save"></span>&nbsp;&nbsp;uložit</button></td>
      <td></td>
    </tr>
  </table>
 
  
  </form>
  </div>
  
  <br ng-if="rightList.hasAccessToGroupOfSupportedPersonList.read">
  <table ng-if="rightList.hasAccessToGroupOfSupportedPersonList.read" ng-show="!showEdit" class="table">
    <thead>
      <tr>
        <th><a class="sord" ng-click="order('name')">Název podpořené skupiny osob&nbsp;<span ng-show="sordBy === 'name'" class="glyphicon" ng-class="{true:' glyphicon-menu-up', false:' glyphicon-menu-down'}[reverse]" ></span></a></th>
        <th>Akce</th>
      </tr> 
      <tr ng-if="rightList.hasAccessToGroupOfSupportedPersonList.fullAccess">
        <td></td>
        <td><button type="submit" name="save" class="btn btn-warning" ng-click="addGroupOfSupportedPerson();"><span class="glyphicon glyphicon-plus"></span>&nbsp;&nbsp;přidat</button></td>
      </tr>
    </thead> 
    <tbody> 
    <tr ng-repeat="groupOfSupportedPerson in groupOfSupportedPersonList | orderBy:sordBy:reverse">
	    <td>{{ groupOfSupportedPerson.name }}</td>
	    <td nowrap>
	      <a ng-if="rightList.hasAccessToGroupOfSupportedPersonList.fullAccess" class="btn btn-success" href="#" title="upravit" ng-click="editGroupOfSupportedPerson(groupOfSupportedPerson.groupId);" ><span class="glyphicon glyphicon-edit"></span>&nbsp;&nbsp;upravit</a>
	      <a ng-if="!groupOfSupportedPerson.isUsedGroupId && rightList.hasAccessToGroupOfSupportedPersonList.fullAccess" class="btn btn-success" href="groupOfSupportedPersonList.php?groupId={{ groupOfSupportedPerson.groupId }}&delete=1" title="smazat"><span class="glyphicon glyphicon-trash"></span>&nbsp;&nbsp;smazat</a>
	    </td>
    </tr> 
    </tbody> 
    <tr ng-if="rightList.hasAccessToGroupOfSupportedPersonList.fullAccess">
      <td></td>
      <td><button type="submit" name="save" class="btn btn-warning" ng-click="addGroupOfSupportedPerson();"><span class="glyphicon glyphicon-plus"></span>&nbsp;&nbsp;přidat</button></td>
    </tr>
  </table>
 
  <form ng-if="rightList.hasAccessToGroupOfSupportedPersonList.fullAccess" ng-show="showEdit" method="post" action="groupOfSupportedPersonList.php" name="groupOfSupportedPersonForm" novalidate class="form-horizontal">
  <div class="form-group">
    <label for="inputName" class="col-sm-3 control-label">Název podpořené skupiny osob&nbsp;<span class="glyphicon glyphicon-star"></span></label>
    <div class="col-sm-7" ng-class="{true:'has-error', false:''}[groupOfSupportedPersonForm.name.$error.required && !groupOfSupportedPersonForm.name.$pristine]">
      <input type="text" name="name" class="form-control" id="inputName" ng-model="groupOfSupportedPerson.name" placeholder="Název podpořené skupiny osob, např. lidé bez domova" required>
      <label class="control-label" for="inputName" ng-show="groupOfSupportedPersonForm.name.$error.required && !groupOfSupportedPersonForm.name.$pristine">Tento údaj je povinný.</label>
    </div>
  </div>
  <div class="col-sm-10 text-right"><span class="glyphicon glyphicon-star"></span> Povinné údaje.</div>  
  <div class="form-group">
    <div class="col-sm-offset-2 col-sm-8">
      <button type="submit" name="save" class="btn btn-success" title="upravit" ng-disabled="!canSave();"><span class="glyphicon glyphicon-save"></span>&nbsp;&nbsp;uložit</button>
      <a class="btn btn-success" title="zpět na seznam" ng-click="showEdit=false;"><span class="glyphicon glyphicon-hand-left"></span>&nbsp;&nbsp;zpět na seznam</a>
    </div>
  </div>
  <input type="hidden" name="groupId" ng-model="groupOfSupportedPerson.groupId" value="{{ groupOfSupportedPerson.groupId }}"/>
  </form>
  
  <hr>
  <footer>
    <p>{copyright}</p>
  </footer>
  </div> <!-- /container -->
  </body>
</html>