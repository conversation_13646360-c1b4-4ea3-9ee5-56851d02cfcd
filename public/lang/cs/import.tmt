<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="description" content="">
    <meta name="author" content="">
    <link rel="icon" href="../../favicon.ico">
    <title>Potravinová banka</title>
    <!-- Bootstrap core CSS -->
    <link href="bootstrap/css/bootstrap.min.css?hpbankaversion={hpBankaVersion}" rel="stylesheet">
    <link href="bootstrap-additions/bootstrap-additions.min.css?hpbankaversion={hpBankaVersion}" rel="stylesheet">
    <!-- Custom styles for this template -->
    <link href="style/hpbanka.css?hpbankaversion={hpBankaVersion}" rel="stylesheet"> 
    <!-- HTML5 shim and Respond.js for IE8 support of HTML5 elements and media queries -->
    <!--[if lt IE 9]>
      <script src="https://oss.maxcdn.com/html5shiv/3.7.2/html5shiv.min.js?hpbankaversion={hpBankaVersion}"></script>
      <script src="https://oss.maxcdn.com/respond/1.4.2/respond.min.js?hpbankaversion={hpBankaVersion}"></script>
    <![endif]-->
    <script src="js/angular/angular.min.js?hpbankaversion={hpBankaVersion}"></script>
    <script src="js/angular/i18n/angular-locale_cs-cz.js?hpbankaversion={hpBankaVersion}"></script>
    <script src="js/jquery.min.js?hpbankaversion={hpBankaVersion}"></script>
    <script src="bootstrap/js/bootstrap.min.js?hpbankaversion={hpBankaVersion}"></script>
    <script src="js/angular-strap/angular-strap.min.js?hpbankaversion={hpBankaVersion}"></script>
    <script src="js/angular-strap/angular-strap.tpl.min.js?hpbankaversion={hpBankaVersion}"></script> 
    <script type="text/javascript" src="js/upload/angular-file-upload.min.js?hpbankaversion={hpBankaVersion}"></script>    
    <script type="text/javascript" src="stockSelectList.js?hpbankaversion={hpBankaVersion}"></script>
    <script type="text/javascript" src="hpBankaMenu.js?hpbankaversion={hpBankaVersion}"></script>    
    <script type="text/javascript" src="import.js?hpbankaversion={hpBankaVersion}2"></script>
  </head>
  <body ng-app="PBanka" ng-controller="ImportController"  nv-file-drop="" uploader="uploader" filters="queueLimit, customFilter" ng-init="basicInformation={basicInformationData}; rightList={rightListData}; importedData={importedData};">
  <div>
    <div class="container">
      <img src="images/logo_hpbanka.png" alt="Potravinová banka" id="logoHpBanka">
      <img src="images/logo/{logoName}" alt="Potravinová banka" id="logoPBanka">
      <div id="namePBanka">{pbName}</div>
    </div>
  </div>
  <hp-banka-admin-menu ng-init="activeMainMenu='stock'; activeSubMenu='import'; showSubmenuStock=true;"></hp-banka-admin-menu>
  <div class="container" ng-init="stockList={stockListData}; initCurrentStockId={currentStockId};currentStock=[]; summaryItem={summaryItem};">
  <h3 ng-show="initCurrentStockId==null">Nemáte nastavený sklad, kontakujte administrátora.</h3>
  <stock-select-list ng-show="initCurrentStockId!=null" stock-list="stockList" current-stock="currentStock" init-current-stock-id="initCurrentStockId" summary-item="summaryItem" change-stock-fn="changeStock(newCurrentStock)"></stock-select-list>
  <div class="container">
    <div class="row">
    <br/>
      <div class="col-md-3">
        <div ng-show="uploader.isHTML5">
          <div class="well my-drop-zone" nv-file-over="" uploader="uploader">Přetáhněte soubory na toto místo.</div>
        </div>
        <!-- <input type="file" nv-file-select="" uploader="uploader" multiple /><br/>-->
      </div>
      <div class="col-md-9" style="margin-bottom: 40px">
        <p>Počet souborů k načtení: {{ uploader.queue.length }}</p>
        <table class="table">
          <thead>
            <tr>
              <th width="50%">Jméno souboru</th>
              <th>Status</th>
              <th>Akce</th>
            </tr>
          </thead>
          <tbody>
            <tr ng-repeat="item in uploader.queue">
              <td><strong>{{ item.file.name }}</strong></td>
              <td class="text-center">
                <span ng-show="item.isError"><i class="glyphicon glyphicon-remove"></i></span>
              </td>
              <td>
                <button type="button" class="btn btn-success btn-xs" ng-click="item.remove()">
                  <span class="glyphicon glyphicon-trash"></span> smazat
                </button>
              </td>
            </tr>
          </tbody>
        </table>
        <div>
          <div>
          Průběh načítání:
            <div class="progress" style="">
              <div class="progress-bar" role="progressbar" ng-style="{ 'width': uploader.progress + '%' }"></div>
            </div>
          </div>
          <button type="button" class="btn btn-success btn-s" ng-click="uploader.uploadAll()" ng-disabled="!uploader.getNotUploadedItems().length">
            <span class="glyphicon glyphicon-upload"></span> načíst vše
          </button>
          <button type="button" class="btn btn-success btn-s" ng-click="uploader.clearQueue()" ng-disabled="!uploader.queue.length">
            <span class="glyphicon glyphicon-trash"></span> smazat vše
          </button>
        </div>
      </div>
    </div>
  </div>

  
  <table class="table" ng-show="initCurrentStockId!=null">
	  <tr ng-repeat="item in importedData">
	    <td>
	        <table class="table">
	            <thead>
	            <tr>
	                 <th ng-class="{true:'danger', false:''}[!item.customer]">Odběratel: {{ item.customer ? item.customer : "NENALEZEN (ID #" + item.customerId + ")"}}</th>
	                 <th>Datum výdeje: {{ item.created | date: fullDate }}</th>
	                 <th class="text-right">
	                     <a class="btn btn-success" href="outputFromStockList.php?customerId={{ item.customerId }}&issuedAt={{ item.created }}&import=1" title="nový výdej" ng-disabled="!canFill();">
	                     <span class="glyphicon glyphicon-barcode"></span>&nbsp;&nbsp;vytvořit výdejku</a>
	                     <button class="btn btn-success" ng-click="showRemoveImportedDataConfirmationDialog(item.customerId,item.created);" title="smazat">
                       <span class="glyphicon glyphicon-trash"></span>&nbsp;&nbsp;smazat</button>
	                 </th>
	            </tr>
	            </thead> 
	            <tbody> 
	                <tr>
	                  <th width="50%">Komodita</th>
	                  <th>Množství (kg)</th>
	                  <th class="no-border-top"></th>
	                </tr> 
	                <tr ng-repeat="commodity in item.commodities">
	                  <td ng-class="{true:'danger', false:''}[!commodity.commodityName]" scope="row">{{ commodity.commodityName ? commodity.commodityName : "NENALEZENA (ID #" + commodity.code + ")"}}</td>
	                  <td class="text-right">{{ commodity.amount | number:3 }}</td>
	                  <td class="no-border-top"></td>
	                </tr> 
	            </tbody> 
          </table>
	    </td>
	  </tr>
  </table>
 
 
  <hr>
  <footer>
    <p>{copyright}</p>
  </footer>
  </div> <!-- /container -->
  </body>
</html>