<!-- Static navbar -->
<form method="post" action="/index.php">
  <nav class="navbar navbar-menu navbar-default navbar-static-top">
    <div class="container">
      <div class="navbar-header">
        <button type="button" class="navbar-toggle collapsed" data-toggle="collapse" data-target="#navbar"
          aria-expanded="false" aria-controls="navbar">
          <span class="sr-only">Přepnout navigaci</span>
          <span class="icon-bar"></span>
          <span class="icon-bar"></span>
          <span class="icon-bar"></span>
          <span class="icon-bar"></span>
        </button>
      </div>
      <div id="navbar" class="navbar-collapse collapse">
        <ul class="nav navbar-nav">
          <li ng-if="rightList.incomePerStockList.read" ng-class="{true:'active', false:''}[activeMainMenu == 'stock']">
            <a href="/incomePerStockList.php">Sklad</a>
            <ul class="submenu submenu-mobil">
              <li ng-if="rightList.import.read" ng-class="{true:'active', false:''}[activeSubMenu == 'import']"><a
                  href="import.php">Import výdejek</a></li>
              <li ng-if="rightList.incomePerStockList.read"
                ng-class="{true:'active', false:''}[activeSubMenu == 'incomePerStockList']"><a
                  href="incomePerStockList.php">Nový příjem</a></li>
              <li ng-if="rightList.receiptToStockList.read"
                ng-class="{true:'active', false:''}[activeSubMenu == 'receiptToStockList']"><a
                  href="receiptToStockList.php">Příjemky</a></li>
              <li ng-if="rightList.outputFromStockList.read"
                ng-class="{true:'active', false:''}[activeSubMenu == 'outputFromStockList']"><a
                  href="outputFromStockList.php">Nový výdej</a></li>
              <li ng-if="rightList.stockReleaseNoteList.read"
                ng-class="{true:'active', false:''}[activeSubMenu == 'stockReleaseNoteList']"><a
                  href="stockReleaseNoteList.php">Výdejky</a></li>
              <li ng-if="rightList.stockTransfersList.read"
                ng-class="{true:'active', false:''}[activeSubMenu == 'stockTransfersList']"><a
                  href="stockTransfersList.php">Převodky</a></li>
              <li ng-if="rightList.supply.read" ng-class="{true:'active', false:''}[activeSubMenu == 'supply']"><a
                  href="supply.php">Zásoby</a></li>
            </ul>
          </li>
          <li ng-if="rightList.customerList.read"
            ng-class="{true:'active', false:''}[activeMainMenu == 'customerList']"><a
              href="/customerList.php">Odběratelé</a></li>
          <li ng-if="rightList.supplierList.read"
            ng-class="{true:'active', false:''}[activeMainMenu == 'supplierList']"><a
              href="/supplierList.php">Dodavatelé</a></li>
          <li ng-if="rightList.donationAgreementList.read"
            ng-class="{true:'active', false:''}[activeMainMenu == 'donationAgreementList']"><a
              href="/donationAgreementList.php">Darovací smlouvy</a></li>
          <li ng-if="rightList.supportedPersonListByCustomer.read"
            ng-class="{true:'active', false:''}[activeMainMenu == 'supportedPersonListByCustomer']"><a
              href="/supportedPersonListByCustomer.php">Podpořené osoby</a></li>
          <li ng-if="rightList.reportList.read" ng-class="{true:'active', false:''}[activeMainMenu == 'reportList']"><a
              href="/reports/reportList.php">Reporty</a></li>
          <li ng-if="rightList.manualList.read" ng-class="{true:'active', false:''}[activeMainMenu == 'manualList']"><a
              href="/manualList.php">Manuály</a></li>
          <li ng-if="rightList.formList.read" ng-class="{true:'active', false:''}[activeMainMenu == 'formList']">
            <a href="/formList.php">Nastavení</a>
            <ul class="submenu submenu-mobil">
              <li ng-if="rightList.formList.read" ng-class="{true:'active', false:''}[activeSubMenu == 'formList']"><a
                  href="formList.php">Formuláře</a></li>
              <li ng-if="rightList.commodityList.read"
                ng-class="{true:'active', false:''}[activeSubMenu == 'commodityList']"><a
                  href="commodityList.php">Komodity</a></li>
              <li ng-if="rightList.stockList.read" ng-class="{true:'active', false:''}[activeSubMenu == 'stockList']"><a
                  href="stockList.php">Sklady</a></li>
              <li ng-if="rightList.appUserList.read"
                ng-class="{true:'active', false:''}[activeSubMenu == 'appUserList']"><a
                  href="appUserList.php">Uživatelé</a></li>
              <li ng-if="rightList.groupOfSupportedPersonList.read"
                ng-class="{true:'active', false:''}[activeSubMenu == 'groupOfSupportedPersonList']"><a
                  href="groupOfSupportedPersonList.php">Podpořené osoby</a></li>
              <li ng-if="rightList.settingDonationAgreements.read"
                ng-class="{true:'active', false:''}[activeSubMenu == 'settingDonationAgreements']"><a
                  href="settingDonationAgreements.php">Darovací smlouvy</a></li>
            </ul>
          </li>
        </ul>
        <ul class="nav navbar-nav navbar-right">
          <li>
            <p class="navbar-text">Přihlášený je:<b> {{ basicInformation.login }}</b></p>
          </li>
          <li><button type="submit" class="btn btn-danger navbar-btn"><span class="glyphicon glyphicon-off"
                aria-hidden="true"></span>&nbsp;&nbsp;odhlásit se</button></li>
        </ul>
      </div><!--/.nav-collapse -->
    </div>
  </nav>
  <nav ng-if="showSubmenuStock" class="navbar navbar-submenu navbar-default">
    <div class="container">
      <ul class="nav navbar-nav submenu">
        <li ng-if="rightList.import.read" ng-class="{true:'active', false:''}[activeSubMenu == 'import']"><a
            href="import.php">Import výdejek</a></li>
        <li ng-if="rightList.incomePerStockList.read"
          ng-class="{true:'active', false:''}[activeSubMenu == 'incomePerStockList']"><a
            href="incomePerStockList.php">Nový příjem</a></li>
        <li ng-if="rightList.receiptToStockList.read"
          ng-class="{true:'active', false:''}[activeSubMenu == 'receiptToStockList']"><a
            href="receiptToStockList.php">Příjemky</a></li>
        <li ng-if="rightList.outputFromStockList.read"
          ng-class="{true:'active', false:''}[activeSubMenu == 'outputFromStockList']"><a
            href="outputFromStockList.php">Nový výdej</a></li>
        <li ng-if="rightList.stockReleaseNoteList.read"
          ng-class="{true:'active', false:''}[activeSubMenu == 'stockReleaseNoteList']"><a
            href="stockReleaseNoteList.php">Výdejky</a></li>
        <li ng-if="rightList.stockTransfersList.read"
          ng-class="{true:'active', false:''}[activeSubMenu == 'stockTransfersList']"><a
            href="stockTransfersList.php">Převodky</a></li>
        <li ng-if="rightList.supply.read" ng-class="{true:'active', false:''}[activeSubMenu == 'supply']"><a
            href="supply.php">Zásoby</a></li>
      </ul>
    </div>
  </nav>
  <nav ng-if="showSubmenuSettings" class="navbar navbar-submenu navbar-default">
    <div class="container">
      <ul class="nav navbar-nav submenu">
        <li ng-if="rightList.formList.read" ng-class="{true:'active', false:''}[activeSubMenu == 'formList']"><a
            href="formList.php">Formuláře</a></li>
        <li ng-if="rightList.commodityList.read" ng-class="{true:'active', false:''}[activeSubMenu == 'commodityList']">
          <a href="commodityList.php">Komodity</a>
        </li>
        <li ng-if="rightList.stockList.read" ng-class="{true:'active', false:''}[activeSubMenu == 'stockList']"><a
            href="stockList.php">Sklady</a></li>
        <li ng-if="rightList.appUserList.read" ng-class="{true:'active', false:''}[activeSubMenu == 'appUserList']"><a
            href="appUserList.php">Uživatelé</a></li>
        <li ng-if="rightList.groupOfSupportedPersonList.read"
          ng-class="{true:'active', false:''}[activeSubMenu == 'groupOfSupportedPersonList']"><a
            href="groupOfSupportedPersonList.php">Podpořené osoby</a></li>
        <li ng-if="rightList.settingDonationAgreements.read"
          ng-class="{true:'active', false:''}[activeSubMenu == 'settingDonationAgreements']"><a
            href="settingDonationAgreements.php">Darovací smlouvy</a></li>
        <li ng-if="rightList.settingFoodBank.read"
          ng-class="{true:'active', false:''}[activeSubMenu == 'settingFoodBank']"><a
            href="settingFoodBank.php">Potravinová banka</a></li>
      </ul>
    </div>
  </nav>
</form>