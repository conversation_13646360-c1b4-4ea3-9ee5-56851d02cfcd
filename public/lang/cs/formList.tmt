<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="description" content="">
    <meta name="author" content="">
    <link rel="icon" href="../../favicon.ico">
    <title>Potravinová banka</title>
    <!-- Bootstrap core CSS -->
    <link href="bootstrap/css/bootstrap.min.css?hpbankaversion={hpBankaVersion}" rel="stylesheet">
    <link href="bootstrap-additions/bootstrap-additions.min.css?hpbankaversion={hpBankaVersion}" rel="stylesheet">
    <!-- Custom styles for this template -->
    <link href="style/hpbanka.css?hpbankaversion={hpBankaVersion}" rel="stylesheet"> 
    <!-- HTML5 shim and Respond.js for IE8 support of HTML5 elements and media queries -->
    <!--[if lt IE 9]>
      <script src="https://oss.maxcdn.com/html5shiv/3.7.2/html5shiv.min.js?hpbankaversion={hpBankaVersion}"></script>
      <script src="https://oss.maxcdn.com/respond/1.4.2/respond.min.js?hpbankaversion={hpBankaVersion}"></script>
    <![endif]-->
    <script src="js/angular/angular.min.js?hpbankaversion={hpBankaVersion}"></script>
    <script src="js/angular/i18n/angular-locale_cs-cz.js?hpbankaversion={hpBankaVersion}"></script>
    <script src="js/jquery.min.js?hpbankaversion={hpBankaVersion}"></script>
    <script src="bootstrap/js/bootstrap.min.js?hpbankaversion={hpBankaVersion}"></script>
    <script src="js/angular-strap/angular-strap.min.js?hpbankaversion={hpBankaVersion}"></script>
    <script src="js/angular-strap/angular-strap.tpl.min.js?hpbankaversion={hpBankaVersion}"></script> 
    <script type="text/javascript" src="hpBanka.js?hpbankaversion={hpBankaVersion}"></script> 
    <script type="text/javascript" src="hpBankaMenu.js?hpbankaversion={hpBankaVersion}"></script> 
    <script type="text/javascript" src="formList.js?hpbankaversion={hpBankaVersion}"></script>  
    <script>
	    $(function () {
	    	  $('[data-toggle="tooltip"]').tooltip({container: 'body'})
	    	})
    </script>   
  </head>

  <body ng-app="PBanka" ng-controller="FormListController" ng-init="basicInformation={basicInformationData}; rightList={rightListData}; formList={formListData};">
  <div>
    <div class="container">
      <img src="images/logo_hpbanka.png" alt="Potravinová banka" id="logoHpBanka">
      <img src="images/logo/{logoName}" alt="Potravinová banka" id="logoPBanka">
      <div id="namePBanka">{pbName}</div>
    </div>
  </div>
  <hp-banka-admin-menu ng-init="activeMainMenu='formList'; activeSubMenu='formList'; showSubmenuSettings=true;"></hp-banka-admin-menu>
  <div class="container">
  <br>
  <table ng-show="!showEdit" class="table">
    <thead>
      <tr>
        <th>Č.</th><th>Název akce / formuláře</th><th>Platnost od</th><th>Platnost do</th><th class="text-right" data-toggle="tooltip" data-placement="top" title="Počet dnů od data odběru zboží kdy odběratel může vyplnit formulář.">Počet dnů</th><th>Akce</th>
      </tr>
      <tr class="button" ng-if="rightList.formList.fullAccess">
        <td colspan="5"></td><td><button type="submit" name="save" class="btn btn-warning" ng-click="addForm();"><span class="glyphicon glyphicon-plus"></span>&nbsp;&nbsp;přidat</button></td>
      </tr> 
    </thead> 
    <tbody> 
    <tr ng-repeat="form in formList">
	    <th scope="row">{{ $index + 1 }}</th>
	    <td>{{ form.actionName }}</td>
	    <td>{{ form.validFrom | date: fullDate }}</td>
	    <td>{{ form.validTo | date: fullDate  }}</td>
	    <td class="text-right">{{ form.backDays }}</td>
	    <td nowrap="nowrap">
	      <a ng-show="rightList.formList.fullAccess" class="btn btn-success" href="#" title="upravit" ng-click="editForm($index);"><span class="glyphicon glyphicon-edit"></span>&nbsp;&nbsp;upravit</a>
	      <a ng-show="!rightList.formList.fullAccess" class="btn btn-success" href="#" title="zobrazit" ng-click="showForm($index);"><span class="glyphicon glyphicon-eye-open"></span>&nbsp;&nbsp;zobrazit</a>
	      <a class="btn btn-success" href="formEditor.php?formId={{ form.formId }}" title="komodity"><span class="glyphicon glyphicon-grain"></span>&nbsp;&nbsp;komodity</a>
	      <a class="btn btn-success" href="formSupplierEditor.php?formId={{ form.formId }}" title="dodavatelé"><span class="glyphicon glyphicon-glass"></span>&nbsp;&nbsp;dodavatelé</a>
	      <a ng-if="rightList.formList.fullAccess && !form.editedItem" class="btn btn-success" href="formList.php?formId={{ form.formId }}&delete=1" title="smazat"><span class="glyphicon glyphicon-trash"></span>&nbsp;&nbsp;smazat</a>
	    </td>
    </tr> 
    <tr ng-if="rightList.formList.fullAccess">
    <td colspan="5"></td>
    <td><button type="submit" name="save" class="btn btn-warning" ng-click="addForm();"><span class="glyphicon glyphicon-plus"></span>&nbsp;&nbsp;přidat</button></td>
    </tr>
    </tbody> 
  </table>  

  <form ng-show="showEdit" method="post" action="formList.php" name="formListForm" novalidate class="form-horizontal">

  <div class="form-group">
    <label for="inputActionName" class="col-sm-3 control-label">Název akce / formuláře&nbsp;<span class="glyphicon glyphicon-star"></span></label>
    <div class="col-sm-5" ng-class="{true:'has-error', false:''}[formListForm.actionName.$error.required && !formListForm.actionName.$pristine]">
      <input ng-disabled="!canEdit();" type="text" name="actionName" class="form-control" id="inputActionName" ng-model="form.actionName" required>
      <label class="control-label" ng-show="formListForm.actionName.$error.required && !formListForm.actionName.$pristine">Tento údaj je povinný.</label>
    </div>
  </div>

  <div class="form-group">
    <label for="inputValidFrom" class="col-sm-3 control-label">Platnost od&nbsp;<span class="glyphicon glyphicon-star"></span></label>
    <div class="col-sm-5" ng-class="{true:'has-error', false:''}[(formListForm.validFrom.$error.required || formListForm.validFrom.$error.date) && !formListForm.validFrom.$pristine]">
      <input ng-disabled="!canEdit();" type="text" name="validFrom" class="form-control" id="inputValidFrom" ng-model="form.validFrom" data-max-date="{{form.validTo}}" bs-datepicker required>
      <label class="control-label" for="" ng-show="formListForm.validFrom.$error.required && !formListForm.validFrom.$pristine">Tento údaj je povinný.</label>
      <label class="control-label" for="" ng-show="formListForm.validFrom.$error.date && !formListForm.validFrom.$pristine">Zadejte datum ve formátu DD.MM.YYYY.</label>
    </div>
  </div>

  <div class="form-group">
    <label for="inputValidTo" class="col-sm-3 control-label">Platnost do&nbsp;<span class="glyphicon glyphicon-star"></span></label>
    <div class="col-sm-5" ng-class="{true:'has-error', false:''}[(formListForm.validTo.$error.required  || formListForm.validTo.$error.date) && !formListForm.validTo.$pristine]">
      <input ng-disabled="!canEdit();" type="text" name="validTo" class="form-control" id="inputValidTo" ng-model="form.validTo" data-min-date="{{form.validFrom}}"   bs-datepicker required>
      <label class="control-label" for="" ng-show="formListForm.validTo.$error.required && !formListForm.validTo.$pristine">Tento údaj je povinný.</label>
      <label class="control-label" for="" ng-show="formListForm.validTo.$error.date && !formListForm.validTo.$pristine">Zadejte datum ve formátu DD.MM.YYYY.</label>
    </div>
  </div>

  <div class="form-group">
    <label for="inputBackDays" class="col-sm-3 control-label">Počet dnů&nbsp;<span class="glyphicon glyphicon-star"></span></label>
    <div class="col-sm-5" ng-class="{true:'has-error', false:''}[formListForm.backDays.$error.required && !formListForm.backDays.$pristine]">
      <input ng-disabled="!canEdit();" type="text" name="backDays" class="form-control" id="inputBackDays" ng-model="form.backDays" required>
      <label class="control-label" for="" ng-show="formListForm.backDays.$error.required && !formListForm.backDays.$pristine">Tento údaj je povinný.</label>
      <span id="helpBlock" class="help-block">Počet dnů od data odběru zboží, kdy odběratel může vyplnit formulář.</span>
    </div>
  </div>

  <div class="form-group" ng-init="stockList={stockListData}">
    <label for="inputStock" class="col-sm-3 control-label">Sklad pro přímý odběr&nbsp;<span class="glyphicon glyphicon-star"></span></label>
    <div class="col-sm-5">
      <select class="form-control" ng-disabled="!canEditedStockList();" ng-model="currentStock" ng-options="stock.name for stock in stockList" ng-init="inicializeSelectedStock({stockId});"></select>
      <input type="hidden" name="stockId" value="{{ currentStock.stockId }}">
      <span id="helpBlock" class="help-block">Příjem a výdej přímého odběru se uskuteční na tento sklad.</span>
    </div>
  </div>

  <div class="form-group">
    <label class="col-sm-3 control-label">Formulář určený pro&nbsp;<span class="glyphicon glyphicon-star"></span></label>
    <div class="col-sm-5">
      <label class="checkbox-inline" for="inputDirectConsumption">
        <input type="checkbox" name="directConsumption" ng-disabled="!canEdit();" id="inputDirectConsumption" ng-model="form.directConsumption">přímý odběr
      </label>
	 <label class="checkbox-inline" for="inputIncomePerStock">
		<input type="checkbox" name="incomePerStock" ng-disabled="!canEdit();" id="inputIncomePerStock" ng-model="form.incomePerStock">příjem
	 </label>
	 <label class="checkbox-inline" for="inputOutputFromStock">
		<input type="checkbox" name="outputFromStock" ng-disabled="!canEdit();" id="inputOutputFromStock" ng-model="form.outputFromStock">výdej
	 </label>
    </div>
  </div>
  
  <div class="col-sm-8 text-right"><span class="glyphicon glyphicon-star"></span> Povinné údaje.</div>
  
  <div class="form-group">
    <div class="col-sm-offset-3 col-sm-5">
      <button ng-if="rightList.formList.fullAccess" type="submit" name="save" class="btn btn-success" title="upravit" ng-disabled="!canSave();"><span class="glyphicon glyphicon-save"></span>&nbsp;&nbsp;uložit</button>
      <a class="btn btn-success" title="zpět na seznam" ng-click="showEdit=false;"><span class="glyphicon glyphicon-hand-left"></span>&nbsp;&nbsp;zpět na seznam</a>
    </div>
  </div>
  <input type="hidden" name="formId" ng-model="form.formId" value="{{ form.formId }}"/>
  </form>
  <hr>
  <footer>
    <p>{copyright}</p>
  </footer>
  </div> <!-- /container -->
  </body>
</html>