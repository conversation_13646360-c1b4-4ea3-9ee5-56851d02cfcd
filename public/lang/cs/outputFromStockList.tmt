<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="description" content="">
    <meta name="author" content="">
    <link rel="icon" href="../../favicon.ico">
    <title>Potravinová banka</title>
    <!-- Bootstrap core CSS -->
    <link href="bootstrap/css/bootstrap.min.css?hpbankaversion={hpBankaVersion}" rel="stylesheet">
    <link href="bootstrap-additions/bootstrap-additions.min.css?hpbankaversion={hpBankaVersion}" rel="stylesheet">
    <!-- Custom styles for this template -->
    <link href="style/hpbanka.css?hpbankaversion={hpBankaVersion}" rel="stylesheet"> 
    <!-- HTML5 shim and Respond.js for IE8 support of HTML5 elements and media queries -->
    <!--[if lt IE 9]>
      <script src="https://oss.maxcdn.com/html5shiv/3.7.2/html5shiv.min.js?hpbankaversion={hpBankaVersion}"></script>
      <script src="https://oss.maxcdn.com/respond/1.4.2/respond.min.js?hpbankaversion={hpBankaVersion}"></script>
    <![endif]-->
    <script src="js/angular/angular.min.js?hpbankaversion={hpBankaVersion}"></script>
    <script src="js/angular/i18n/angular-locale_cs-cz.js?hpbankaversion={hpBankaVersion}"></script>
    <script src="js/jquery.min.js?hpbankaversion={hpBankaVersion}"></script>
    <script src="bootstrap/js/bootstrap.min.js?hpbankaversion={hpBankaVersion}"></script>
    <script type="text/javascript" src="stockSelectList.js?hpbankaversion={hpBankaVersion}"></script>
    <script type="text/javascript" src="hpBankaMenu.js?hpbankaversion={hpBankaVersion}"></script>    
    <script type="text/javascript" src="outputFromStockList.js?hpbankaversion={hpBankaVersion}"></script>
  </head>
  <body ng-app="PBanka" ng-controller="OutputFromStockListController" ng-init="basicInformation={basicInformationData}; rightList={rightListData}; outputFromStockList={outputFromStockListData};">
  <div >
    <div class="container">
      <img src="images/logo_hpbanka.png" alt="Potravinová banka" id="logoHpBanka">
      <img src="images/logo/{logoName}" alt="Potravinová banka" id="logoPBanka">
      <div id="namePBanka">{pbName}</div>
    </div>
  </div>
  <hp-banka-admin-menu ng-init="activeMainMenu='stock'; activeSubMenu='outputFromStockList'; showSubmenuStock=true;"></hp-banka-admin-menu>
  <div class="container" ng-init="stockList={stockListData}; initCurrentStockId={currentStockId};currentStock=[];summaryItem={summaryItem};">
  <h3 ng-show="initCurrentStockId==null">Nemáte nastavený sklad, kontakujte administrátora.</h3>
  <stock-select-list ng-show="initCurrentStockId!=null" stock-list="stockList" current-stock="currentStock" init-current-stock-id="initCurrentStockId" summary-item="summaryItem" change-stock-fn="changeStock(newCurrentStock)"></stock-select-list>
  <table class="table" ng-show="initCurrentStockId!=null">
    <thead>
      <tr><th>Č.</th><th>Název formuláře</th><th>Přístupný do</th><th>Akce</th></tr> 
    </thead>
    <tbody ng-init="dataForOutputFromStock={dataForOutputFromStock}">
    <tr ng-repeat="outputFromStock in outputFromStockList">
      <th scope="row">{{ outputFromStock.order }}</th><td>{{ outputFromStock.actionName }}</td><td>{{ outputFromStock.validTo }}</td>
      <td>
        <a class="btn btn-success" ng-click="goToOutputFromStock(outputFromStock.formId);" href="#" title="nový výdej" ng-disabled="!canFill();"><span class="glyphicon glyphicon-grain"></span>&nbsp;&nbsp;vyplnit výdej</a>
      </td>
    </tr> 
    </tbody> 
  </table>
  <hr>
  <footer>
    <p>{copyright}</p>
  </footer>
  </div> <!-- /container -->
  </body>
</html>