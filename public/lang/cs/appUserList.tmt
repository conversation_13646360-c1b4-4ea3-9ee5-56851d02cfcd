<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="description" content="">
    <meta name="author" content="">
    <link rel="icon" href="../../favicon.ico">
    <title>Potravinová banka</title>
    <!-- Bootstrap core CSS -->
    <link href="bootstrap/css/bootstrap.min.css?hpbankaversion={hpBankaVersion}" rel="stylesheet">
    <!-- Custom styles for this template -->
    <link href="style/hpbanka.css?hpbankaversion={hpBankaVersion}" rel="stylesheet"> 
    <!-- HTML5 shim and Respond.js for IE8 support of HTML5 elements and media queries -->
    <!--[if lt IE 9]>
      <script src="https://oss.maxcdn.com/html5shiv/3.7.2/html5shiv.min.js?hpbankaversion={hpBankaVersion}"></script>
      <script src="https://oss.maxcdn.com/respond/1.4.2/respond.min.js?hpbankaversion={hpBankaVersion}"></script>
    <![endif]-->
	  <script type="text/javascript" src="js/jquery.min.js?hpbankaversion={hpBankaVersion}"></script>
	  <script type="text/javascript" src="js/angular/angular.min.js?hpbankaversion={hpBankaVersion}"></script>
	  <script type="text/javascript" src="bootstrap/js/bootstrap.min.js?hpbankaversion={hpBankaVersion}"></script> 
	  <script type="text/javascript" src="js/angular/sortable.js?hpbankaversion={hpBankaVersion}"></script>
	  <script type="text/javascript" src="appUserList.js?hpbankaversion={hpBankaVersion}"></script>
	  <script type="text/javascript" src="js/jquery-ui.min.js?hpbankaversion={hpBankaVersion}"></script> 
	  <script type="text/javascript" src="hpBankaMenu.js?hpbankaversion={hpBankaVersion}"></script>   
  </head>

  <body ng-app="PBanka" ng-controller="AppUserListController" ng-init="basicInformation={basicInformationData}; rightList={rightListData}; appUserList={appUserListData};">
  <div>
    <div class="container">
      <img src="images/logo_hpbanka.png" alt="Potravinová banka" id="logoHpBanka">
      <img src="images/logo/{logoName}" alt="Potravinová banka" id="logoPBanka">
      <div id="namePBanka">{pbName}</div>
    </div>
  </div>
  <hp-banka-admin-menu ng-init="activeMainMenu='formList'; activeSubMenu='appUserList'; showSubmenuSettings=true;"></hp-banka-admin-menu>
  <div class="container">
  <br>
  <table ng-show="!showEdit && !showAssignStock" class="table">
    <thead>  
      <tr>
        <th>Č.</th>
        <th nowrap><a class="sord" ng-click="order('login')">Přihlašovací jméno <span ng-show="sordBy === 'login'" class="glyphicon" ng-class="{true:' glyphicon-menu-up', false:' glyphicon-menu-down'}[reverse]" ></span></a></th>
        <th>Typ uživatele</th>
        <th>Akce</th>
      </tr> 
      <tr ng-if="rightList.appUserList.fullAccess" class="button">
        <td colspan="3"></td>
        <td class="text-left"><button type="submit" name="save" class="btn btn-warning" ng-click="addAppUser();"><span class="glyphicon glyphicon-plus"></span>&nbsp;&nbsp;přidat</button></td> 
      </tr>
    </thead> 
    <tbody> 
    <tr ng-repeat="appUser in appUserList | orderBy:sordBy:reverse">
	    <th scope="row">{{ $index + 1 }}</th>
	    <td>{{ appUser.login }}</td>
	    <td>{{ appUser.userTypeName }}</td>
	    <td nowrap>
	      <a ng-show="rightList.appUserList.fullAccess" class="btn btn-success" href="#" title="upravit" ng-click="editAppUser(appUser.appUserId);"><span class="glyphicon glyphicon-edit"></span>&nbsp;&nbsp;upravit</a>
        <a ng-show="rightList.appUserList.fullAccess && appUser.isStorekeeper" class="btn btn-success" href="#" title="sklady" ng-click="assignedStock(appUser.appUserId);"><span class="glyphicon glyphicon-briefcase"></span>&nbsp;&nbsp;sklady</a>
	      <a ng-show="!rightList.appUserList.fullAccess" class="btn btn-success" href="#" title="zobrazit" ng-click="showAppUser(appUser.appUserId);"><span class="glyphicon glyphicon-eye-open"></span>&nbsp;&nbsp;zobrazit</a>
	      <a ng-if="rightList.appUserList.fullAccess" class="btn btn-success" href="#" title="změna hesla" ng-click="editPassword(appUser.appUserId);"><span class="glyphicon glyphicon-edit"></span>&nbsp;&nbsp;změna hesla</a>
	      <a ng-if="rightList.appUserList.fullAccess && !appUser.usedItem" class="btn btn-success" href="appUserList.php?appUserId={{ appUser.appUserId }}&delete=1" title="smazat"><span class="glyphicon glyphicon-trash"></span>&nbsp;&nbsp;smazat</a>
	    </td>
    </tr> 
    <tr ng-if="rightList.appUserList.fullAccess">
    <td colspan="3"></td>
    <td class="text-left"><button type="submit" name="save" class="btn btn-warning" ng-click="addAppUser();"><span class="glyphicon glyphicon-plus"></span>&nbsp;&nbsp;přidat</button></td>
    </tr>
    </tbody> 
  </table>
  
  <form ng-show="showEdit" method="post" action="appUserList.php" name="appUserListForm" novalidate class="form-horizontal">
  <div class="form-group">
    <label for="inputLogin" class="col-sm-2 control-label">Přihlašovací jméno&nbsp;<span class="glyphicon glyphicon-star"></span></label>
    <div class="col-sm-8" ng-class="{true:'has-error', false:''}[(appUserListForm.login.$error.required || appUserListForm.login.$error.username) && !appUserListForm.login.$pristine]">
      <input ng-disabled="!canEdit();" type="text" name="login" class="form-control" id="inputLogin" placeholder="Přihlašovací jméno" username ng-model="appUser.login" required>
      <label class="control-label" for="inputLogin" ng-show="appUserListForm.login.$error.required && !appUserListForm.login.$pristine">Tento údaj je povinný.</label>
      <label class="control-label" for="inputLogin" ng-show="appUserListForm.login.$error.username && !appUserListForm.login.$pristine">Přihlašovací jméno nelze použít.</label>
    </div>
  </div>
  
  <div class="form-group" ng-init="userTypeList={userTypeListData}">
    <label for="inputUserType" class="col-sm-2 control-label">Typ uživatele&nbsp;<span class="glyphicon glyphicon-star"></span></label>
    <div class="col-sm-8">
      <select class="form-control" ng-model="currentUserType" ng-options="userType.name for userType in userTypeList" ng-init="inicializeSelectedUserType({userTypeId});"></select>
      <input type="hidden" name="userType" value="{{ currentUserType.userTypeId }}">
    </div>
  </div>
  
  <div ng-if="appUser.appUserId===0 || showPassword">
  <div class="form-group">
    <label for="inputPassword" class="col-sm-2 control-label"> Heslo&nbsp;<span class="glyphicon glyphicon-star"></span></label>
    <div class="col-sm-8" ng-class="{true:'has-error', false:''}[(appUserListForm.password.$error.required || appUserListForm.password.$error.minlength || appUserListForm.password.$error.sameasusername) && !appUserListForm.password.$pristine]">
      <input ng-disabled="!canEdit();" type="password" name="password" class="form-control" id="inputPassword" placeholder="Heslo"  minlength="5" sameasusername ng-model="appUser.password" required>
      <label class="control-label" for="inputPassword" ng-show="appUserListForm.password.$error.required && !appUserListForm.password.$pristine">Tento údaj je povinný.</label>
      <label class="control-label" for="inputPassword" ng-show="appUserListForm.password.$error.minlength && !appUserListForm.password.$pristine">Heslo je příliš krátké.</label>
      <label class="control-label" for="inputPassword" ng-show="appUserListForm.password.$error.sameasusername && !appUserListForm.password.$pristine">Heslo nesmí být stejné jako přihlašovací jméno.</label>
      <span id="helpBlock" class="help-block">Heslo nesmí být kratší než 5 znaků nebo stejné jako přihlašovací jméno.</span>
    </div>
  </div>

  <div class="form-group">
    <label for="inputPassword2" class="col-sm-2 control-label">Zopakujte Heslo&nbsp;<span class="glyphicon glyphicon-star"></span></label>
    <div class="col-sm-8" ng-class="{true:'has-error', false:''}[(appUserListForm.password2.$error.required || appUserListForm.password2.$error.pattern) && !appUserListForm.password2.$pristine]">
      <input ng-disabled="!canEdit();" type="password" name="password2" class="form-control" id="inputPassword2" placeholder="Heslo" ng-model="password2" ng-pattern="appUser.password" required>
      <label class="control-label" for="inputPassword2" ng-show="appUserListForm.password2.$error.required && !appUserListForm.password2.$pristine">Tento údaj je povinný.</label>
      <label class="control-label" for="inputPassword2" ng-show="appUserListForm.password2.$error.pattern && !appUserListForm.password2.$pristine">Hesla se neshodují.</label>
    </div>
  </div>
  </div>
  <div class="col-sm-10 text-right"><span class="glyphicon glyphicon-star"></span> Povinné údaje.</div>
  <div class="form-group">
    <div class="col-sm-offset-2 col-sm-8">
      <button ng-if="rightList.appUserList.fullAccess" type="submit" name="save" class="btn btn-success" title="upravit" ng-disabled="!canSave();"><span class="glyphicon glyphicon-save"></span>&nbsp;&nbsp;uložit</button>
      <a class="btn btn-success" title="zpět na seznam" ng-click="showEdit=false;"><span class="glyphicon glyphicon-hand-left"></span>&nbsp;&nbsp;zpět na seznam</a>
    </div>
  </div>
  <input type="hidden" name="appUserId" ng-model="appUser.appUserId" value="{{ appUser.appUserId }}"/>
  </form>
  
  
  <div ng-show="showAssignStock">
  <div class="page-header">
    <h4>Přiřazení skladů skladníkovi</h4>
  </div>
  <div ng-show="showSaveMessage" class="alert alert-success show-message" role="alert">Byla přidána položka.</div>
  <div ng-show="showRemoveMessage" class="alert alert-success show-message" role="alert">Byla smazána položka.</div>
  <div ng-show="showErrorMessage" class="alert alert-danger show-message" role="alert">Nepodařilo se uložit řazení.</div>
  <div class="panel panel-default">
  <!-- Default panel contents -->
  <div class="panel-body">
    <p>Seznam nabízených skladů</p>
    <div ui-sortable="sortableOption" ng-model="selectedStockList">
      <div ng-repeat="stock in selectedStockList" class="form-inline">
        <fieldset ng-disabled="!canEdit()">
          <div ng-show="canEdit();" class="form-group handleForSorting cursor-move"><span class="glyphicon glyphicon-resize-vertical"></span>&nbsp;</div>
          <div class="checkbox" ng-click="removeSelectedStockList(stock);"><label><input type="checkbox" checked="checked">&nbsp;&nbsp;{{stock.name}}</label></div>
        </fieldset>  
      </div>
    </div>  
  </div>
  <div class="panel-body">
    <p>Přidejte do seznamu z nabídky skladů</p>
    <div  ng-model="availableStockList">
      <div ng-repeat="stock in availableStockList" class="form-inline">
        <fieldset ng-disabled="!canEdit()">
          <div class="checkbox" ng-click="addToSelectedStockList(stock);"><label><input type="checkbox">&nbsp;&nbsp;{{stock.name}}</label></div>
        </fieldset>
      </div>
    </div>
  </div>
  </div>
  <a class="btn btn-success" title="zpět na seznam" ng-click="showEdit=false;showAssignStock=false;"><span class="glyphicon glyphicon-hand-left"></span>&nbsp;&nbsp;zpět na seznam</a>
  <input type="hidden" name="formList" value="{{ formList }}" />
  </div>
  
  <hr>
  <footer>
    <p>{copyright}</p>
  </footer>
  </div> <!-- /container -->
  </body>
</html>