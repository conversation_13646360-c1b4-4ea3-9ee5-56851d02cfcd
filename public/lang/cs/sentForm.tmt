<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="description" content="">
    <meta name="author" content="">
    <link rel="icon" href="../../favicon.ico">
    <title>Potravinová banka</title>
    <!-- Bootstrap core CSS -->
    <link href="bootstrap/css/bootstrap.min.css?hpbankaversion={hpBankaVersion}" rel="stylesheet">
    <!-- Custom styles for this template -->
    <link href="style/hpbanka.css?hpbankaversion={hpBankaVersion}" rel="stylesheet">
    <!-- HTML5 shim and Respond.js for IE8 support of HTML5 elements and media queries -->
    <!--[if lt IE 9]>
    <script src="https://oss.maxcdn.com/html5shiv/3.7.2/html5shiv.min.js?hpbankaversion={hpBankaVersion}"></script>
    <script src="https://oss.maxcdn.com/respond/1.4.2/respond.min.js?hpbankaversion={hpBankaVersion}"></script>
    <![endif]-->
    <script src="js/angular/angular.min.js?hpbankaversion={hpBankaVersion}"></script>
    <script src="js/angular/i18n/angular-locale_cs-cz.js?hpbankaversion={hpBankaVersion}"></script>
    <script src="js/jquery.min.js?hpbankaversion={hpBankaVersion}"></script>
    <script src="bootstrap/js/bootstrap.min.js?hpbankaversion={hpBankaVersion}"></script>
    <script type="text/javascript" src="sentForm.js?hpbankaversion={hpBankaVersion}"></script>
</head>

<body ng-app="PBanka" ng-controller="SentFormController">

<div ng-init="form={formData};">
    <h4>Akce: {{ form.actionName }}</h4>
    <h5>Formulář číslo: {{ form.foodBankStockReleaseNoteId }}</h5>
    <h5>Odběr se uskutečnil dne: {{ form.issuedAt | date: fullDate }}</h5>
    <h5>Odběratel: {{ form.customerName }}</h5>
    <h5>Dodavatel: {{ form.supplierName }}</h5>
    <h5>Hodnota daru uvedená dodavatelem: {{ form.giftValue | number:2 }} {currencySymbol}</h5>
    <h5>Hodnota daru v kalkulační ceně: {{ form.priceCalculation | number:2 }} {currencySymbol}</h5>
    <h5>Množství celkem: {{ form.totalKg | number:3 }} kg</h5>
    <hr>

    <table class="table table-bordered" ng-init="stockReleaseNoteCommodityList={stockReleaseNoteCommodityData};">
        <thead>
        <tr>
            <th colspan="3">Množství odebraných komodit</th>
        </tr>
        <tr>
            <th>Číslo komodity</th>
            <th>Název komodity</th>
            <th class="text-right">Množství v kg</th>
        </tr>
        </thead>
        <tbody>
        <tr ng-repeat="stockReleaseNoteCommodity in stockReleaseNoteCommodityList">
            <td>{{ stockReleaseNoteCommodity.code }}</td>
            <td>{{ stockReleaseNoteCommodity.name }}</td>
            <td class="text-right">{{ stockReleaseNoteCommodity.amount | number:3 }}</td>
        </tr>
        </tbody>
    </table>
</div>
<a href="sentFormList.php" class="btn btn-default btn-xs" role="button">Zpět na odeslané formuláře</a>
<hr>
<footer>
    <p>{copyright} / {printname}</p>
</footer>
</div>
</body>
</html>