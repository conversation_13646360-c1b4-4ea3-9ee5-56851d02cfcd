<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="description" content="">
    <meta name="author" content="">
    <link rel="icon" href="../../favicon.ico">
    <title>Potravinová banka</title>
    <!-- Bootstrap core CSS -->
    <link href="bootstrap/css/bootstrap.min.css?hpbankaversion={hpBankaVersion}" rel="stylesheet">
    <link href="bootstrap-additions/bootstrap-additions.min.css?hpbankaversion={hpBankaVersion}" rel="stylesheet">
    <link href="bootstrap-select/css/bootstrap-select.min.css?hpbankaversion={hpBankaVersion}" rel="stylesheet">
    <!-- Custom styles for this template -->
    <link href="style/hpbanka.css?hpbankaversion={hpBankaVersion}" rel="stylesheet">
    <!-- HTML5 shim and Respond.js for IE8 support of HTML5 elements and media queries -->
    <!--[if lt IE 9]>
      <script src="https://oss.maxcdn.com/html5shiv/3.7.2/html5shiv.min.js?hpbankaversion={hpBankaVersion}"></script>
      <script src="https://oss.maxcdn.com/respond/1.4.2/respond.min.js?hpbankaversion={hpBankaVersion}"></script>
    <![endif]-->
    <script src="js/angular/angular.min.js?hpbankaversion={hpBankaVersion}"></script>
    <script src="js/angular/angular-animate.min.js?hpbankaversion={hpBankaVersion}"></script>
    <script src="js/angular/i18n/angular-locale_cs-cz.js?hpbankaversion={hpBankaVersion}"></script>
    <script src="js/jquery.min.js?hpbankaversion={hpBankaVersion}"></script>
    <script src="bootstrap/js/bootstrap.min.js?hpbankaversion={hpBankaVersion}"></script>
    <script src="bootstrap-select/js/bootstrap-select.min.js?hpbankaversion=={hpBankaVersion}"></script>
    <script src="bootstrap-select/js/i18n/defaults-cs_CZ.min.js?hpbankaversion=={hpBankaVersion}"></script>
    <script src="js/angular-strap/angular-strap.min.js?hpbankaversion={hpBankaVersion}"></script>
    <script src="js/angular-strap/angular-strap.tpl.min.js?hpbankaversion={hpBankaVersion}"></script>
    <script type="text/javascript" src="hpBanka.js?hpbankaversion={hpBankaVersion}"></script>
    <script type="text/javascript" src="hpBankaMenu.js?hpbankaversion={hpBankaVersion}"></script>
    <script type="text/javascript" src="js/angular/selectPicker.js?hpbankaversion={hpBankaVersion}"></script>
    <script type="text/javascript" src="formFiller.js?hpbankaversion={hpBankaVersion}"></script>
</head>

<body ng-app="PBanka" ng-controller="FormFillerController"
    ng-init="basicInformation={basicInformationData}; rightList={rightListData}; formFillerList={formFillerListData};">
    <div>
        <div class="container">
            <img src="images/logo_hpbanka.png" alt="Potravinová banka" id="logoHpBanka">
            <img src="images/logo/{logoName}" alt="Potravinová banka" id="logoPBanka">
            <div id="namePBanka">{pbName}</div>
        </div>
    </div>

    <hp-banka-admin-menu ng-if="formFillerList.indirectly"
        ng-init="activeMainMenu='stock'; activeSubMenu='incomePerStockList'; showSubmenuStock=true;"></hp-banka-admin-menu>
    <hp-banka-user-menu ng-if="!formFillerList.indirectly"
        ng-init="activeMainMenu='activeFormSelector';"></hp-banka-user-menu>

    <div ng-show="formFillerList.formHasBeenFilledAndSent" class="container">

        <div class="page-header">
            <h2>Děkujeme za odeslání</h2>
        </div>
        <h3>Váš vyplněný formulář byl přijat. </h3>
        <h4>V rámci přímého odběru byla vytvořena příjemka č. {{ formFillerList.stockFilledFormId }} a výdejka č.
            {{ formFillerList.stockStockReleaseNoteId }}.</h4>
        <p>&nbsp;</p>
        <p>Nyní se můžete <a href="index.php">odhlásit</a>, <a
                href="sentFormList.php?stockReleaseNoteId={{ formFillerList.stockReleaseNoteId }}&amp;print=1">vytisknout
                formulář</a> nebo přejít na <a href="sentFormList.php">přehled odběrů</a>.</p>
    </div>

    <div ng-hide="formFillerList.formHasBeenFilledAndSent" class="container">
        <div class="page-header">
            <h2>Akce: {{ formFillerList.actionName }}</h2>
        </div>
        <form method="post" name="formFillerForm" class="form-horizontal" novalidate>
            <table class="form-filler-caption">
                <tr>
                    <td><label for="dateOfCollection">Odebráno dne&nbsp;<span
                                class="glyphicon glyphicon-star"></span></label>&nbsp;&nbsp;&nbsp;</td>
                    <td
                        ng-class="{true:'has-error', false:''}[(formFillerForm.consumptionDate.$error.required || formFillerForm.consumptionDate.$error.min || formFillerForm.consumptionDate.$error.date) && !formFillerForm.consumptionDate.$pristine]">
                        <input name="consumptionDate" type="text" class="form-control" id="dateOfCollection"
                            ng-model="consumptionDate" data-min-date="{{ formFillerList.minDate }}" bs-datepicker
                            required>
                        <label class="control-label" for=""
                            ng-show="formFillerForm.consumptionDate.$error.required && !formFillerForm.consumptionDate.$pristine">Tento
                            údaj je povinný.</label>
                        <label class="control-label" for=""
                            ng-show="formFillerForm.consumptionDate.$error.date && !formFillerForm.consumptionDate.$error.min && !formFillerForm.consumptionDate.$pristine">Zadejte
                            datum ve formátu DD.MM.YYYY.</label>
                        <label class="control-label" for=""
                            ng-show="formFillerForm.consumptionDate.$error.min && !formFillerForm.consumptionDate.$pristine && !formFillerForm.consumptionDate.$error.required">Datum
                            nesmí být starší než 10 dní.</label>
                    </td>
                    <td class="col-sm-1"></td>
                    <td><label for="note">Poznámka</label></td>
                    <td><textarea class="form-control" rows="2" ng-model="formFillerList.note"
                            maxlength="30"></textarea></td>
                </tr>
                <tr>
                    <td><label for="supplier">Dodavatel&nbsp;<span
                                class="glyphicon glyphicon-star"></span></label>&nbsp;&nbsp;&nbsp;</td>
                    <td ng-init="supplierList={supplierListData}">
                        <select class="form-control" ng-model="currentSupplier"
                            ng-options="supplier.name for supplier in supplierList"
                            ng-init="inicializeSelectedSupplier({supplierId});" required></select>
                        <input type="hidden" name="supplierId" value="{{ currentSupplier.supplierId }}">
                    </td>
                    <td class="col-sm-1"></td>
                    <td><label for="giftValue">Hodnota daru uvedená dodavatelem v {currencySymbol}&nbsp;<span
                                class="glyphicon glyphicon-star"></span></label>&nbsp;&nbsp;&nbsp;</td>
                    <td
                        ng-class="{true:'has-error', false:''}[(formFillerForm.giftValue.$error.required || formFillerForm.giftValue.$error.pattern) && !formFillerForm.giftValue.$pristine]">
                        <input name="giftValue" type="text" class="form-control" id="giftValue"
                            ng-model="formFillerList.giftValue" ng-pattern="regNumber" required>
                        <label class="control-label" for=""
                            ng-show="formFillerForm.giftValue.$error.required && !formFillerForm.giftValue.$pristine">Tento
                            údaj je povinný.</label>
                        <label class="control-label" for=""
                            ng-show="formFillerForm.giftValue.$error.pattern && !formFillerForm.giftValue.$pristine">Prosím,
                            zadejte kladné číslo.</label>
                    </td>
                </tr>

                <tr ng-if="{indirectly}">
                    <td><label for="customer">Odběratel&nbsp;<span
                                class="glyphicon glyphicon-star"></span></label>&nbsp;&nbsp;&nbsp;</td>
                    <td ng-init="customerList={customerListData}">
                        <select class="form-control selectpicker" data-live-search="true" ng-model="currentCustomer"
                            ng-options="customer.name for customer in customerList"
                            ng-change="setCustomer(currentCustomer)" required select-picker></select>
                    </td>
                </tr>
            </table>
            <br>
            <h4 ng-init="commodityList={commodityListData};">Uveďte množství odebraných komodit:&nbsp;<span
                    class="glyphicon glyphicon-star"></span></h4>
            <div class="form-group" ng-repeat="commodity in commodityList">
                <label class="col-sm-5 control-label text-left" for="name{{ $index+1 }}">{{ commodity.code }} &nbsp;
                    {{ commodity.name }}</label>
                <div ng-class="{true:'has-error', false:''}[formFillerForm.name{{ $index+1 }}.$error.pattern && !formFillerForm.name{{ $index+1 }}.$pristine]"
                    class="input-group col-sm-2">
                    <input type="text" class="form-control" name="name{{ $index+1 }}" id="name{{ $index+1 }}"
                        placeholder="např. 1.005" ng-change="countAmount();" ng-pattern="regNumber"
                        ng-model="commodity.amount">
                    <div class="input-group-addon">{{ commodity.unit }}</div>
                </div>
                <strong class="col-sm-7 control-label text-right text-danger"
                    ng-show="formFillerForm.name{{ $index+1 }}.$error.pattern">Prosím, zadejte kladné číslo.</strong>
            </div>
            <div class="col-sm-7 text-right"><span class="glyphicon glyphicon-star"></span> Povinné údaje.</div>
            <div class="col-sm-5 text-right">&nbsp;</div>
            <h5>Hmotnost zboží celkem: <b>{{ totalAmount | number:3 }} kg</b>.</h5>

            <div class="form-group">
                <div class="col-sm-1">
                    <button ng-if="formFillerList.indirectly" name="save" class="btn btn-success" title="odeslat"
                        ng-click="saveFormFiller();" ng-disabled="!canSave();"><span
                            class="glyphicon glyphicon-save"></span>&nbsp;&nbsp;Odeslat</button>
                    <button ng-if="!formFillerList.indirectly" name="save" class="btn btn-success" title="odeslat"
                        ng-click="showModal();" ng-disabled="!canSave();"><span
                            class="glyphicon glyphicon-save"></span>&nbsp;&nbsp;Odeslat</button>
                </div>
            </div>
            <h5>
                <!-- Vyplněný formulář bude zaslán potravinové bance a kopie Vám bude odeslána na email "<EMAIL>".</b> -->
                Kopie formuláře bude zaslaná na email <b> "{{ formFillerList.customerEmail }}"</b>.
            </h5>
        </form>
        <hr>
        <footer>
            <p>{copyright}</p>
        </footer>
    </div> <!-- /container -->
</body>

</html>