<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="description" content="">
    <meta name="author" content="">
    <link rel="icon" href="../../favicon.ico">
    <title>Potravinová banka</title>
    <!-- Bootstrap core CSS -->
    <link href="bootstrap/css/bootstrap.min.css?hpbankaversion={hpBankaVersion}" rel="stylesheet">
    <link href="bootstrap-additions/bootstrap-additions.min.css?hpbankaversion={hpBankaVersion}" rel="stylesheet">
    <!-- Custom styles for this template -->
    <link href="style/hpbanka.css?hpbankaversion={hpBankaVersion}" rel="stylesheet"> 
    <!-- HTML5 shim and Respond.js for IE8 support of HTML5 elements and media queries -->
    <!--[if lt IE 9]>
      <script src="https://oss.maxcdn.com/html5shiv/3.7.2/html5shiv.min.js?hpbankaversion={hpBankaVersion}"></script>
      <script src="https://oss.maxcdn.com/respond/1.4.2/respond.min.js?hpbankaversion={hpBankaVersion}"></script>
    <![endif]-->
    <script src="js/angular/angular.min.js?hpbankaversion={hpBankaVersion}"></script>
    <script src="js/angular/i18n/angular-locale_cs-cz.js?hpbankaversion={hpBankaVersion}"></script>
    <script src="js/jquery.min.js?hpbankaversion={hpBankaVersion}"></script>
    <script src="bootstrap/js/bootstrap.min.js?hpbankaversion={hpBankaVersion}"></script>
    <script src="js/angular-strap/angular-strap.min.js?hpbankaversion={hpBankaVersion}"></script>
    <script src="js/angular-strap/angular-strap.tpl.min.js?hpbankaversion={hpBankaVersion}"></script> 
    <script type="text/javascript" src="hpBanka.js?hpbankaversion={hpBankaVersion}"></script>
    <script type="text/javascript" src="hpBankaMenu.js?hpbankaversion={hpBankaVersion}"></script>    
    <script type="text/javascript" src="formList.js?hpbankaversion={hpBankaVersion}"></script>
  </head>

  <body ng-app="PBanka" ng-controller="FormListController" ng-init="basicInformation={basicInformationData}; rightList={rightListData}; formList={formListData};">
  <div >
    <div class="container">
      <img src="images/logo_hpbanka.png" alt="Potravinová banka">
    </div>
  </div>
  <hp-banka-user-menu ng-init="activeMainMenu='activeFormSelector';"></hp-banka-user-menu>
  <div class="container" ng-init="showNoForm={showNoForm}">
  <h3 ng-if="showNoForm" >Momentálně není k dispozici pro vyplnění žádný formulář.</h3>
  <h4 ng-if="showNoForm" >Neprobíhá žádná aktuální akce potravinové banky.</h4>
  <form ng-if="!showNoForm" method="post" action="formList.php" name="formListForm" novalidate>
  <div class="page-header">
    <h4>Vyberte formulář přímého odběru</h4>
  </div>
  <table class="table">
    <thead>
      <tr><th>Č.</th><th>Název formuláře</th><th>Přístupný do</th><th>Akce</th></tr> 
    </thead> 
    <tbody> 
    <tr ng-repeat="form in formList">
      <th scope="row">{{ form.order }}</th><td>{{ form.actionName }}</td><td>{{ form.validTo }}</td>
      <td>
        <a class="btn btn-success" href="formFiller.php?formId={{ form.formId }}&list=1" title="komodity"><span class="glyphicon glyphicon-grain"></span>&nbsp;&nbsp;vyplnit formulář</a>
      </td>
    </tr> 
    </tbody> 
  </table>
  
  <input type="hidden" name="formList" value="{{ formList }}" />
  </form>
  <hr>
  <footer>
    <p>{copyright}</p>
  </footer>
  </div> <!-- /container -->
  </body>
</html>