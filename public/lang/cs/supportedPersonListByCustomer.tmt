<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="description" content="">
    <meta name="author" content="">
    <link rel="icon" href="../../favicon.ico">
    <title>Potravinová banka</title>
    <!-- Bootstrap core CSS -->
    <link href="bootstrap/css/bootstrap.min.css?hpbankaversion={hpBankaVersion}" rel="stylesheet">
    <link href="bootstrap-additions/bootstrap-additions.min.css?hpbankaversion={hpBankaVersion}" rel="stylesheet">
    <!-- Custom styles for this template -->
    <link href="style/hpbanka.css?hpbankaversion={hpBankaVersion}" rel="stylesheet"> 
    <!-- HTML5 shim and Respond.js for IE8 support of HTML5 elements and media queries -->
    <!--[if lt IE 9]>
      <script src="https://oss.maxcdn.com/html5shiv/3.7.2/html5shiv.min.js?hpbankaversion={hpBankaVersion}"></script>
      <script src="https://oss.maxcdn.com/respond/1.4.2/respond.min.js?hpbankaversion={hpBankaVersion}"></script>
    <![endif]-->
    <script src="js/angular/angular.min.js?hpbankaversion={hpBankaVersion}"></script>
    <script src="js/angular/i18n/angular-locale_cs-cz.js?hpbankaversion={hpBankaVersion}"></script>
	  <script src="js/jquery.min.js?hpbankaversion={hpBankaVersion}"></script>
	  <script src="bootstrap/js/bootstrap.min.js?hpbankaversion={hpBankaVersion}"></script> 
	  <script src="js/angular-strap/angular-strap.min.js?hpbankaversion={hpBankaVersion}"></script>
    <script src="js/angular-strap/angular-strap.tpl.min.js?hpbankaversion={hpBankaVersion}"></script> 
    <script type="text/javascript" src="hpBanka.js?hpbankaversion={hpBankaVersion}"></script>
	  <script type="text/javascript" src="hpBankaMenu.js?hpbankaversion={hpBankaVersion}"></script>   
    <script type="text/javascript" src="supportedPersonListByCustomer.js?hpbankaversion={hpBankaVersion}"></script>
    <script>
      $(function () {
          $('[data-toggle="tooltip"]').tooltip({container: 'body'})
        })
    </script>       
  </head>

  <body ng-app="PBanka" ng-controller="SupportedPersonListByCustomerController" ng-init="basicInformation={basicInformationData}; rightList={rightListData}; supportedPersonListByCustomer={supportedPersonListByCustomerData};">
  <div>
    <div class="container">
      <img src="images/logo_hpbanka.png" alt="Potravinová banka" id="logoHpBanka">
      <img src="images/logo/{logoName}" alt="Potravinová banka" id="logoPBanka">
      <div id="namePBanka">{pbName}</div>
    </div>
  </div>
  <hp-banka-admin-menu ng-init="activeMainMenu='supportedPersonListByCustomer';"></hp-banka-admin-menu>
  <div class="container">
  <form method="post" name="formCustomer" action="supportedPersonListByCustomer.php" novalidate>
  <br>
  <table class="table" ng-init="filterList={filterListData};">
    <thead>  
      <tr>
        <th>Č.</th>
        <th><a class="sord" ng-click="order('name')">Název <span ng-show="sordBy === 'name'" class="glyphicon" ng-class="{true:' glyphicon-menu-up', false:' glyphicon-menu-down'}[reverse]" ></span></a></th>
        <th nowrap="nowrap">Kontaktní osoba</th>
        <th>Organizace</th>
        <th><a class="sord" ng-click="order('yearMonth')">Vykázané období <span ng-show="sordBy === 'yearMonth'" class="glyphicon" ng-class="{true:' glyphicon-menu-up', false:' glyphicon-menu-down'}[reverse]" ></span></a></th>
        <th>Počet podpořených osob</th>
        <th>Akce</th>
      </tr> 
      <tr>
        <td></td>
        <td><input type="text" size="8" name="filterByName" ng-model="filterList.name"></td>
        <td><input type="text" size="8" name="filterByContactPerson" ng-model="filterList.contactPerson"></td>
        <td><input type="text" size="8" name="filterByGroupName" ng-model="filterList.groupName"></td>
        <td nowrap="nowrap"><input type="text"size="10" name="filterByDateFrom" ng-model="filterList.dateFrom" placeholder="Datum od" bs-datepicker></td>
        <td></td>
        <td nowrap="nowrap">
          <button name="filter" class="btn btn-warning" title="vybrat"><span class="glyphicon glyphicon-filter"></span>&nbsp;&nbsp;vybrat</button>
          <a href="supportedPersonListByCustomer.php" class="btn btn-warning" title="zrušit filtr"><span class="glyphicon glyphicon-erase"></span>&nbsp;&nbsp;zrušit filtr</a>
        </td>
      </tr>
    </thead> 
    
    <tbody> 
    <tr ng-repeat="customer in supportedPersonListByCustomer | orderBy:sordBy:reverse">
	    <th scope="row">{{ $index + 1 }}</th>
	    <td data-toggle="tooltip" data-placement="right" title="Adresa: {{ customer.address }}">{{ customer.name }}</td>
	    <td>{{ customer.contactPerson }}</td>
	    <td>{{ customer.groupName }}</td>
	    <td>{{ customer.yearMonth | date : 'M / yyyy' }}</td>
	    <td>{{ customer.sumNumberOfSupportedPersons }}</td>
	    <td nowrap>
	      <a ng-show="rightList.supportedPersonListByCustomer.fullAccess" class="btn btn-success" href="supportedPersonList.php?customerId={{ customer.customerId }}&period={{ customer.yearMonth }}" title="upravit"><span class="glyphicon glyphicon-edit"></span>&nbsp;&nbsp;upravit</a>
	    </td>    
    </tr> 
    
    </tbody> 
  </table>
  </form>
  <hr>
  <footer>
    <p>{copyright}</p>
  </footer>
  </div> <!-- /container -->
  </body>
</html>