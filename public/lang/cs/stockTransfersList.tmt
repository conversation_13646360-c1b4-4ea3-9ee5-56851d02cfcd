<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="description" content="">
    <meta name="author" content="">
    <link rel="icon" href="../../favicon.ico">
    <title>Potravinová banka</title>
    <!-- Bootstrap core CSS -->
    <link href="bootstrap/css/bootstrap.min.css?hpbankaversion={hpBankaVersion}" rel="stylesheet">
    <link href="bootstrap-additions/bootstrap-additions.min.css?hpbankaversion={hpBankaVersion}" rel="stylesheet">
    <!-- Custom styles for this template -->
    <link href="style/hpbanka.css?hpbankaversion={hpBankaVersion}" rel="stylesheet">
    <!-- HTML5 shim and Respond.js for IE8 support of HTML5 elements and media queries -->
    <!--[if lt IE 9]>
      <script src="https://oss.maxcdn.com/html5shiv/3.7.2/html5shiv.min.js?hpbankaversion={hpBankaVersion}"></script>
      <script src="https://oss.maxcdn.com/respond/1.4.2/respond.min.js?hpbankaversion={hpBankaVersion}"></script>
    <![endif]-->
    <script src="js/angular/angular.min.js?hpbankaversion={hpBankaVersion}"></script>
    <script src="js/angular/i18n/angular-locale_cs-cz.js?hpbankaversion={hpBankaVersion}"></script>
    <script src="js/jquery.min.js?hpbankaversion={hpBankaVersion}"></script>
    <script src="bootstrap/js/bootstrap.min.js?hpbankaversion={hpBankaVersion}"></script>
    <script src="js/angular-strap/angular-strap.min.js?hpbankaversion={hpBankaVersion}"></script>
    <script src="js/angular-strap/angular-strap.tpl.min.js?hpbankaversion={hpBankaVersion}"></script>
    <script type="text/javascript" src="hpBanka.js?hpbankaversion={hpBankaVersion}"></script>
    <script type="text/javascript" src="hpBankaMenu.js?hpbankaversion={hpBankaVersion}"></script>
    <script type="text/javascript" src="transferCommodityAmount.js?hpbankaversion={hpBankaVersion}"></script>
    <script type="text/javascript" src="numberLimits.js?hpbankaversion={hpBankaVersion}"></script>
    <script type="text/javascript" src="stockSelectList.js?hpbankaversion={hpBankaVersion}"></script>
    <script type="text/javascript" src="stockTransfersList.js?hpbankaversion={hpBankaVersion}"></script>
    <script>
      $(function () {
          $('[data-toggle="tooltip"]').tooltip({container: 'body'})
        })
    </script>
  </head>

  <body ng-app="PBanka" ng-controller="StockTransfersListController" ng-init="basicInformation={basicInformationData}; rightList={rightListData}; stockTransfersList={stockTransfersListData};">
  <div >
    <div class="container">
      <img src="images/logo_hpbanka.png" alt="Potravinová banka" id="logoHpBanka">
      <img src="images/logo/{logoName}" alt="Potravinová banka" id="logoPBanka">
      <div id="namePBanka">{pbName}</div>
    </div>
  </div>
  <hp-banka-admin-menu ng-init="activeMainMenu='stock'; activeSubMenu='stockTransfersList'; showSubmenuStock=true;"></hp-banka-admin-menu>

  <div ng-show="showStockTransfersList" class="container" ng-init="stockList={stockListData}; initCurrentStockId={currentStockId};currentStock=[];summaryItem={summaryItem}; showFirstStockReleaseNote({stockReleaseNoteId})">
  <h3 ng-show="initCurrentStockId==null">Nemáte nastavený sklad, kontakujte administrátora.</h3>
  <stock-select-list ng-show="!showEdit && !showInfoAboutCreateDonationAgreement && initCurrentStockId!=null" stock-list="stockList" current-stock="currentStock" init-current-stock-id="initCurrentStockId" summary-item="summaryItem" change-stock-fn="changeStock(newCurrentStock)"></stock-select-list>
  <br/>
  <div ng-hide="showEdit" class="well well-sm"><strong>Množství celkem: {{ totalAmount | number:3 }} kg.</strong></div>

  <form ng-show="!showEdit && !showInfoAboutCreateDonationAgreement && initCurrentStockId!=null" method="post" action="stockTransfersList.php" novalidate>
  <table class="table" ng-init="filterList={filterListData};">
    <thead>
      <tr>
          <th>Číslo převodky</th>
          <th>Vydávající sklad</th>
          <th>Přijímající sklad</th>
          <th>Vyskladněno dne</th>
          <th class="text-right">Množství v kg</th>
          <th></th>
          <th>Akce</th></tr>
      <tr>
        <td></td>
        <td></td>
        <td></td>
        <!-- <td><input type="text" size="5" class="filter-height"  name="filterByStockTransferId" ng-model="filterList.stockTransferId"></td>
        <td ng-init="targetStockList={targetStockListData}">
          <select ng-init="inicializeSelectedTargetStock(filterList.targetStockId)" class="filter-height" ng-model="filterList.targetStock" ng-options="targetStock.name for targetStock in targetStockList"></select>
          <input type="hidden" name="filterByTargetStockId" value="{{ filterList.targetStock.stockId }}" />
        </td> -->
        <td nowrap="nowrap"><input type="text" class="filter-height" size="10" name="filterByDateFrom" ng-model="filterList.dateFrom" bs-datepicker>&nbsp;/&nbsp;<input type="text" class="filter-height"  size="10" name="filterByDateTo" ng-model="filterList.dateTo" bs-datepicker></td>
        <td></td>
        <td></td>
        <td nowrap="nowrap">
          <button name="filter" class="btn btn-warning" title="vybrat"><span class="glyphicon glyphicon-filter"></span>&nbsp;&nbsp;vybrat</button>
          <a href="stockTransfersList.php" class="btn btn-warning" title="zrušit výběr"><span class="glyphicon glyphicon-erase"></span>&nbsp;&nbsp;výchozí období</a>
        </td>
      </tr>
      <tr class="button" ng-if="rightList.stockTransfersList.fullAccess">
        <td colspan="6"></td><td><a class="btn btn-warning" ng-disabled="currentStock.stockId==0" ng-click="goToStockTransfer(currentStock);" href="#" title="prevest"><span class="glyphicon glyphicon-plus"></span>&nbsp;&nbsp;převést</button></td>
      </tr>
    </thead>
    <tbody>
    <tr ng-repeat="stockTransfer in stockTransfersList" ng-init="countTotalAmount();">
      <td><span ng-show="stockTransfer.isNote" data-toggle="tooltip" data-placement="top" title="{{ stockTransfer.note }}" class="glyphicon glyphicon-comment"></span> {{ stockTransfer.stockTransferId }}</td>
      <td>{{ stockTransfer.sourceStockName }}</td>
      <td>{{ stockTransfer.targetStockName }}</td>
      <td>{{ stockTransfer.issuedAt | date: fullDate }}</td>
      <td class="text-right">{{ stockTransfer.totalAmount | number:3 }}</td>
      <td></td>
      <td nowrap="nowrap">
        <a class="btn btn-success" target="_blank" href="stockTransfer2PDF.php?transferId={{ stockTransfer.transferId }}" title="tisk" role="button"><span class="glyphicon glyphicon-print"></span>&nbsp;&nbsp;tisk převodky</a>
        <a ng-if="rightList.stockTransfersList.fullAccess" class="btn btn-success" href="#" title="upravit" ng-click="editStockTransfer(stockTransfer.transferId);"><span class="glyphicon glyphicon-edit"></span>&nbsp;&nbsp;upravit</a>
        <button type="button" ng-if="stockTransfer.itCanBeDeleted" name="delete" class="btn btn-success" title="smazat" ng-click="showModal(stockTransfer.transferId);"><span class="glyphicon glyphicon-trash"></span>&nbsp;&nbsp;smazat</button>
      </td>
    </tr>
    </tbody>
  </table>
  </form>
  <form ng-show="showEdit" method="post" name="stockTransferForm" class="form-horizontal" novalidate >
  <h2>Převod ze skladu</h2>

  <table class="form-filler-caption">
    <tr>
        <td colspan="2"><label>Vydávající sklad: {{ stockTransfer.nameSourceStock }}</label></td>
      </tr>
      <tr>
      <td>Převedeno dne:&nbsp; </td>
      <td>{{ stockTransfer.issuedAt | date: fullDate }} </td>
      <td class="col-sm-1"></td>
      <td>Přijímající sklad:&nbsp; </td>
      <td> {{ stockTransfer.nameTargetStock }}</td>
    </tr>
    <tr>
      <td>Poznámka&nbsp;</td>
      <td colspan="4"><textarea id="note" rows="1" class="form-control" ng-model="stockTransfer.note" maxlength="30"></textarea>
    </tr>
  </table>
  <h4>Uveďte množství převedených komodit:&nbsp;<span class="glyphicon glyphicon-star"></span></h4>
  <div class="form-group" ng-repeat="commodity in stockTransfer.transferCommodityList">
    <transfer-commodity-amount commodity="commodity" form="stockTransferForm" id="$index + 1" count-amount-fn="countAmount()"></transfer-commodity-amount>
  </div>


    <div class="col-sm-7 text-right"><span class="glyphicon glyphicon-star"></span> Povinné údaje.</div><div class="col-sm-5 text-right">&nbsp;</div>
    <h5>Hmotnost převedeného zboží celkem: <b>{{ stockTransferTotalAmount | number:3 }} kg</b>.</h5>

    <div class="form-group">
      <div class="col-sm-5">
        <button ng-if="rightList.stockReleaseNoteList.fullAccess" name="save" ng-click="saveTransferCommodity();" class="btn btn-success" title="uložit" ng-disabled="!canSave();"><span class="glyphicon glyphicon-save"></span>&nbsp;&nbsp;Uložit</button>
        <a class="btn btn-success" title="zpět na seznam" ng-click="showEdit=false;"><span class="glyphicon glyphicon-hand-left"></span>&nbsp;&nbsp;zpět na seznam</a>
      </div>
    </div>

  <input type="hidden" name="stockTransfer" value="{{ stockTransfer }}"/>
  <input type="hidden" name="filterList" value="{{ filterList }}"/>
  </form>
  <hr>
  <footer>
    <p>{{ basicInformation.copyright }}</p>
  </footer>
  </div>
  </body>
</html>
