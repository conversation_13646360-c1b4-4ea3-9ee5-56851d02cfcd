<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="description" content="">
    <meta name="author" content="">
    <link rel="icon" href="../../favicon.ico">
    <title>Potravinová banka</title>
    <!-- Bootstrap core CSS -->
    <link href="bootstrap/css/bootstrap.min.css?hpbankaversion={hpBankaVersion}" rel="stylesheet">
    <link href="bootstrap-additions/bootstrap-additions.min.css?hpbankaversion={hpBankaVersion}" rel="stylesheet">
    <!-- Custom styles for this template -->
    <link href="style/hpbanka.css?hpbankaversion={hpBankaVersion}" rel="stylesheet">
    <!-- HTML5 shim and Respond.js for IE8 support of HTML5 elements and media queries -->
    <!--[if lt IE 9]>
      <script src="https://oss.maxcdn.com/html5shiv/3.7.2/html5shiv.min.js?hpbankaversion={hpBankaVersion}"></script>
      <script src="https://oss.maxcdn.com/respond/1.4.2/respond.min.js?hpbankaversion={hpBankaVersion}"></script>
    <![endif]-->
    <script src="js/angular/angular.min.js?hpbankaversion={hpBankaVersion}"></script>
    <script src="js/angular/i18n/angular-locale_cs-cz.js?hpbankaversion={hpBankaVersion}"></script>
    <script src="js/jquery.min.js?hpbankaversion={hpBankaVersion}"></script>
    <script src="bootstrap/js/bootstrap.min.js?hpbankaversion={hpBankaVersion}"></script>
    <script src="js/angular-strap/angular-strap.min.js?hpbankaversion={hpBankaVersion}"></script>
    <script src="js/angular-strap/angular-strap.tpl.min.js?hpbankaversion={hpBankaVersion}"></script>
    <script type="text/javascript" src="hpBankaMenu.js?hpbankaversion={hpBankaVersion}"></script>
    <script type="text/javascript" src="supportedPersonList.js?hpbankaversion={hpBankaVersion}"></script>
  </head>

  <body ng-app="PBanka" ng-controller="SupportPersonListController" ng-init="basicInformation={basicInformationData}; rightList={rightListData}; supportPersonList={supportPersonListData};">
  <div role="logos">
    <div class="container">
      <img src="images/logo_hpbanka.png" alt="Potravinová banka" id="logoHpBanka">
      <img src="images/logo/{logoName}" alt="Potravinová banka" id="logoPBanka">
      <div id="namePBanka">{pbName}</div>
    </div>
  </div>
  
  <!-- IF {isUser} -->
  <hp-banka-user-menu ng-init="activeMainMenu='supportedPersonList';"></hp-banka-user-menu>
  <!-- ENDIF -->
  <!-- IFNOT {isUser} -->
  <hp-banka-admin-menu ng-init="activeMainMenu='supportedPersonListByCustomer';"></hp-banka-admin-menu>
  <!-- ENDIFNOT -->
  
  <br>
  <div class="container">
  <form method="post" class="form-inline">
  <div class="form-group">
    <label for="month" ng-init="monthList={monthListData}">Měsíc:&nbsp;</label>
    <select ng-init="inicializeMonthList({initMonth});" ng-change="setYearAndMonth();" id="month" ng-model="supportPersonList.selectedMonth" ng-options="month.name for month in monthList" class="form-control"></select>
    &nbsp;&nbsp;
    <label for="year" ng-init="yearList={yearListData}">Rok:&nbsp;</label>
    <select ng-init="initializeYearList({initYear});" ng-change="setYearAndMonth();" id="year" ng-model="supportPersonList.selectedYear" ng-options="year.name for year in yearList" class="form-control"></select>
  </div>
  </form>
  <br>
  <form method="post" action="supportedPersonList.php" name="supportedPersonsForm" novalidate class="form-horizontal">
  <h4>Uveďte počet podpořených osob:</h4>
  <div class="form-group" ng-repeat="supportPerson in supportPersonList.supportedPersons">
    <label class="col-sm-4 control-label text-left" for="name{{ $index+1 }}">{{ supportPerson.name }}</label>
    <div ng-class="{true:'has-error', false:''}[supportedPersonsForm.name{{ $index+1 }}.$error.pattern && !supportedPersonsForm.name{{ $index+1 }}.$pristine]" class="input-group col-sm-2">
      <input type="text" class="form-control" name="name{{ $index+1 }}" id="name{{ $index+1 }}" placeholder="např. 10" ng-pattern="regNumber" ng-model="supportPerson.numberOfSupportedPersons">
      <div class="input-group-addon">osob</div>
    </div>
    <strong class="col-sm-7 control-label text-right text-danger" ng-show="supportedPersonsForm.name{{ $index+1 }}.$error.pattern">Prosím, zadejte kladné číslo.</strong>
  </div> 
  
  <div class="form-group">
    <div class="col-sm-4">
      <button ng-show="canShowSave();" name="save" class="btn btn-success" title="uložit" ng-disabled="!canSave();"><span class="glyphicon glyphicon-save"></span>&nbsp;&nbsp;Uložit</button>
      <!-- IFNOT {isUser} -->
      <a class="btn btn-success" title="zpět na seznam" href="supportedPersonListByCustomer.php"><span class="glyphicon glyphicon-hand-left"></span>&nbsp;&nbsp;zpět na seznam</a>
      <!-- ENDIFNOT -->
    </div>
  </div>
  <input name="customerId" value="{customerId}" type="hidden" />
  <input name="supportPersonList" value="{{ supportPersonList }}" type="hidden" />   
  </form>
  
  <hr>
  <footer>
    <p>{{ basicInformation.copyright }}</p>
  </footer>
  </div>
  </body>
</html>