<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="description" content="">
    <meta name="author" content="">
    <link rel="icon" href="../../favicon.ico">
    <title>Potravinová banka</title>
    <!-- Bootstrap core CSS -->
    <link href="bootstrap/css/bootstrap.min.css?hpbankaversion={hpBankaVersion}" rel="stylesheet">
    <link href="bootstrap-additions/bootstrap-additions.min.css?hpbankaversion={hpBankaVersion}" rel="stylesheet">
    <!-- Custom styles for this template -->
    <link href="style/hpbanka.css?hpbankaversion={hpBankaVersion}" rel="stylesheet"> 
    <!-- HTML5 shim and Respond.js for IE8 support of HTML5 elements and media queries -->
    <!--[if lt IE 9]>
      <script src="https://oss.maxcdn.com/html5shiv/3.7.2/html5shiv.min.js?hpbankaversion={hpBankaVersion}"></script>
      <script src="https://oss.maxcdn.com/respond/1.4.2/respond.min.js?hpbankaversion={hpBankaVersion}"></script>
    <![endif]-->
    <script src="js/angular/angular.min.js?hpbankaversion={hpBankaVersion}"></script>
    <script src="js/angular/i18n/angular-locale_cs-cz.js?hpbankaversion={hpBankaVersion}"></script>
    <script src="js/angular-strap/angular-strap.min.js?hpbankaversion={hpBankaVersion}"></script>
    <script src="js/angular-strap/angular-strap.tpl.min.js?hpbankaversion={hpBankaVersion}"></script>
    <script type="text/javascript" src="hpBanka.js?hpbankaversion={hpBankaVersion}"></script>  
    <script type="text/javascript" src="hpBankaMenu.js?hpbankaversion={hpBankaVersion}"></script> 
    <script type="text/javascript" src="stockSelectList.js?hpbankaversion={hpBankaVersion}"></script>
    <script type="text/javascript" src="supply.js?hpbankaversion={hpBankaVersion}"></script>
  </head>

  <body ng-app="PBanka" ng-controller="SupplyController" ng-init="report={reportData};basicInformation={basicInformationData}; rightList={rightListData};">
  <div>
    <div class="container">
      <img src="images/logo_hpbanka.png" alt="Potravinová banka" id="logoHpBanka">
      <img src="images/logo/{logoName}" alt="Potravinová banka" id="logoPBanka">
      <div id="namePBanka">{pbName}</div>
    </div>
  </div>
  <hp-banka-admin-menu ng-init="activeMainMenu='stock'; activeSubMenu='supply'; showSubmenuStock=true;"></hp-banka-admin-menu>
  <div class="container" ng-init="stockList={stockListData}; initCurrentStockId={currentStockId};currentStock=[];summaryItem={summaryItem};">
  <h3 ng-show="initCurrentStockId==null">Nemáte nastavený sklad, kontakujte administrátora.</h3>
  <stock-select-list ng-show="initCurrentStockId!=null" stock-list="stockList" current-stock="currentStock" init-current-stock-id="initCurrentStockId" summary-item="summaryItem" change-stock-fn="changeStock(newCurrentStock)"></stock-select-list>
  <div class="table-responsive" ng-show="!report.showEmptyReport && initCurrentStockId!=null">
  <table class="table">
    <thead>
      <tr><th class="text-right">Kód</th><th>Název komodity</th><th class="text-right">Množství (kg)</th><th class="text-right">Hodnota ({currencySymbol})</th></tr>
    </thead>
    <tbody ng-init="commoditySummary={commoditySummary};">
      <tr ng-repeat="commodity in commoditySummary.commodities" >
        <td class="text-right">{{ commodity.code }}</td>
        <td>{{ commodity.name }}</td>
        <td class="text-right">{{ commodity.amount | number:3 }}</td>
        <td class="text-right">{{ commodity.value | number:2 }}</td>
      </tr>
      <tr>
        <th class="text-right">Celkem</th>
        <th></th>
        <th class="text-right">{{ commoditySummary.totalAmount | number:3 }}</th>
        <th class="text-right">{{ commoditySummary.totalValue | number:2 }}</th>
      </tr>
    </tbody>
  </table>
  </div>

  <h5 ng-show="report.showEmptyReport">Nejsou žádná data.</h5>
  <a ng-show="initCurrentStockId!=null" class="btn btn-success" href="supply.php?export=1&amp;stockId={{ currentStock.stockId }}" role="button" title="export do CSV">export do CSV</a>
  <hr>
  <footer>
    <p>{copyright}</p>
  </footer>
  </div> <!-- /container -->
  </body>
</html>