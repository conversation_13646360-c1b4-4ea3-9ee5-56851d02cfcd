<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="description" content="">
    <meta name="author" content="">
    <link rel="icon" href="../../favicon.ico">
    <title>Potravinová banka</title>
    <!-- Bootstrap core CSS -->
    <link href="bootstrap/css/bootstrap.min.css?hpbankaversion={hpBankaVersion}" rel="stylesheet">
    <link href="bootstrap-additions/bootstrap-additions.min.css?hpbankaversion={hpBankaVersion}" rel="stylesheet">
    <!-- Custom styles for this template -->
    <link href="style/hpbanka.css?hpbankaversion={hpBankaVersion}" rel="stylesheet">
    <!-- HTML5 shim and Respond.js for IE8 support of HTML5 elements and media queries -->
    <!--[if lt IE 9]>
      <script src="https://oss.maxcdn.com/html5shiv/3.7.2/html5shiv.min.js?hpbankaversion={hpBankaVersion}"></script>
      <script src="https://oss.maxcdn.com/respond/1.4.2/respond.min.js?hpbankaversion={hpBankaVersion}"></script>
    <![endif]-->
    <script src="js/angular/angular.min.js?hpbankaversion={hpBankaVersion}"></script>
    <script src="js/angular/i18n/angular-locale_cs-cz.js?hpbankaversion={hpBankaVersion}"></script>
    <script src="js/jquery.min.js?hpbankaversion={hpBankaVersion}"></script>
    <script src="bootstrap/js/bootstrap.min.js?hpbankaversion={hpBankaVersion}"></script>
    <script type="text/javascript" src="hpBanka.js?hpbankaversion={hpBankaVersion}"></script>
    <script type="text/javascript" src="hpBankaMenu.js?hpbankaversion={hpBankaVersion}"></script>
    <script type="text/javascript" src="manualList.js?hpbankaversion={hpBankaVersion}"></script>
</head>

<body ng-app="PBanka" ng-controller="ManualListController"
    ng-init="basicInformation={basicInformationData}; rightList={rightListData}; manual={manualData};">
    <div>
        <div class="container">
            <img src="images/logo_hpbanka.png" alt="Potravinová banka" id="logoHpBanka">
            <img src="images/logo/{logoName}" alt="Potravinová banka" id="logoPBanka">
            <div id="namePBanka">{pbName}</div>
        </div>
    </div>
    <hp-banka-admin-menu ng-init="activeMainMenu='manualList';"></hp-banka-admin-menu>
    <div class="container">
        <br>
        
        <!-- Zprávy o úspěchu -->
        <!-- IF {showSaveMessage} -->
        <div class="alert alert-success alert-dismissible" role="alert">
            <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                <span aria-hidden="true">&times;</span>
            </button>
            <strong>Úspěch!</strong> Manuál byl úspěšně uložen.
        </div>
        <!-- ENDIF -->
        
        <!-- IF {showDeleteMessage} -->
        <div class="alert alert-success alert-dismissible" role="alert">
            <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                <span aria-hidden="true">&times;</span>
            </button>
            <strong>Úspěch!</strong> Manuál byl úspěšně smazán.
        </div>
        <!-- ENDIF -->
        
        <!-- Chybové zprávy -->
        <div ng-show="showErrorMessage" class="alert alert-danger alert-dismissible" role="alert">
            <button type="button" class="close" ng-click="hideMessages()" aria-label="Close">
                <span aria-hidden="true">&times;</span>
            </button>
            <strong>Chyba!</strong> {{ errorMessage }}
        </div>

        <div class="page-header">
            <h1>Manuály</h1>
            <p class="lead">Zde můžete spravovat obsah manuálů pro vaši potravinovou banku.</p>
        </div>

        <div ng-show="canRead()">
            <form name="manualForm" novalidate>
                <div class="form-group">
                    <label for="manualTitle" class="control-label">Název manuálu <span class="glyphicon glyphicon-star"></span></label>
                    <input type="text" 
                           class="form-control" 
                           id="manualTitle" 
                           name="title"
                           ng-model="manual.title" 
                           ng-disabled="!canEdit()"
                           placeholder="Zadejte název manuálu"
                           required>
                </div>

                <div class="form-group">
                    <label for="manualContent" class="control-label">Obsah manuálu <span class="glyphicon glyphicon-star"></span></label>
                    <textarea class="form-control" 
                              id="manualContent" 
                              name="content"
                              ng-model="manual.content" 
                              ng-disabled="!canEdit()"
                              rows="20" 
                              placeholder="Zadejte obsah manuálu..."
                              required></textarea>
                    <p class="help-block">Zde můžete vložit jakýkoliv text, instrukce nebo informace pro vaši organizaci.</p>
                </div>

                <div ng-show="canEdit()" class="form-group">
                    <button type="button" 
                            class="btn btn-primary" 
                            ng-click="saveManual()"
                            ng-disabled="!manual.title || !manual.content">
                        <span class="glyphicon glyphicon-floppy-disk"></span>&nbsp;&nbsp;Uložit manuál
                    </button>
                    
                    <button type="button" 
                            class="btn btn-danger" 
                            ng-click="deleteManual()"
                            ng-show="hasContent()"
                            style="margin-left: 10px;">
                        <span class="glyphicon glyphicon-trash"></span>&nbsp;&nbsp;Smazat manuál
                    </button>
                </div>
            </form>
        </div>

        <div ng-show="!canRead()" class="alert alert-warning">
            <strong>Upozornění!</strong> Nemáte oprávnění k zobrazení této stránky.
        </div>
    </div>

    <!-- Placed at the end of the document so the pages load faster -->
    <script src="bootstrap/js/bootstrap.min.js?hpbankaversion={hpBankaVersion}"></script>
</body>

</html>
