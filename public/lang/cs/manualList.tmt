<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="description" content="">
    <meta name="author" content="">
    <link rel="icon" href="../../favicon.ico">
    <title>Potravinová banka</title>
    <!-- Bootstrap core CSS -->
    <link href="bootstrap/css/bootstrap.min.css?hpbankaversion={hpBankaVersion}" rel="stylesheet">
    <link href="bootstrap-additions/bootstrap-additions.min.css?hpbankaversion={hpBankaVersion}" rel="stylesheet">
    <!-- Custom styles for this template -->
    <link href="style/hpbanka.css?hpbankaversion={hpBankaVersion}" rel="stylesheet">
    <!-- Summernote CSS - Bootstrap 3 verze -->
    <link href="https://cdn.jsdelivr.net/npm/summernote@0.8.18/dist/summernote-bs3.min.css" rel="stylesheet">

    <!-- Custom styles for manual editor -->
    <style>
        .manual-view-panel {
            margin-bottom: 20px;
        }

        .manual-view-panel .panel-heading {
            position: relative;
            padding-right: 200px;
        }

        .manual-view-panel .panel-heading .pull-right {
            position: absolute;
            right: 15px;
            top: 10px;
        }

        .manual-content {
            line-height: 1.6;
            font-size: 14px;
        }

        .manual-content h1, .manual-content h2, .manual-content h3,
        .manual-content h4, .manual-content h5, .manual-content h6 {
            margin-top: 20px;
            margin-bottom: 10px;
        }

        .manual-content ul, .manual-content ol {
            margin-bottom: 15px;
        }

        .manual-content p {
            margin-bottom: 10px;
        }

        .note-editor {
            border: 1px solid #ddd;
        }

        .note-toolbar {
            background-color: #f8f9fa;
            border-bottom: 1px solid #ddd;
        }

        .note-btn-group .note-btn {
            border: 1px solid transparent;
            margin: 1px;
        }

        .note-btn-group .note-btn:hover {
            background-color: #e9ecef;
            border-color: #adb5bd;
        }

        .note-dropdown-menu {
            max-height: 300px;
            overflow-y: auto;
            z-index: 1050 !important;
            position: absolute !important;
            display: block !important;
        }

        .note-dropdown-menu.open {
            display: block !important;
        }

        .note-btn-group.open .note-dropdown-menu {
            display: block !important;
        }

        /* Oprava pro dropdown menu */
        .note-toolbar .note-dropdown-menu {
            min-width: 160px;
            background-color: #fff;
            border: 1px solid #ccc;
            border-radius: 4px;
            box-shadow: 0 6px 12px rgba(0,0,0,.175);
        }

        .note-dropdown-menu > li > a {
            display: block;
            padding: 3px 20px;
            clear: both;
            font-weight: normal;
            line-height: 1.42857143;
            color: #333;
            white-space: nowrap;
            text-decoration: none;
        }

        .note-dropdown-menu > li > a:hover {
            background-color: #f5f5f5;
            color: #262626;
        }

        .edit-mode-buttons {
            margin-top: 20px;
            padding-top: 15px;
            border-top: 1px solid #eee;
        }

        @media (max-width: 768px) {
            .manual-view-panel .panel-heading {
                padding-right: 15px;
            }

            .manual-view-panel .panel-heading .pull-right {
                position: static;
                margin-top: 10px;
            }
        }
    </style>
    <!-- HTML5 shim and Respond.js for IE8 support of HTML5 elements and media queries -->
    <!--[if lt IE 9]>
      <script src="https://oss.maxcdn.com/html5shiv/3.7.2/html5shiv.min.js?hpbankaversion={hpBankaVersion}"></script>
      <script src="https://oss.maxcdn.com/respond/1.4.2/respond.min.js?hpbankaversion={hpBankaVersion}"></script>
    <![endif]-->
    <script src="js/angular/angular.min.js?hpbankaversion={hpBankaVersion}"></script>
    <script src="js/angular/i18n/angular-locale_cs-cz.js?hpbankaversion={hpBankaVersion}"></script>
    <script src="js/jquery.min.js?hpbankaversion={hpBankaVersion}"></script>
    <script src="bootstrap/js/bootstrap.min.js?hpbankaversion={hpBankaVersion}"></script>
    <!-- Summernote JS - musí být po Bootstrap -->
    <script src="https://cdn.jsdelivr.net/npm/summernote@0.8.18/dist/summernote-bs3.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/summernote@0.8.18/dist/lang/summernote-cs-CZ.min.js"></script>
    <script type="text/javascript" src="hpBanka.js?hpbankaversion={hpBankaVersion}"></script>
    <script type="text/javascript" src="hpBankaMenu.js?hpbankaversion={hpBankaVersion}"></script>
    <script type="text/javascript" src="manualList.js?hpbankaversion={hpBankaVersion}"></script>
</head>

<body ng-app="PBanka" ng-controller="ManualListController"
    ng-init="basicInformation={basicInformationData}; rightList={rightListData}; manual={manualData};">
    <div>
        <div class="container">
            <img src="images/logo_hpbanka.png" alt="Potravinová banka" id="logoHpBanka">
            <img src="images/logo/{logoName}" alt="Potravinová banka" id="logoPBanka">
            <div id="namePBanka">{pbName}</div>
        </div>
    </div>
    <hp-banka-admin-menu ng-init="activeMainMenu='manualList';"></hp-banka-admin-menu>
    <div class="container">
        <br>
        
        <!-- Zprávy o úspěchu -->
        <!-- IF {showSaveMessage} -->
        <div class="alert alert-success alert-dismissible" role="alert">
            <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                <span aria-hidden="true">&times;</span>
            </button>
            <strong>Úspěch!</strong> Manuál byl úspěšně uložen.
        </div>
        <!-- ENDIF -->
        
        <!-- IF {showDeleteMessage} -->
        <div class="alert alert-success alert-dismissible" role="alert">
            <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                <span aria-hidden="true">&times;</span>
            </button>
            <strong>Úspěch!</strong> Manuál byl úspěšně smazán.
        </div>
        <!-- ENDIF -->
        
        <!-- Chybové zprávy -->
        <div ng-show="showErrorMessage" class="alert alert-danger alert-dismissible" role="alert">
            <button type="button" class="close" ng-click="hideErrorMessage()" aria-label="Close">
                <span aria-hidden="true">&times;</span>
            </button>
            <strong>Chyba!</strong> {{ errorMessage }}
        </div>

        <div class="page-header">
            <h1>Manuály</h1>
            <p class="lead">Zde můžete spravovat obsah manuálů pro vaši potravinovou banku.</p>
        </div>

        <div ng-show="canRead()">
            <!-- Zobrazovací režim -->
            <div ng-show="!editMode">
                <div ng-show="hasContent()">
                    <div class="panel panel-default manual-view-panel">
                        <div class="panel-heading">
                            <h3 class="panel-title" style="padding: 8px;">{{ manual.title || 'Bez názvu' }}</h3>
                            <div class="pull-right" ng-show="canEdit()">
                                <button type="button"
                                        class="btn btn-sm btn-primary"
                                        ng-click="enterEditMode()">
                                    <span class="glyphicon glyphicon-edit"></span>&nbsp;&nbsp;Přepnout na editační režim
                                </button>
                            </div>
                            <div class="clearfix"></div>
                        </div>
                        <div class="panel-body">
                            <div class="manual-content" ng-bind-html="manual.content | trustAsHtml"></div>
                        </div>
                    </div>
                </div>

                <div ng-show="!hasContent()" class="alert alert-info">
                    <h4>Zatím nemáte vytvořený žádný manuál</h4>
                    <p>Klikněte na tlačítko níže a vytvořte první manuál pro vaši potravinovou banku.</p>
                    <br />
                    <button type="button"
                            class="btn btn-primary"
                            ng-show="canEdit()"
                            ng-click="enterEditMode()">
                        <span class="glyphicon glyphicon-plus"></span>&nbsp;&nbsp;Vytvořit první manuál
                    </button>
                </div>
            </div>

            <!-- Editační režim -->
            <div ng-show="editMode">
                <form name="manualForm" novalidate>
                    <div class="form-group">
                        <label for="manualTitle" class="control-label">Název manuálu <span class="glyphicon glyphicon-star"></span></label>
                        <input type="text"
                               class="form-control"
                               id="manualTitle"
                               name="title"
                               ng-model="manual.title"
                               placeholder="Zadejte název manuálu"
                               required>
                    </div>

                    <div class="form-group">
                        <label for="manualContent" class="control-label">Obsah manuálu <span class="glyphicon glyphicon-star"></span></label>
                        <div id="summernote"></div>
                        <p class="help-block">Použijte editor pro formátování textu. Můžete přidávat nadpisy, seznamy, odkazy a další formátování.</p>
                    </div>

                    <div class="form-group edit-mode-buttons">
                        <button type="button"
                                class="btn btn-success"
                                ng-click="saveManual()"
                                ng-disabled="!manual.title || !manual.content">
                            <span class="glyphicon glyphicon-floppy-disk"></span>&nbsp;&nbsp;Uložit manuál
                        </button>

                        <button type="button"
                                class="btn btn-default"
                                ng-click="cancelEdit()"
                                style="margin-left: 10px;">
                            <span class="glyphicon glyphicon-remove"></span>&nbsp;&nbsp;Zrušit
                        </button>

                        <button type="button"
                                class="btn btn-danger pull-right"
                                ng-click="deleteManual()"
                                ng-show="hasContent()">
                            <span class="glyphicon glyphicon-trash"></span>&nbsp;&nbsp;Smazat manuál
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <div ng-show="!canRead()" class="alert alert-warning">
            <strong>Upozornění!</strong> Nemáte oprávnění k zobrazení této stránky.
        </div>
    </div>

    <!-- Placed at the end of the document so the pages load faster -->
    <script src="bootstrap/js/bootstrap.min.js?hpbankaversion={hpBankaVersion}"></script>
</body>

</html>
