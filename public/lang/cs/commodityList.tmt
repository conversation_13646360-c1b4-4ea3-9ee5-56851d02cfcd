<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="description" content="">
    <meta name="author" content="">
    <link rel="icon" href="../../favicon.ico">
    <title>Potravinová banka</title>
    <!-- Bootstrap core CSS -->
    <link href="bootstrap/css/bootstrap.min.css?hpbankaversion={hpBankaVersion}" rel="stylesheet">
    <!-- Custom styles for this template -->
    <link href="style/hpbanka.css?hpbankaversion={hpBankaVersion}" rel="stylesheet"> 
    <!-- HTML5 shim and Respond.js for IE8 support of HTML5 elements and media queries -->
    <!--[if lt IE 9]>
      <script src="https://oss.maxcdn.com/html5shiv/3.7.2/html5shiv.min.js?hpbankaversion={hpBankaVersion}"></script>
      <script src="https://oss.maxcdn.com/respond/1.4.2/respond.min.js?hpbankaversion={hpBankaVersion}"></script>
    <![endif]-->
    <script src="js/angular/angular.min.js?hpbankaversion={hpBankaVersion}"></script>
    <script src="js/angular/i18n/angular-locale_cs-cz.js?hpbankaversion={hpBankaVersion}"></script>
    <script src="js/jquery.min.js?hpbankaversion={hpBankaVersion}"></script>
    <script src="bootstrap/js/bootstrap.min.js?hpbankaversion={hpBankaVersion}"></script> 
    <script type="text/javascript" src="hpBankaMenu.js?hpbankaversion={hpBankaVersion}"></script>    
    <script type="text/javascript" src="commodityList.js?hpbankaversion={hpBankaVersion}"></script>
  </head>

  <body ng-app="PBanka" ng-controller="CommodityListController" ng-init="basicInformation={basicInformationData}; rightList={rightListData}; commodityList={commodityListData};">
  <div >
    <div class="container">
      <img src="images/logo_hpbanka.png" alt="Potravinová banka" id="logoHpBanka">
      <img src="images/logo/{logoName}" alt="Potravinová banka" id="logoPBanka">
      <div id="namePBanka">{pbName}</div>
    </div>
  </div>
  <hp-banka-admin-menu ng-init="activeMainMenu='formList'; activeSubMenu='commodityList'; showSubmenuSettings=true;"></hp-banka-admin-menu>
  <div class="container">
  <br>
  <table ng-show="!showEdit" class="table">
    <thead>
      <tr>
        <th width="10%"><a class="sord" ng-click="order('code')">Kód <span ng-show="sordBy === 'code'" class="glyphicon" ng-class="{true:' glyphicon-menu-up', false:' glyphicon-menu-down'}[reverse]" ></span></a></th>
        <th><a class="sord" ng-click="order('name')">Název <span ng-show="sordBy === 'name'" class="glyphicon" ng-class="{true:' glyphicon-menu-up', false:' glyphicon-menu-down'}[reverse]" ></span></a></th>
        <th class="text-right">Cena za kg v {currencySymbol}</th>
        <th>Akce</th>
      </tr> 
      <tr>
        <td colspan="3"></td>
        <td>
          <a class="btn btn-success" href="listOfCommodities2PDF.php" title="čárové kódy" target="_blank"><span class="glyphicon glyphicon-barcode"></span>&nbsp;&nbsp;čárové kódy</a>
          <button ng-if="rightList.commodityList.fullAccess" type="submit" name="save" class="btn btn-warning" ng-click="addCommodity();"><span class="glyphicon glyphicon-plus"></span>&nbsp;&nbsp;přidat</button>
        </td>
      </tr>
    </thead> 
    <tbody> 
    <tr ng-repeat="commodity in commodityList | orderBy:sordBy:reverse">
	    <th scope="row">{{ commodity.code }}</th>
	    <td>{{ commodity.name }}</td>
	    <td class="text-right">{{ commodity.pricePerUnit | number:2 }}</td>
      <td nowrap>
        <a ng-if="rightList.commodityList.fullAccess" class="btn btn-success" href="#" title="upravit" ng-click="editCommodity(commodity.commodityId);" ><span class="glyphicon glyphicon-edit"></span>&nbsp;&nbsp;upravit</a>
        <a ng-if="!commodity.isUsedCommodityId && rightList.commodityList.fullAccess" class="btn btn-success" href="commodityList.php?commodityId={{ commodity.commodityId }}&delete=1" title="smazat"><span class="glyphicon glyphicon-trash"></span>&nbsp;&nbsp;smazat</a>
      </td>
    </tr> 
    </tbody>
    <tr ng-if="rightList.commodityList.fullAccess">
      <td colspan="3"></td>
      <td><button type="submit" name="save" class="btn btn-warning" ng-click="addCommodity();"><span class="glyphicon glyphicon-plus"></span>&nbsp;&nbsp;přidat</button></td>
    </tr>
  </table>
  <form ng-show="showEdit" method="post" action="commodityList.php" name="commodityListForm" novalidate class="form-horizontal">
  <div class="form-group">
    <label for="inputCode" class="col-sm-2 control-label">Kód&nbsp;<span class="glyphicon glyphicon-star"></span></label>
    <div class="col-sm-8" ng-class="{true:'has-error', false:''}[commodityListForm.code.$error.required && !commodityListForm.code.$pristine]">
      <input type="text" name="code" class="form-control" id="inputCode" ng-model="commodity.code" required>
      <label class="control-label" for="inputCode" ng-show="commodityListForm.code.$error.required && !commodityListForm.code.$pristine">Tento údaj je povinný.</label>
    </div>
  </div>
  <div class="form-group">
    <label for="inputName" class="col-sm-2 control-label">Název&nbsp;<span class="glyphicon glyphicon-star"></span></label>
    <div class="col-sm-8" ng-class="{true:'has-error', false:''}[commodityListForm.name.$error.required && !commodityListForm.name.$pristine]">
      <input type="text" name="name" class="form-control" id="inputName" ng-model="commodity.name" placeholder="Název, např. mouka" required>
      <label class="control-label" for="inputName" ng-show="commodityListForm.name.$error.required && !commodityListForm.name.$pristine">Tento údaj je povinný.</label>
    </div>
  </div>
  <div class="form-group">
    <label for="inputPricePerUnit" class="col-sm-2 control-label">Cena za jednotku&nbsp;<span class="glyphicon glyphicon-star"></span></label>
    <div class="col-sm-8" ng-class="{true:'has-error', false:''}[commodityListForm.pricePerUnit.$error.required && !commodityListForm.pricePerUnit.$pristine]">
      <input type="text" name="pricePerUnit" class="form-control" id="inputPricePerUnit" placeholder="např. 19.50" ng-model="commodity.pricePerUnit" required>
      <label class="control-label" for="inputPricePerUnit" ng-show="commodityListForm.pricePerUnit.$error.required && !commodityListForm.pricePerUnit.$pristine">Tento údaj je povinný.</label>
    </div>
  </div>
  <div class="col-sm-10 text-right"><span class="glyphicon glyphicon-star"></span> Povinné údaje.</div>  
  <div class="form-group">
    <div class="col-sm-offset-2 col-sm-8">
      <button type="submit" name="save" class="btn btn-success" title="upravit" ng-disabled="!canSave();"><span class="glyphicon glyphicon-save"></span>&nbsp;&nbsp;uložit</button>
      <a class="btn btn-success" title="zpět na seznam" ng-click="showEdit=false;"><span class="glyphicon glyphicon-hand-left"></span>&nbsp;&nbsp;zpět na seznam</a>
    </div>
  </div>
  <input type="hidden" name="commodityId" ng-model="commodity.commodityId" value="{{ commodity.commodityId }}"/>
  </form>
  <hr>
  <footer>
    <p>{copyright}</p>
  </footer>
  </div> <!-- /container -->
  </body>
</html>