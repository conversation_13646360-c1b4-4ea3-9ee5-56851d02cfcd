<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="description" content="">
    <meta name="author" content="">
    <link rel="icon" href="../../favicon.ico">
    <title>Potravinová banka</title>
    <!-- Bootstrap core CSS -->
    <link href="bootstrap/css/bootstrap.min.css?hpbankaversion={hpBankaVersion}" rel="stylesheet">
    <link href="bootstrap-additions/bootstrap-additions.min.css?hpbankaversion={hpBankaVersion}" rel="stylesheet">
    <!-- Custom styles for this template -->
    <link href="style/hpbanka.css?hpbankaversion={hpBankaVersion}" rel="stylesheet">
    <!-- HTML5 shim and Respond.js for IE8 support of HTML5 elements and media queries -->
    <!--[if lt IE 9]>
      <script src="https://oss.maxcdn.com/html5shiv/3.7.2/html5shiv.min.js?hpbankaversion={hpBankaVersion}"></script>
      <script src="https://oss.maxcdn.com/respond/1.4.2/respond.min.js?hpbankaversion={hpBankaVersion}"></script>
    <![endif]-->
    <script src="js/angular/angular.min.js?hpbankaversion={hpBankaVersion}"></script>
    <script src="js/angular/i18n/angular-locale_cs-cz.js?hpbankaversion={hpBankaVersion}"></script>
    <script src="js/jquery.min.js?hpbankaversion={hpBankaVersion}"></script>
    <script src="bootstrap/js/bootstrap.min.js?hpbankaversion={hpBankaVersion}"></script>
    <script src="js/angular-strap/angular-strap.min.js?hpbankaversion={hpBankaVersion}"></script>
    <script src="js/angular-strap/angular-strap.tpl.min.js?hpbankaversion={hpBankaVersion}"></script>
    <script type="text/javascript" src="hpBanka.js?hpbankaversion={hpBankaVersion}"></script>
    <script type="text/javascript" src="hpBankaMenu.js?hpbankaversion={hpBankaVersion}"></script>
    <script type="text/javascript" src="stockSelectList.js?hpbankaversion={hpBankaVersion}"></script>
    <script type="text/javascript" src="receiptToStockList.js?hpbankaversion={hpBankaVersion}"></script>
    <script>
        $(function () {
            $('[data-toggle="tooltip"]').tooltip({container: 'body'})
        })
    </script>
</head>

<body ng-app="PBanka" ng-controller="ReceiptToStockListController"
    ng-init="basicInformation={basicInformationData}; rightList={rightListData}; receivedFormList={receivedFormListData}; showFirstReceivedForm({filledFormId})">
    <div>
        <div class="container">
            <img src="images/logo_hpbanka.png" alt="Potravinová banka" id="logoHpBanka">
            <img src="images/logo/{logoName}" alt="Potravinová banka" id="logoPBanka">
            <div id="namePBanka">{pbName}</div>
        </div>
    </div>
    <hp-banka-admin-menu ng-init="activeMainMenu='stock'; activeSubMenu='receiptToStockList'; showSubmenuStock=true;">
    </hp-banka-admin-menu>
    <div class="container"
        ng-init="stockList={stockListData}; initCurrentStockId={currentStockId};currentStock=[];summaryItem={summaryItem};">
        <h3 ng-show="initCurrentStockId==null">Nemáte nastavený sklad, kontakujte administrátora.</h3>
        <stock-select-list ng-show="!showEdit && initCurrentStockId!=null" stock-list="stockList"
            current-stock="currentStock" init-current-stock-id="initCurrentStockId" summary-item="summaryItem"
            change-stock-fn="changeStock(newCurrentStock)"></stock-select-list>
        <br />
        <div ng-hide="showEdit" class="well well-sm"><strong>Množství celkem: {{ totalAmount | number:3 }} kg. Hodnota
                daru uvedená dodavateli celkem: {{ totalGiftValue | number:2 }} {currencySymbol}.</strong></div>
        <form ng-show="!showEdit && initCurrentStockId!=null" method="post" name="formReceived"
            action="receiptToStockList.php" novalidate>
            <table class="table" ng-init="filterList={filterListData};">
                <thead>
                    <tr>
                        <th colspan="2">Číslo příjemky / číslo výdejky</th>
                        <th>Dodavatel</th>
                        <th>Název formuláře</th>
                        <th>Naskladněno dne</th>
                        <th class="text-right">Množství v kg</th>
                        <th class="text-right">Hodnota daru v {currencySymbol} uvedná dodavatelem</th>
                        <th></th>
                        <th>Akce</th>
                    </tr>
                    <tr>
                        <td colspan="2"><input type="text" size="5" name="filterByStockFilledFormId"
                                ng-model="filterList.stockFilledFormId"></td>
                        <td><input type="text" name="filterBySupplier" ng-model="filterList.supplier"></td>
                        <td><input type="text" size="5" name="filterByActionName" ng-model="filterList.actionName"></td>
                        <td nowrap="nowrap"><input type="text" size="10" name="filterByDateFrom"
                                ng-model="filterList.dateFrom" bs-datepicker>&nbsp;/&nbsp;<input type="text" size="10"
                                name="filterByDateTo" ng-model="filterList.dateTo" bs-datepicker></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td nowrap="nowrap">
                            <button name="filter" class="btn btn-warning" title="vybrat"><span
                                    class="glyphicon glyphicon-filter"></span>&nbsp;&nbsp;vybrat</button>
                            <a href="receiptToStockList.php" class="btn btn-warning" title="zrušit výběr"><span
                                    class="glyphicon glyphicon-erase"></span>&nbsp;&nbsp;výchozí období</a>
                        </td>
                    </tr>
                </thead>
                <tbody>
                    <tr ng-repeat="receivedForm in receivedFormList" ng-init="countTotalItems();">
                        <td><span ng-show="receivedForm.isNote" data-toggle="tooltip" data-placement="top"
                                title="{{ receivedForm.note }}" class="glyphicon glyphicon-comment"></span></td>
                        <td scope="row" nowrap="nowrap">{{ receivedForm.stockFilledFormId }} / {{
                            receivedForm.stockStockReleaseNoteId }}</td>
                        <td ng-click="filterList.supplier=receivedForm.name" role="button"><span
                                class="glyphicon glyphicon-copy"></span> {{ receivedForm.name }}</td>
                        <td>{{ receivedForm.actionName }}</td>
                        <td>{{ receivedForm.consumptionDate | date: fullDate }}</td>
                        <td class="text-right">{{ receivedForm.totalAmount | number:3 }}</td>
                        <td class="text-right">{{ receivedForm.giftValue | number:2 }}</td>
                        <td></td>
                        <td nowrap="nowrap">
                            <a class="btn btn-success" target="_blank"
                                href="receipt2PDF.php?filledFormId={{ receivedForm.filledFormId }}" title="tisk"
                                role="button"><span class="glyphicon glyphicon-print"></span>&nbsp;&nbsp;tisk
                                příjemky</a>
                            <a ng-if="rightList.receiptToStockList.fullAccess && receivedForm.donationAgreementId==null"
                                class="btn btn-success" href="#" title="upravit"
                                ng-disabled="receivedForm.isCustomerArchived"
                                ng-click="editReceivedForm(receivedForm.isCustomerArchived, receivedForm.filledFormId);"><span
                                    class="glyphicon glyphicon-edit"></span>&nbsp;&nbsp;upravit</a>
                            <a ng-if="!rightList.receiptToStockList.fullAccess" class="btn btn-success" href="#"
                                title="zobrazit"
                                ng-click="showReceivedForm(receivedForm.isCustomerArchived, receivedForm.filledFormId);"><span
                                    class="glyphicon glyphicon-eye-open"></span>&nbsp;&nbsp;zobrazit</a>
                            <a ng-if="rightList.receiptToStockList.fullAccess && receivedForm.isDirectConsumption"
                                ng-disabled="receivedForm.isCustomerArchived"
                                ng-href="{{ receivedForm.isCustomerArchived ? '' : 'stockReleaseNoteList.php?stockReleaseNoteId=' + receivedForm.stockReleaseNoteId }}"
                                class="btn btn-success" title="výdejka"><span
                                    class="glyphicon glyphicon-edit"></span>&nbsp;&nbsp;výdejka</a>
                        </td>
                    </tr>
                    <tr>
                        <td colspan="7"></td>
                        <td nowrap="nowrap">
                            <input type="hidden" name="receivedFormList" value="{{ receivedFormList }}" />
                            <input type="hidden" name="filterList" value="{{ filterList }}" />
                        </td>
                    </tr>
                </tbody>
            </table>
        </form>
        <form ng-show="showEdit && initCurrentStockId!=null" method="post" name="formFillerForm" novalidate
            class="form-horizontal">
            <h2>Akce: {{ formFillerList.actionName }}</h2>
            <table class="form-filler-caption">
                <tr>
                    <td><label for="dateOfCollection">Naskladněno dne&nbsp;<span
                                class="glyphicon glyphicon-star"></span></label>&nbsp;&nbsp;&nbsp;</td>
                    <td
                        ng-class="{true:'has-error', false:''}[(formFillerForm.consumptionDate.$error.required || formFillerForm.consumptionDate.$error.date) && !formFillerForm.consumptionDate.$pristine]">
                        <input ng-disabled="!canEdit();" name="consumptionDate" type="text" class="form-control"
                            id="dateOfCollection" ng-model="formFillerList.consumptionDate" bs-datepicker required>
                        <label class="control-label" for=""
                            ng-show="formFillerForm.consumptionDate.$error.required && !formFillerForm.consumptionDate.$pristine">Tento
                            údaj je povinný.</label>
                        <label class="control-label" for=""
                            ng-show="formFillerForm.consumptionDate.$error.date && !formFillerForm.consumptionDate.$error.min && !formFillerForm.consumptionDate.$pristine">Zadejte
                            datum ve formátu DD.MM.YYYY.</label>
                    </td>
                    <td class="col-sm-1"></td>
                    <td colspan="2"><label>Název skladu: {{ formFillerList.nameStock}}</label></td>
                </tr>
                <tr>
                    <td><label for="supplier">Dodavatel&nbsp;<span
                                class="glyphicon glyphicon-star"></span></label>&nbsp;&nbsp;&nbsp;</td>
                    <td>
                        <select ng-disabled="!canEdit();" class="form-control" name="supplierId"
                            ng-model="current.supplier"
                            ng-options="supplier.supplierId as supplier.name for supplier in supplierList"></select>
                    </td>
                    <td class="col-sm-1"></td>
                    <td><label for="giftValue">Hodnota daru uvedená dodavatelem v {currencySymbol}&nbsp;<span
                                class="glyphicon glyphicon-star"></span></label>&nbsp;&nbsp;&nbsp;</td>
                    <td
                        ng-class="{true:'has-error', false:''}[(formFillerForm.giftValue.$error.required || formFillerForm.giftValue.$error.pattern) && !formFillerForm.giftValue.$pristine]">
                        <input ng-disabled="!canEdit();" name="giftValue" type="text" class="form-control"
                            id="giftValue" ng-model="formFillerList.giftValue" ng-pattern="regNumber" required>
                        <label class="control-label" for=""
                            ng-show="formFillerForm.giftValue.$error.required && !formFillerForm.giftValue.$pristine">Tento
                            údaj je povinný.</label>
                        <label class="control-label" for=""
                            ng-show="formFillerForm.giftValue.$error.pattern && !formFillerForm.giftValue.$pristine">Prosím,
                            zadejte kladné číslo.</label>
                    </td>
                </tr>
                <tr>
                    <td><label for="note">Poznámka</label></td>
                    <td><textarea class="form-control" rows="2" ng-model="formFillerList.note"
                            maxlength="30"></textarea></td>
                </tr>
            </table>
            <h4>Uveďte množství odebraných komodit:</h4>
            <div class="form-group" ng-repeat="commodity in commodityList">
                <label class="col-sm-5 control-label text-left" for="name{{ $index+1 }}">{{ commodity.code }} &nbsp; {{
                    commodity.name }}</label>
                <div ng-class="{true:'has-error', false:''}[formFillerForm.name{{ $index+1 }}.$error.pattern && !formFillerForm.name{{ $index+1 }}.$pristine]"
                    class="input-group col-sm-2">
                    <input ng-disabled="!canEdit();" type="text" class="form-control" name="name{{ $index+1 }}"
                        id="name{{ $index+1 }}" placeholder="např. 1.005" ng-change="countAmount();"
                        ng-pattern="regNumber" ng-model="commodity.amount">
                    <div class="input-group-addon">{{ commodity.unit }}</div>
                </div>
                <strong class="col-sm-7 control-label text-right text-danger"
                    ng-show="formFillerForm.name{{ $index+1 }}.$error.pattern">Prosím, zadejte kladné číslo.</strong>
            </div>
            <div class="col-sm-7 text-right"><span class="glyphicon glyphicon-star"></span> Povinné údaje.</div>
            <div class="col-sm-5 text-right">&nbsp;</div>
            <h5>Hmotnost zboží celkem: <b>{{ formFillerList.formTotalAmount | number:3 }} kg</b>.</h5>

            <div class="form-group">
                <div class="col-sm-5">
                    <button ng-if="rightList.receiptToStockList.fullAccess" ng-click="saveReceiptToStock();" name="save"
                        class="btn btn-success" title="odeslat" ng-disabled="!canSave();"><span
                            class="glyphicon glyphicon-save"></span>&nbsp;&nbsp;Uložit</button>
                    <a class="btn btn-success" title="zpět na seznam" ng-click="showEdit=false;"><span
                            class="glyphicon glyphicon-hand-left"></span>&nbsp;&nbsp;zpět na seznam</a>
                </div>
            </div>

            <input type="hidden" name="formFillerList" value="{{ formFillerList }}" />
            <input type="hidden" name="commodityList" value="{{ commodityList  }}" />
        </form>
        <hr>
        <footer>
            <p>{{ basicInformation.copyright }}</p>
        </footer>
    </div>
</body>

</html>