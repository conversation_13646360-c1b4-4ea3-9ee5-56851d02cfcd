<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="description" content="">
    <meta name="author" content="">
    <link rel="icon" href="../../favicon.ico">
    <title>Potravinová banka</title>
    <!-- Bootstrap core CSS -->
    <link href="bootstrap/css/bootstrap.min.css?hpbankaversion={hpBankaVersion}" rel="stylesheet">
    <link href="bootstrap-additions/bootstrap-additions.min.css?hpbankaversion={hpBankaVersion}" rel="stylesheet">
    <link href="bootstrap-select/css/bootstrap-select.min.css?hpbankaversion={hpBankaVersion}" rel="stylesheet">
    <!-- Custom styles for this template -->
    <link href="style/hpbanka.css?hpbankaversion={hpBankaVersion}" rel="stylesheet">
    <!-- HTML5 shim and Respond.js for IE8 support of HTML5 elements and media queries -->
    <!--[if lt IE 9]>
      <script src="https://oss.maxcdn.com/html5shiv/3.7.2/html5shiv.min.js?hpbankaversion={hpBankaVersion}"></script>
      <script src="https://oss.maxcdn.com/respond/1.4.2/respond.min.js?hpbankaversion={hpBankaVersion}"></script>
    <![endif]-->
    <script src="js/angular/angular.min.js?hpbankaversion={hpBankaVersion}"></script>
    <script src="js/angular/angular-animate.min.js?hpbankaversion={hpBankaVersion}"></script>
    <script src="js/angular/i18n/angular-locale_cs-cz.js?hpbankaversion={hpBankaVersion}"></script>
    <script src="js/jquery.min.js?hpbankaversion={hpBankaVersion}"></script>
    <script src="bootstrap/js/bootstrap.min.js?hpbankaversion={hpBankaVersion}"></script>
    <script src="bootstrap-select/js/bootstrap-select.min.js?hpbankaversion=={hpBankaVersion}"></script>
    <script src="bootstrap-select/js/i18n/defaults-cs_CZ.min.js?hpbankaversion=={hpBankaVersion}"></script>
    <script src="js/angular-strap/angular-strap.min.js?hpbankaversion={hpBankaVersion}"></script>
    <script src="js/angular-strap/angular-strap.tpl.min.js?hpbankaversion={hpBankaVersion}"></script>
    <script type="text/javascript" src="hpBanka.js?hpbankaversion={hpBankaVersion}"></script>
    <script type="text/javascript" src="hpBankaMenu.js?hpbankaversion={hpBankaVersion}"></script>
    <script type="text/javascript" src="js/angular/selectPicker.js?hpbankaversion={hpBankaVersion}"></script>
    <script type="text/javascript" src="commodityAmount.js?hpbankaversion={hpBankaVersion}"></script>
    <script type="text/javascript" src="numberLimits.js?hpbankaversion={hpBankaVersion}"></script>
    <script type="text/javascript" src="outputFromStock.js?hpbankaversion={hpBankaVersion}"></script>
</head>

<body ng-app="PBanka" ng-controller="OutputFromStockController"
    ng-init="basicInformation={basicInformationData}; rightList={rightListData}; outputFromStock={outputFromStockData};">
    <div>
        <div class="container">
            <img src="images/logo_hpbanka.png" alt="Potravinová banka" id="logoHpBanka">
            <img src="images/logo/{logoName}" alt="Potravinová banka" id="logoPBanka">
            <div id="namePBanka">{pbName}</div>
        </div>
    </div>
    <hp-banka-admin-menu ng-init="activeMainMenu='stock'; activeSubMenu='outputFromStockList'; showSubmenuStock=true;">
    </hp-banka-admin-menu>
    <div class="container">
        <div class="page-header">
            <h2>Akce: {{ outputFromStock.actionName }}</h2>
        </div>
        <form method="post" name="outputFromStockForm" class="form-horizontal" action="outputFromStock.php" novalidate>
            <table class="form-filler-caption">
                <tr>
                    <td colspan="2"><label>Název skladu: {{ outputFromStock.nameStock}}</label></td>
                </tr>
                <tr>
                    <td><label for="issuedAt">Vyskladněno dne&nbsp;<span
                                class="glyphicon glyphicon-star"></span></label>&nbsp;&nbsp;&nbsp;</td>
                    <td
                        ng-class="{true:'has-error', false:''}[(outputFromStockForm.issuedAt.$error.required || outputFromStockForm.issuedAt.$error.date) && !outputFromStockForm.issuedAt.$pristine]">
                        <input type="text" class="form-control" id="issuedAt" ng-model="outputFromStock.issuedAt"
                            bs-datepicker required>
                        <label class="control-label" for=""
                            ng-show="outputFromStockForm.issuedAt.$error.required && !outputFromStockForm.issuedAt.$pristine">Tento
                            údaj je povinný.</label>
                        <label class="control-label" for=""
                            ng-show="outputFromStockForm.issuedAt.$error.date && !outputFromStockForm.issuedAt.$pristine">Zadejte
                            datum ve formátu DD.MM.YYYY.</label>
                    </td>
                    <td class="col-sm-1"></td>
                    <td><label for="customer">Odběratel&nbsp;<span
                                class="glyphicon glyphicon-star"></span></label>&nbsp;&nbsp;&nbsp;</td>
                    <td ng-init="customerList={customerListData}">
                        <select class="form-control selectpicker" data-live-search="true" ng-model="currentCustomer"
                            ng-options="customer.name for customer in customerList"
                            ng-init="inicializeSelectedCustomer({customerId});" required select-picker></select>
                        <input type="hidden" name="customerId" value="{{ currentCustomer.customerId }}">
                    </td>
                </tr>
                <tr>
                    <td><label for="note">Poznámka</label></td>
                    <td><textarea class="form-control" rows="2" ng-model="outputFromStock.note"
                            maxlength="30"></textarea>
                    </td>
                </tr>
            </table>
            <br>
            <h4 ng-init="commodityList={commodityListData};">Uveďte množství vydaných komodit:&nbsp;<span
                    class="glyphicon glyphicon-star"></span></h4>
            <div class="form-group" ng-repeat="commodity in commodityList">
                <commodity-amount commodity="commodity" form="outputFromStockForm" id="$index + 1"
                    count-amount-fn="countAmount()"></commodity-amount>
            </div>
            <div ng-init="importedCommoditiesNotOnForm={importedCommoditiesNotOnForm}"
                ng-show="importedCommoditiesNotOnForm.length>0" class="alert alert-danger" role="alert">
                Nebylo možné zpracovat tyto položky:
                <ul>
                    <li ng-repeat="commodity in importedCommoditiesNotOnForm">{{ commodity.code }} {{ commodity.name }}
                        ({{ commodity.amount | number:3 }} kg)</li>
                </ul>
            </div>
            <div class="col-sm-7 text-right"><span class="glyphicon glyphicon-star"></span> Povinné údaje.</div>
            <div class="col-sm-5 text-right">&nbsp;</div>
            <h5>Hmotnost vydaného zboží celkem: <b>{{ outputFromStock.totalAmount | number:3 }} kg</b>.</h5>
            <div class="form-group">
                <div class="col-sm-5">
                    <input type="hidden" name="commodityList" value="{{ commodityList }}">
                    <input type="hidden" name="issuedAt" value="{{ outputFromStock.issuedAt }}">
                    <input type="hidden" name="import" value="{import}">
                    <input type="hidden" name="outputFromStock" value="{{ outputFromStock }}">
                    <input type="hidden" name="stockId" value="{currentStockId}">
                    <button name="save" class="btn btn-success" title="uložit" ng-disabled="!canSave();"><span
                            class="glyphicon glyphicon-save"></span>&nbsp;&nbsp;Uložit</button>
                    <a class="btn btn-success"
                        href="outputFromStockList.php?&customerId={{ currentCustomer.customerId }}&issuedAt={{ outputFromStock.issuedAt }}&import={import}"
                        title="zpět na seznam"><span class="glyphicon glyphicon-hand-left"></span>&nbsp;&nbsp;zpět na
                        seznam</a>
                </div>
            </div>
        </form>
        <hr>
        <footer>
            <p>{copyright}</p>
        </footer>
    </div> <!-- /container -->
</body>

</html>