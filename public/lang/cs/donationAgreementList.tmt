<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="description" content="">
    <meta name="author" content="">
    <link rel="icon" href="../../favicon.ico">
    <title>Potravinová banka</title>
    <!-- Bootstrap core CSS -->
    <link href="bootstrap/css/bootstrap.min.css?hpbankaversion={hpBankaVersion}" rel="stylesheet">
    <link href="bootstrap-additions/bootstrap-additions.min.css?hpbankaversion={hpBankaVersion}" rel="stylesheet">
    <!-- Custom styles for this template -->
    <link href="style/hpbanka.css?hpbankaversion={hpBankaVersion}" rel="stylesheet">
    <!-- HTML5 shim and Respond.js for IE8 support of HTML5 elements and media queries -->
    <!--[if lt IE 9]>
      <script src="https://oss.maxcdn.com/html5shiv/3.7.2/html5shiv.min.js?hpbankaversion={hpBankaVersion}"></script>
      <script src="https://oss.maxcdn.com/respond/1.4.2/respond.min.js?hpbankaversion={hpBankaVersion}"></script>
    <![endif]-->
    <script src="js/angular/angular.min.js?hpbankaversion={hpBankaVersion}"></script>
    <script src="js/angular/i18n/angular-locale_cs-cz.js?hpbankaversion={hpBankaVersion}"></script>
    <script src="js/jquery.min.js?hpbankaversion={hpBankaVersion}"></script>
    <script src="bootstrap/js/bootstrap.min.js?hpbankaversion={hpBankaVersion}"></script>
    <script src="js/angular-strap/angular-strap.min.js?hpbankaversion={hpBankaVersion}"></script>
    <script src="js/angular-strap/angular-strap.tpl.min.js?hpbankaversion={hpBankaVersion}"></script>
    <script type="text/javascript" src="hpBanka.js?hpbankaversion={hpBankaVersion}"></script>
    <script type="text/javascript" src="hpBankaMenu.js?hpbankaversion={hpBankaVersion}"></script>
    <script type="text/javascript" src="donationAgreementList.js?hpbankaversion={hpBankaVersion}"></script>
</head>

<body ng-app="PBanka" ng-controller="DonationAgreementListController"
    ng-init="basicInformation={basicInformationData}; rightList={rightListData}; donationAgreementList={donationAgreementListData};">
    <div role="logos">
        <div class="container">
            <img src="images/logo_hpbanka.png" alt="Potravinová banka" id="logoHpBanka">
            <img src="images/logo/{logoName}" alt="Potravinová banka" id="logoPBanka">
            <div id="namePBanka">{pbName}</div>
        </div>
    </div>
    <!-- IF {isUser} -->
    <hp-banka-user-menu ng-init="activeMainMenu='donationAgreementList';"></hp-banka-user-menu>
    <!-- ENDIF -->
    <!-- IFNOT {isUser} -->
    <hp-banka-admin-menu ng-init="activeMainMenu='donationAgreementList';"></hp-banka-admin-menu>
    <!-- ENDIFNOT -->
    <div class="container">
        <br>
        <form method="post" action="donationAgreementList.php" novalidate>
            <table class="table" ng-init="filterList={filterListData};">
                <thead>
                    <tr>
                        <th colspan="2">Číslo smlouvy</th>
                        <!-- IFNOT {isUser} -->
                        <th>Obdarovaný</th>
                        <!-- ENDIFNOT -->
                        <th>Období</th>
                        <th class="text-right">Hodnota daru v {currencySymbol}</th>
                        <th>Zpracována</th>
                        <th></th>
                        <th>Akce</th>
                    </tr>
                    <!-- IFNOT {isUser} -->
                    <tr>
                        <td colspan="2"><input type="text" size="2" name="filterByFoodBankDonationAgreementId"
                                ng-model="filterList.foodBankDonationAgreementId"></td>
                        <td><input type="text" name="filterByCustomerName" ng-model="filterList.customerName"></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td nowrap="nowrap">
                            <button name="filter" class="btn btn-warning" title="vybrat"><span
                                    class="glyphicon glyphicon-filter"></span>&nbsp;&nbsp;vybrat</button>
                            <a href="donationAgreementList.php" class="btn btn-warning" title="zrušit filtr"><span
                                    class="glyphicon glyphicon-erase"></span>&nbsp;&nbsp;zrušit filtr</a>
                        </td>
                    </tr>
                    <!-- ENDIFNOT -->
                </thead>
                <tbody>
                    <tr ng-repeat="donationAgreement in donationAgreementList">
                        <td width="2%"><span ng-show="donationAgreement.isNote" data-toggle="tooltip"
                                data-placement="top" title="{{ donationAgreement.note }}"
                                class="glyphicon glyphicon-comment"></span></td>

                        <th scope="row">{{ donationAgreement.foodBankDonationAgreementId }}</th>
                        <!-- IFNOT {isUser} -->
                        <td>{{ donationAgreement.customerName }}</td>
                        <!-- ENDIFNOT -->
                        <td>{{ donationAgreement.dateFrom | date: fullDate }} - {{ donationAgreement.dateTo | date:
                            fullDate }}</td>
                        <td class="text-right">{{ donationAgreement.calculatedValue | number:2 }}</td>
                        <td>
                            <!-- IFNOT {isUser} -->
                            <input type="checkbox" ng-model="donationAgreement.processed"
                                ng-click="setProcessed(donationAgreement.donationAgreementId, donationAgreement.processed)" />
                            <!-- ENDIFNOT -->
                            <!-- IF {isUser} -->
                            <span ng-class="{
                                    'glyphicon-warning-sign': !donationAgreement.processed,
                                    infoIconDanger: !donationAgreement.processed,
                                    'glyphicon-ok': donationAgreement.processed,
                                    infoIconOk: donationAgreement.processed
                                }" class="glyphicon"></span>
                            <!-- ENDIF -->
                        </td>
                        <td></td>
                        <td>
                            <a class="btn btn-success" target="_blank"
                                href="donationAgreement2PDF.php?donationAgreementId={{ donationAgreement.donationAgreementId }}"
                                title="tisk" role="button"><span
                                    class="glyphicon glyphicon-print"></span>&nbsp;&nbsp;tisk smlouvy</a>
                            <button type="button" ng-if="rightList.donationAgreementList.fullAccess" name="delete"
                                class="btn btn-success" title="smazat"
                                ng-click="showModal(donationAgreement.donationAgreementId);"><span
                                    class="glyphicon glyphicon-trash"></span>&nbsp;&nbsp;smazat</button>
                        </td>
                    </tr>
                    <tr>
                        <!-- IF {isUser} -->
                        <td colspan="6" class="text-right">
                            <!-- ENDIF -->
                            <!-- IFNOT {isUser} -->
                        <td colspan="7" class="text-right">
                            <!-- ENDIFNOT -->
                            Hromadný tisk smluv od čísla: <input type="text" size="4"
                                ng-model="filterList.donationAgreementIdFrom"> do čísla <input type="text" size="4"
                                ng-model="filterList.donationAgreementIdTo">
                        </td>
                        <td>
                            <a class="btn btn-success" target="_blank"
                                href="donationAgreement2PDF.php?allDonationAgreementPrint=1&donationAgreementIdFrom={{ filterList.donationAgreementIdFrom }}&donationAgreementIdTo={{ filterList.donationAgreementIdTo }}"
                                title="tisk" role="button"><span
                                    class="glyphicon glyphicon-print"></span>&nbsp;&nbsp;hromadný tisk smluv</a>
                        </td>
                    </tr>
                </tbody>
            </table>
        </form>
        <hr>
        <footer>
            <p>{{ basicInformation.copyright }}</p>
        </footer>
    </div>
</body>

</html>
