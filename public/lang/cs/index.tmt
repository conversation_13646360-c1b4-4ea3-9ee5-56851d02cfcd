<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="description" content="">
    <meta name="author" content="">
    <link rel="icon" href="../../favicon.ico">
    <title>Potravinová banka</title>
    <!-- Bootstrap core CSS -->
    <link href="bootstrap/css/bootstrap.min.css?hpbankaversion={hpBankaVersion}" rel="stylesheet">
    <!-- Custom styles for this template -->
    <link href="style/signin.css?hpbankaversion={hpBankaVersion}" rel="stylesheet">
    <!-- HTML5 shim and Respond.js for IE8 support of HTML5 elements and media queries -->
    <!--[if lt IE 9]>
      <script src="https://oss.maxcdn.com/html5shiv/3.7.2/html5shiv.min.js?hpbankaversion={hpBankaVersion}"></script>
      <script src="https://oss.maxcdn.com/respond/1.4.2/respond.min.js?hpbankaversion={hpBankaVersion}"></script>
    <![endif]-->
  </head>

  <body>

    <div class="container">
      <!-- IF isErrorLogin {errorLogin} --> 
      <h4 class="alert alert-danger text-center">Zadané uživatelské jméno nebo heslo je nesprávné.</h4>
      <!-- ENDIF isErrorLogin -->
      <form class="form-signin" method="post" action="index.php">
        <img src="images/logo_hpbanka.png" id="logoHpBanka" alt="hpBanka" class="img-responsive">
        <label for="inputLogin" class="sr-only">Přihlašovací jméno</label>
        <input type="text" id="inputLogin" class="form-control" name="login" placeholder="Přihlašovací jméno" required autofocus>
        <label for="inputPassword" class="sr-only">Heslo</label>
        <input type="password" id="inputPassword" class="form-control" name="password" placeholder="Heslo" required >
        <input type="hidden" id="passwordHash" name="passwordHash" value="">
        <button class="btn btn-lg btn-primary" type="submit" name="enterToApp" id="enterToApp">Přihlásit</button>
      </form>
      <footer class="text-center">
        <p>{copyright}</p>
        <p>hpbanka verze {hpBankaVersion}, verze DB {databaseVersion}</p>
      </footer>
    </div> <!-- /container -->
    <!-- Placed at the end of the document so the pages load faster -->
    <script src="js/jquery.min.js?hpbankaversion={hpBankaVersion}"></script>
    <script type="text/javascript" src="js/md5/md5.js?hpbankaversion={hpBankaVersion}"></script>
    <script type="text/javascript">  
    $(function(){
        $('#enterToApp').click(function(){
          $('#passwordHash').attr('value', hex_md5($('#inputPassword').val()));
        });
    });
    </script>
  </body>
</html>
