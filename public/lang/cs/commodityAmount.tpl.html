<label class="col-sm-5 control-label text-left" for="amount{{ id }}">{{ commodity.code }} &nbsp; {{ commodity.name }}</label>
<div class="input-group col-sm-2" ng-class="{true:'has-error', false:''}[(form.amount{{ id }}.$error.pattern || form.amount{{ id }}.$error.ngMax) && !form.amount{{ id }}.$pristine]">
  <input type="text" class="form-control" name="amount{{ id }}" id="amount{{ id }}" placeholder="max. {{ commodity.availableAmount | number: 3 }}" ng-change="count();" ng-pattern="regNumber" ng-max="{{ commodity.availableAmount }}" ng-model="commodity.amount">
<div class="input-group-addon">{{ commodity.unit }}</div>
</div>
<strong class="col-sm-7 control-label text-right text-danger" ng-show="form.amount{{ id }}.$error.pattern"><PERSON>s<PERSON><PERSON>, zade<PERSON><PERSON> klad<PERSON>.</strong>
<strong class="col-sm-7 control-label text-right text-danger" ng-show="form.amount{{ id }}.$error.ngMax">Není možné vyskladnit více než je na skladě.</strong>

