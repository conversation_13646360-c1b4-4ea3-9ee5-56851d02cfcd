<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="description" content="">
    <meta name="author" content="">
    <link rel="icon" href="../../favicon.ico">
    <title>Potravinová banka</title>
    <!-- Bootstrap core CSS -->
    <link href="bootstrap/css/bootstrap.min.css?hpbankaversion={hpBankaVersion}" rel="stylesheet">
    <link href="bootstrap-additions/bootstrap-additions.min.css?hpbankaversion={hpBankaVersion}" rel="stylesheet">
    <!-- Custom styles for this template -->
    <link href="style/hpbanka.css?hpbankaversion={hpBankaVersion}" rel="stylesheet"> 
    <!-- HTML5 shim and Respond.js for IE8 support of HTML5 elements and media queries -->
    <!--[if lt IE 9]>
      <script src="https://oss.maxcdn.com/html5shiv/3.7.2/html5shiv.min.js?hpbankaversion={hpBankaVersion}"></script>
      <script src="https://oss.maxcdn.com/respond/1.4.2/respond.min.js?hpbankaversion={hpBankaVersion}"></script>
    <![endif]-->
    <script src="js/angular/angular.min.js?hpbankaversion={hpBankaVersion}"></script>
    <script src="js/angular/i18n/angular-locale_cs-cz.js?hpbankaversion={hpBankaVersion}"></script>
    <script src="js/jquery.min.js?hpbankaversion={hpBankaVersion}"></script>
    <script src="bootstrap/js/bootstrap.min.js?hpbankaversion={hpBankaVersion}"></script>
    <script src="js/angular-strap/angular-strap.min.js?hpbankaversion={hpBankaVersion}"></script>
    <script src="js/angular-strap/angular-strap.tpl.min.js?hpbankaversion={hpBankaVersion}"></script>
    <script type="text/javascript" src="hpBanka.js?hpbankaversion={hpBankaVersion}"></script> 
    <script type="text/javascript" src="hpBankaMenu.js?hpbankaversion={hpBankaVersion}"></script>         
    <script type="text/javascript" src="sentFormList.js?hpbankaversion={hpBankaVersion}"></script>
  </head>

  <body ng-app="PBanka" ng-controller="SentFormListController" ng-init="basicInformation={basicInformationData}; rightList={rightListData}; sentFormList={sentFormListData};">
  <div>
    <div class="container">
      <img src="images/logo_hpbanka.png" alt="Potravinová banka" id="logoHpBanka">
      <img src="images/logo/{logoName}" alt="Potravinová banka" id="logoPBanka">
      <div id="namePBanka">{pbName}</div>
    </div>
  </div>
  <hp-banka-user-menu ng-init="activeMainMenu='sentFormList';"></hp-banka-user-menu>
  <div class="container">
    <br>
	  <form ng-show="!showEdit" method="post" action="sentFormList.php" novalidate>
		  <table class="table" ng-init="filterList={filterListData};">
		    <thead>
		      <tr><th>Číslo<br>výdejky</th><th>Název formuláře</th><th>Datum odběru</th><th>Dodavatel</th><th>Hodnota daru<br>v kalkulační ceně v {currencySymbol}</th><th></th><th>Akce</th></tr>
		      <tr>
		        <td><input type="text" size="2" name="filterByStockStockReleaseNoteId" ng-model="filterList.stockStockReleaseNoteId"></td>
		        <td><input type="text" size="8" name="filterByActionName" ng-model="filterList.actionName"></td>
		        <td nowrap="nowrap"><input type="text"size="10" name="filterByDateFrom" ng-model="filterList.dateFrom" bs-datepicker>&nbsp;/&nbsp;<input type="text" size="10" name="filterByDateTo" ng-model="filterList.dateTo" bs-datepicker></td>
		        <td><input type="text" size="8" name="filterBySupplierName" ng-model="filterList.supplierName"></td>
		        <td></td>
		        <td></td>
		        <td nowrap="nowrap">
		          <button name="filter" class="btn btn-warning" title="odeslat"><span class="glyphicon glyphicon-filter"></span>&nbsp;&nbsp;vybrat</button>
		          <a href="sentFormList.php" class="btn btn-warning" title="zrušit výběr"><span class="glyphicon glyphicon-erase"></span>&nbsp;&nbsp;zrušit výběr</a>
		        </td>
		      </tr> 
		    </thead> 
		    <tbody> 
		    <tr ng-repeat="sentForm in sentFormList">
		      <th scope="row">{{ sentForm.stockStockReleaseNoteId }}</th><td>{{ sentForm.actionName }}</td><td>{{ sentForm.issuedAt}}</td><td>{{ sentForm.supplierName}}</td><td class="text-right">{{ sentForm.calculatedValue | number:2 }}</td><td></td>
		      <td>
				  <a ng-if="sentForm.stockReleaseNoteId > 0" class="btn btn-success" target="_blank" href="stockReleaseNote2PDF.php?stockReleaseNoteId={{ sentForm.stockReleaseNoteId }}" title="tisk" role="button"><span class="glyphicon glyphicon-print"></span>&nbsp;&nbsp;tisk výdejky</a>
				  <a ng-if="sentForm.donationAgreementId > 0" class="btn btn-success" target="_blank" href="donationAgreement2PDF.php?donationAgreementId={{ sentForm.donationAgreementId }}" title="tisk" role="button"><span class="glyphicon glyphicon-print"></span>&nbsp;&nbsp;tisk smlouvy</a>
			  </td>
		    </tr> 
		    </tbody> 
		  </table>
	  </form>
	  <input type="hidden" name="formList" value="{{ formList }}" />
	  <hr>
	  <footer>
	    <p>{{ basicInformation.copyright }}</p>
	  </footer>
	  </div>
  
  </body>
</html>