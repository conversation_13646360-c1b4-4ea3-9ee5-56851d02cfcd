<?php
header("Content-Type: text/html; charset=utf-8");

require_once '../../../conf/commonApp.php';

doInit();
core\AuthenticationManager::verifyTheAuthenticity($_authenticationCode, $_loggedAppUserId);
$authorizationManager = new core\AuthorizationManager($_loggedAppUser);

if (
    !$authorizationManager->hasRightToPage(core\RightName::reportList) &&
    !$authorizationManager->hasRightToPage(core\RightName::companyReportList)
) {
    core\HttpUtils::redirectAndExit();
}

$rights = $authorizationManager->getRights();
$hasAccessToAllPbanks = $authorizationManager->hasAccessToAllPbanks();

$t = new ui\Template("./lang/cs");
$t->set_file("tmt", "reportSupportedPersonsByFoodBanks.tmt");

$year = $_REQUEST['year'];
$customerGroupName = '';
$reportManager = report\ReportManager::createForFoodBank($_loggedAppUser->foodBankId);

if ($authorizationManager->isCompanyReporter()) {
    $customerManager = new \party\CustomerManager();
    $customerGroupName = $customerManager->getCustomerGroupName($_loggedAppUser->customerId);
    $reportManager->setExactCustomerGroupFilter($customerGroupName);
}

$customerListWithSumNumberOfSupportedPersonAfterMonths = $reportManager->getSupportedPersonsByFoodBankOrCustomerListForTheYear($year, $hasAccessToAllPbanks);

if ($_GET['export'] == 1) {
    $since = date\DateFormatter::formatToSql(date\Date::create(1, 1, $year));
    $to = date\DateFormatter::formatToSql(date\Date::create(31, 12, $year));
    $CSVHelper = new util\CSVHelper();

    $separator = report\ReportManager::CSV_COLUMN_SEPARATOR;

    $csvContent = "Počet podpořených osob po měsících s odběrateli za rok " . $year . "\n\n";

    $monthList = $CSVHelper->getCSVMonthList("Odběratel");
    $csvContent .= $monthList;

    foreach ($customerListWithSumNumberOfSupportedPersonAfterMonths['customerList'] as $customer) {
        $csvContent .= $customer['customerName'] . $separator;

        foreach ($customer['sumNumberOfSupportedPersonListAfterMonth'] as $numberOfSupportedPerson) {
            $csvContent .= $numberOfSupportedPerson . $separator;
        }
        $csvContent .= "\n";
    }

    $csvContent .= "Celkem" . $separator;
    foreach ($customerListWithSumNumberOfSupportedPersonAfterMonths['summaryCustomerList'] as $summaryCustomer) {
        $csvContent .= $summaryCustomer . $separator;
    }

    $csvContent .= "\n";

    $csvFilename = report\ReportManager::getCsvFileName("Pocet_podporenych_osob_po_mesicich_s_odberateli_za_rok", $since, $to);
    report\ReportManager::sendCsvReport($csvContent, $csvFilename);
    exit;
}

$reportData = array(
    'year' => $year,
    'customerGroupName' => $customerGroupName,
    'isCustomerGroupNameSet' => trim($customerGroupName) === '',
    'showEmptyReport' => empty($customerListWithSumNumberOfSupportedPersonAfterMonths['customerList']) ? true : false
);

$t->set_var(array(
    'customerListWithSumNumberOfSupportedPersonAfterMonthsData' => htmlspecialchars(json_encode($customerListWithSumNumberOfSupportedPersonAfterMonths['customerList'])),
    'summaryCustomerListWithSumNumberOfSupportedPersonAfterMonthsData' => htmlspecialchars(json_encode($customerListWithSumNumberOfSupportedPersonAfterMonths['summaryCustomerList'])),
    'reportData' => htmlspecialchars(json_encode($reportData)),
    'year' => $year,
    'rightListData' => htmlspecialchars(json_encode($rights))
));

$t->set_global_var_to_template();
$t->pparse("out", "tmt");
