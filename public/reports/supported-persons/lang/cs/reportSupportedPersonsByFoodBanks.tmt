<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="description" content="">
    <meta name="author" content="">
    <link rel="icon" href="../../favicon.ico">
    <title>Potravinová banka</title>
    <!-- Bootstrap core CSS -->
    <link href="/bootstrap/css/bootstrap.min.css?hpbankaversion={hpBankaVersion}" rel="stylesheet">
    <link href="/bootstrap-additions/bootstrap-additions.min.css?hpbankaversion={hpBankaVersion}" rel="stylesheet">
    <!-- Custom styles for this template -->
    <link href="/style/hpbanka.css?hpbankaversion={hpBankaVersion}" rel="stylesheet">
    <!-- HTML5 shim and Respond.js for IE8 support of HTML5 elements and media queries -->
    <!--[if lt IE 9]>
      <script src="https://oss.maxcdn.com/html5shiv/3.7.2/html5shiv.min.js?hpbankaversion={hpBankaVersion}"></script>
      <script src="https://oss.maxcdn.com/respond/1.4.2/respond.min.js?hpbankaversion={hpBankaVersion}"></script>
    <![endif]-->
    <script src="/js/angular/angular.min.js?hpbankaversion={hpBankaVersion}"></script>
    <script src="/js/angular/i18n/angular-locale_cs-cz.js?hpbankaversion={hpBankaVersion}"></script>
    <script src="/js/angular-strap/angular-strap.min.js?hpbankaversion={hpBankaVersion}"></script>
    <script src="/js/angular-strap/angular-strap.tpl.min.js?hpbankaversion={hpBankaVersion}"></script>
    <script type="text/javascript" src="/hpBanka.js?hpbankaversion={hpBankaVersion}"></script>
    <script type="text/javascript" src="/hpBankaMenu.js?hpbankaversion={hpBankaVersion}"></script>
    <script type="text/javascript" src="reportSupportedPersonsByFoodBanks.js?hpbankaversion={hpBankaVersion}"></script>
</head>

<body ng-app="PBanka" ng-controller="SupportedPersonsByFoodBanksController"
    ng-init="basicInformation={basicInformationData}; rightList={rightListData}; report={reportData};">
    <div>
        <div class="container">
            <img src="/images/logo_hpbanka.png" alt="Potravinová banka" id="logoHpBanka">
            <img src="/images/logo/{logoName}" alt="Potravinová banka" id="logoPBanka">
            <div id="namePBanka">{pbName}</div>
        </div>
    </div>
    <hp-banka-admin-menu ng-init="activeMainMenu='reportList';"></hp-banka-admin-menu>
    <div class="container">
        <div class="page-header">
            <h4>Počet podpořených osob po měsících za rok {{ report.year }}
                <span *ngIf="report.isCustomerGroupNameSet">v organizaci {{ report.customerGroupName }}</span>
            </h4>
        </div>

        <div class="table-responsive" ng-show="!report.showEmptyReport"
            ng-init="customerListWithSumNumberOfSupportedPersonAfterMonths={customerListWithSumNumberOfSupportedPersonAfterMonthsData};">
            <table class="table">
                <thead>
                    <tr>
                        <th></th>
                        <th class="text-center" colspan="12">Měsíce</th>
                        <th></th>
                    </tr>
                    <tr>
                        <th>Odběratel</th>
                        <th class="text-right">1</th>
                        <th class="text-right">2</th>
                        <th class="text-right">3</th>
                        <th class="text-right">4</th>
                        <th class="text-right">5</th>
                        <th class="text-right">6</th>
                        <th class="text-right">7</th>
                        <th class="text-right">8</th>
                        <th class="text-right">9</th>
                        <th class="text-right">10</th>
                        <th class="text-right">11</th>
                        <th class="text-right">12</th>
                        <th class="text-right">Suma</th>
                    </tr>
                </thead>

                <tbody>
                    <tr ng-repeat="customerData in customerListWithSumNumberOfSupportedPersonAfterMonths">
                        <td>{{ customerData.customerName }}</td>
                        <td ng-repeat="numberOfSupportedPersons in customerData.sumNumberOfSupportedPersonListAfterMonth"
                            class="text-right"> {{ numberOfSupportedPersons }} </td>
                    </tr>

                    <tr
                        ng-init="summaryCustomerListWithSumNumberOfSupportedPersonAfterMonths = {summaryCustomerListWithSumNumberOfSupportedPersonAfterMonthsData}">
                        <th>Celkem</th>
                        <th ng-repeat="summaryNumberOfSupportedPersons in summaryCustomerListWithSumNumberOfSupportedPersonAfterMonths"
                            class="text-right">{{ summaryNumberOfSupportedPersons }}</th>
                    </tr>
                </tbody>
            </table>
        </div>

        <h5 ng-show="report.showEmptyReport">Pro zadané období nejsou žádná data.</h5>
        <a class="btn btn-success" href="../reportList.php" role="button" title="zpět na seznam reportů"><span
                class="glyphicon glyphicon-hand-left"></span>&nbsp;&nbsp;zpět na seznam reportů</a>
        <a class="btn btn-success" href="reportSupportedPersonsByFoodBanks.php?export=1&year={year}" role="button"
            title="export do CSV">export do CSV</a>
        <hr>
        <footer>
            <p>{copyright}</p>
        </footer>
    </div> <!-- /container -->
</body>

</html>
