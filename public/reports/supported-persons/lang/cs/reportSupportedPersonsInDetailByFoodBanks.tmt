<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="description" content="">
    <meta name="author" content="">
    <link rel="icon" href="../../favicon.ico">
    <title>Potravinová banka</title>
    <!-- Bootstrap core CSS -->
    <link href="/bootstrap/css/bootstrap.min.css?hpbankaversion={hpBankaVersion}" rel="stylesheet">
    <link href="/bootstrap-additions/bootstrap-additions.min.css?hpbankaversion={hpBankaVersion}" rel="stylesheet">
    <!-- Custom styles for this template -->
    <link href="/style/hpbanka.css?hpbankaversion={hpBankaVersion}" rel="stylesheet">
    <!-- HTML5 shim and Respond.js for IE8 support of HTML5 elements and media queries -->
    <!--[if lt IE 9]>
      <script src="https://oss.maxcdn.com/html5shiv/3.7.2/html5shiv.min.js?hpbankaversion={hpBankaVersion}"></script>
      <script src="https://oss.maxcdn.com/respond/1.4.2/respond.min.js?hpbankaversion={hpBankaVersion}"></script>
    <![endif]-->
    <script src="/js/angular/angular.min.js?hpbankaversion={hpBankaVersion}"></script>
    <script src="/js/angular/i18n/angular-locale_cs-cz.js?hpbankaversion={hpBankaVersion}"></script>
    <script src="/js/angular-strap/angular-strap.min.js?hpbankaversion={hpBankaVersion}"></script>
    <script src="/js/angular-strap/angular-strap.tpl.min.js?hpbankaversion={hpBankaVersion}"></script>
    <script type="text/javascript" src="/hpBanka.js?hpbankaversion={hpBankaVersion}"></script>
    <script type="text/javascript" src="/hpBankaMenu.js?hpbankaversion={hpBankaVersion}"></script>
    <script type="text/javascript"
        src="reportSupportedPersonsInDetailByFoodBanks.js?hpbankaversion={hpBankaVersion}"></script>
</head>

<body ng-app="PBanka" ng-controller="SupportedPersonsInDetailByFoodBanksController"
    ng-init="basicInformation={basicInformationData}; rightList={rightListData}; report={reportData};">
    <div>
        <div class="container">
            <img src="/images/logo_hpbanka.png" alt="Potravinová banka" id="logoHpBanka">
            <img src="/images/logo/{logoName}" alt="Potravinová banka" id="logoPBanka">
            <div id="namePBanka">{pbName}</div>
        </div>
    </div>
    <hp-banka-admin-menu ng-init="activeMainMenu='reportList';"></hp-banka-admin-menu>
    <div class="container">
        <div class="page-header">
            <h4>Počet podpořených osob po měsících za rok {{ report.year }} podrobně
                <span *ngIf="report.isCustomerGroupNameSet">v organizaci {{ report.customerGroupName }}</span>
            </h4>
            <form class="form-inline" method="post" action="reportSupportedPersonsInDetailByFoodBanks.php"
                novalidate="novalidate" name="reportSupportedPersonsInDetailByFoodBanks">
                <div class="form-group">
                    <label>Odběratelé:</label>
                    <select ng-model="selectedCustomerId" ng-init="selectedCustomerId={selectedCustomerId}"
                        ng-options="customer.customerId as customer.name for customer in report.customerList"
                        class="form-control" name="selectedCustomerId"></select>
                </div>
                <div class="form-group">
                    <label>Podpořená skupina:</label>
                    <select ng-model="selectedGroupId" ng-init="selectedGroupId={selectedGroupId}"
                        ng-options="group.groupId as group.name for group in report.groupOfSupportedPersonList"
                        class="form-control" name="selectedGroupId"></select>
                </div>
                <div class="form-group">
                    <button type="submit" class="btn btn-warning" title="vybrat" name="filter"><span
                            class="glyphicon glyphicon-filter"></span>&nbsp;&nbsp;vybrat</button>
                </div>
                <input type="hidden" name="selectedCustomerId" value="{{ selectedCustomerId }}" />
                <input type="hidden" name="selectedGroupId" value="{{ selectedGroupId }}" />
                <input type="hidden" name="year" value="{year}" />

            </form>
        </div>

        <div class="table-responsive" ng-show="!report.showEmptyReport"
            ng-init="customerListWithNamesOfSupportedPersons={customerListWithNamesOfSupportedPersons};">
            <table class="table" ng-repeat="customerData in customerListWithNamesOfSupportedPersons">
                <thead>
                    <tr>
                        <th></th>
                        <th class="text-center" colspan="12">Měsíce</th>
                        <th></th>
                    </tr>
                    <tr>
                        <th>{{ customerData.name }} </th>
                        <th class="text-right" ng-repeat="n in [].constructor(12) track by $index">{{ $index + 1 }}</th>
                        <th class="text-right">Suma</th>
                    </tr>
                </thead>

                <tbody>
                    <tr ng-repeat="(key, supportedPersonsList) in customerData" ng-if="key !== 'summaryData'">
                        <td class="text-right" ng-if="key !== 'name'">{{ supportedPersonsList.name }}</td>
                        <td ng-repeat="(month, numberOfSupportedPersons) in supportedPersonsList.sumNumberOfSupportedPersons"
                            class="text-right">
                            {{ numberOfSupportedPersons }}
                        </td>
                    </tr>
                    <tr>
                        <th>CELKEM</th>
                        <th ng-repeat="summary in customerData.summaryData" class="text-right">
                            {{ summary }}</th>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="table-responsive" ng-show="!report.showEmptyReport"
            ng-init="customerListWithNamesOfSupportedPersonsInTotal={customerListWithNamesOfSupportedPersonsInTotal};">

            <table class="table">
                <thead>
                    <tr>
                        <th></th>
                        <th class="text-center" colspan="12">Měsíce</th>
                        <th></th>
                    </tr>
                    <tr>
                        <th>ODBĚRATELÉ CELKEM </th>
                        <th class="text-right" ng-repeat="n in [].constructor(12) track by $index">{{ $index + 1 }}</th>
                        <th class="text-right">Suma</th>
                    </tr>
                </thead>
                <tbody>
                    <tr ng-repeat="(key, supportedPersonsList) in customerListWithNamesOfSupportedPersonsInTotal"
                        ng-if="key !== 'summaryData'">
                        <td class="text-right">{{ supportedPersonsList.name }}</td>
                        <td ng-repeat="numberOfSupportedPersons in supportedPersonsList.sumNumberOfSupportedPersons"
                            class="text-right"> {{
                            numberOfSupportedPersons }}</td>
                    </tr>
                    <tr>
                        <th>CELKEM</th>
                        <th ng-repeat="summary in customerListWithNamesOfSupportedPersonsInTotal.summaryData"
                            class="text-right">{{ summary }}</th>
                    </tr>
                </tbody>
            </table>
        </div>

        <h5 ng-show="report.showEmptyReport">Pro zadané období nejsou žádná data.</h5>
        <a class="btn btn-success" href="../reportList.php" role="button" title="zpět na seznam reportů"><span
                class="glyphicon glyphicon-hand-left"></span>&nbsp;&nbsp;zpět na seznam reportů</a>
        <a class="btn btn-success"
            href="reportSupportedPersonsInDetailByFoodBanks.php?export=1&year={year}&selectedCustomerId={{ selectedCustomerId }}&selectedGroupId={{ selectedGroupId }}"
            role="button" title="export do CSV">export do CSV</a>
        <hr>
        <footer>
            <p>{copyright}</p>
        </footer>
    </div> <!-- /container -->
</body>

</html>