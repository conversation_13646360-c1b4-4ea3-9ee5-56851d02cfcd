<?php
header("Content-Type: text/html; charset=utf-8");

require_once '../../../conf/commonApp.php';

doInit();
core\AuthenticationManager::verifyTheAuthenticity($_authenticationCode, $_loggedAppUserId);
$authorizationManager = new core\AuthorizationManager($_loggedAppUser);

if (
    !$authorizationManager->hasRightToPage(core\RightName::reportList) &&
    !$authorizationManager->hasRightToPage(core\RightName::companyReportList)
) {
    core\HttpUtils::redirectAndExit();
}

$rights = $authorizationManager->getRights();
$hasAccessToAllPbanks = $authorizationManager->hasAccessToAllPbanks();

$t = new ui\Template("./lang/cs");
$t->set_file("tmt", "reportSupportedPersonsInDetailByFoodBanks.tmt");

$year = $_REQUEST['year'];
$customerGroupName = '';
$reportManager = report\ReportManager::createForFoodBank($_loggedAppUser->foodBankId);

$supportedPersonListData = array();
$groupOfSupportedPersonManager = \form\GroupOfSupportedPersonManager::create();
$groupOfSupportedPersonList = $groupOfSupportedPersonManager->getGroupOfSupportedPersonList(true);

$customerManager = new \party\CustomerManager();
$basicCustomerList = $customerManager->getBasicCustomerListByFoodBankId($_loggedAppUser->foodBankId);

if ($authorizationManager->isCompanyReporter()) {
    $customerGroupName = $customerManager->getCustomerGroupName($_loggedAppUser->customerId);
    $reportManager->setExactCustomerGroupFilter($customerGroupName);
}

if (isset($_POST['filter'])) {
    $customerId = intval($_POST['selectedCustomerId']);
    $groupId = intval($_POST['selectedGroupId']);
} else {
    $customerId = isset($_GET['selectedCustomerId']) ? intval($_GET['selectedCustomerId']) : 0;
    $groupId = isset($_GET['selectedGroupId']) ? intval($_GET['selectedGroupId']) : 0;
}

$data = $reportManager->getSupportedPersonsInDetailByFilterForTheYear($year, $customerId, $groupId, $hasAccessToAllPbanks);

if ($_GET['export'] == 1) {
    $since = date\DateFormatter::formatToSql(date\Date::create(1, 1, $year));
    $to = date\DateFormatter::formatToSql(date\Date::create(31, 12, $year));
    $CSVHelper = new util\CSVHelper();

    $separator = report\ReportManager::CSV_COLUMN_SEPARATOR;

    $csvContent = "Počet podpořených osob po měsících s odběrateli za rok " . $year . " podrobně v organizaci" . "\n\n";

    foreach ($data['customerListWithNamesOfSupportedPersons'] as $customerData) {

        $monthList = $CSVHelper->getCSVMonthList($customerData['name']);
        $csvContent .= $monthList;

        foreach ($customerData as $key => $supportedPersonsList) {
            if ($key == 'summaryData' or $key == 'name') {
                continue;
            }
            $csvContent .= $supportedPersonsList['name'] . $separator;
            foreach ($supportedPersonsList['sumNumberOfSupportedPersons'] as $numberOfSupportedPerson) {
                $csvContent .= $numberOfSupportedPerson . $separator;
            }
            $csvContent .= "\n";
        }
        $csvContent .= "CELKEM" . $separator;
        foreach ($customerData['summaryData'] as $summary) {
            $csvContent .= $summary . $separator;
        }

        $csvContent .= "\n\n";
    }

    $monthList = $CSVHelper->getCSVMonthList("ODBĚRATELÉ CELKEM");
    $csvContent .= $monthList;

    foreach ($data['customerListWithNamesOfSupportedPersonsInTotal'] as $supportedPersonsList) {
        if (empty($supportedPersonsList['name'])) {
            continue;
        }
        $csvContent .= $supportedPersonsList['name'] . $separator;

        foreach ($supportedPersonsList['sumNumberOfSupportedPersons'] as $numberOfSupportedPerson) {
            $csvContent .= $numberOfSupportedPerson . $separator;
        }
        $csvContent .= "\n";
    }

    $csvContent .= "CELKEM" . $separator;
    foreach ($data['customerListWithNamesOfSupportedPersonsInTotal']['summaryData'] as $summary) {
        $csvContent .= $summary . $separator;
    }

    $csvFilename = report\ReportManager::getCsvFileName("Pocet_podporenych_osob_po_mesicich_s_odberateli_za_rok", $since, $to);
    report\ReportManager::sendCsvReport($csvContent, $csvFilename);
    exit;
}

$reportData = array(
    'year' => $year,
    'customerGroupName' => $customerGroupName,
    'isCustomerGroupNameSet' => trim($customerGroupName) === '',
    'showEmptyReport' => empty($data['customerListWithNamesOfSupportedPersons']) ? true : false,
    'groupOfSupportedPersonList' => $groupOfSupportedPersonList,
    'customerList' => $basicCustomerList
);

$t->set_var(array(
    'customerListWithNamesOfSupportedPersons' => htmlspecialchars(json_encode($data['customerListWithNamesOfSupportedPersons'])),
    'customerListWithNamesOfSupportedPersonsInTotal' => htmlspecialchars(json_encode($data['customerListWithNamesOfSupportedPersonsInTotal'])),
    'reportData' => htmlspecialchars(json_encode($reportData)),
    'year' => $year,
    'selectedCustomerId' => $customerId,
    'selectedGroupId' => $groupId,
    'rightListData' => htmlspecialchars(json_encode($rights))
));

$t->set_global_var_to_template();
$t->pparse("out", "tmt");
