<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="description" content="">
    <meta name="author" content="">
    <link rel="icon" href="../../favicon.ico">
    <title>Potravinová banka</title>
    <!-- Bootstrap core CSS -->
    <link href="/bootstrap/css/bootstrap.min.css?hpbankaversion={hpBankaVersion}" rel="stylesheet">
    <link href="/bootstrap-additions/bootstrap-additions.min.css?hpbankaversion={hpBankaVersion}" rel="stylesheet">
    <!-- Custom styles for this template -->
    <link href="/style/hpbanka.css?hpbankaversion={hpBankaVersion}" rel="stylesheet">
    <!-- HTML5 shim and Respond.js for IE8 support of HTML5 elements and media queries -->
    <!--[if lt IE 9]>
      <script src="https://oss.maxcdn.com/html5shiv/3.7.2/html5shiv.min.js?hpbankaversion={hpBankaVersion}"></script>
      <script src="https://oss.maxcdn.com/respond/1.4.2/respond.min.js?hpbankaversion={hpBankaVersion}"></script>
    <![endif]-->
    <script src="/js/angular/angular.min.js?hpbankaversion={hpBankaVersion}"></script>
    <script src="/js/angular/i18n/angular-locale_cs-cz.js?hpbankaversion={hpBankaVersion}"></script>
    <script src="/js/angular-strap/angular-strap.min.js?hpbankaversion={hpBankaVersion}"></script>
    <script src="/js/angular-strap/angular-strap.tpl.min.js?hpbankaversion={hpBankaVersion}"></script>
    <script type="text/javascript" src="/hpBanka.js?hpbankaversion={hpBankaVersion}"></script>
    <script type="text/javascript" src="/hpBankaMenu.js?hpbankaversion={hpBankaVersion}"></script>
    <script type="text/javascript" src="reportSupplyByStock.js?hpbankaversion={hpBankaVersion}"></script>
  </head>

  <body ng-app="PBanka" ng-controller="ReportSupplyByStockControllerList" ng-init="basicInformation={basicInformationData}; rightList={rightListData}; report={reportData};">
   <div>
    <div class="container">
      <img src="/images/logo_hpbanka.png" alt="Potravinová banka" id="logoHpBanka">
      <img src="/images/logo/{logoName}" alt="Potravinová banka" id="logoPBanka">
      <div id="namePBanka">{pbName}</div>
    </div>
  </div>
  <hp-banka-admin-menu ng-init="activeMainMenu='reportList';"></hp-banka-admin-menu>
  <div class="container">
  <div class="page-header">
    <h4>Inventura stavu zásob podle skladu ke dni {{ report.dateTo |  date: fullDate }} na skladu: {{ report.stockName }}</h4>
    <form  method="post" action="reportSupplyByStock.php" novalidate="novalidate">
      <div class="row">
        <div class="col-xs-4" ng-init="stockList={stockListData}">
            <select id="stock" ng-init="initializeStockList({filterStockId});" ng-model="currentStock" ng-options="stock.name for stock in stockList" class="form-control"></select>
            <input type="hidden" name="stockId" ng-model="currentStock.stockId" value="{{ currentStock.stockId }}"/>
        </div>
        <div class="col-xs-2">
          <input type="text" class="form-control" name="dateTo" placeholder="Inventura ke dni" ng-model="report.filterDateTo" bs-datepicker required>
        </div>
        
        <div class="col-xs-3">
          <input type="hidden" name="defaultDateTo" value="{defaultDateTo}" />
          <input type="hidden" name="defaultStockId" value="{defaultStockId}" />
          <button type="submit" class="btn btn-warning" title="vybrat" name="filter"><span class="glyphicon glyphicon-filter"></span>&nbsp;&nbsp;vybrat</button>
          <a class="btn btn-warning" title="výchozí období" href="reportSupplyByStock.php?defaultDateTo={defaultDateTo}&amp;defaultStockId={defaultStockId}&amp;resetFilter=1"><span class="glyphicon glyphicon-erase"></span>&nbsp;&nbsp;výchozí hodnoty</a>
        </div>
      </div>
    </form>
  </div>
  
  <div class="table-responsive" ng-show="!report.showEmptyReport">
  <table class="table">
    <thead>
      <tr><th class="text-right">Kód</th><th>Název komodity</th><th class="text-right">Množství (kg)</th><th class="text-right">Hodnota ({currencySymbol})</th></tr>
    </thead>
    <tbody ng-init="commoditySummary={commoditySummary};">
      <tr ng-repeat="commodity in commoditySummary.commodities" >
        <td class="text-right">{{ commodity.code }}</td>
        <td>{{ commodity.name }}</td>
        <td class="text-right">{{ commodity.amount | number:3 }}</td>
        <td class="text-right">{{ commodity.value | number:2 }}</td>
      </tr>
      <tr>
        <th class="text-right">Celkem</th>
        <th></th>
        <th class="text-right">{{ commoditySummary.totalAmount | number:3 }}</th>
        <th class="text-right">{{ commoditySummary.totalValue | number:2 }}</th>
      </tr>
    </tbody>
  </table>
  </div>
  
  <h5 ng-show="report.showEmptyReport">Pro zadané období nejsou žádná data.</h5>
  <a class="btn btn-success" href="reportList.php" role="button" title="zpět na seznam reportů"><span class="glyphicon glyphicon-hand-left"></span>&nbsp;&nbsp;zpět na seznam reportů</a>
  <a ng-show="!report.showEmptyReport" class="btn btn-success" href="reportSupplyByStock.php?export=1&amp;stockId={{ currentStock.stockId }}&dateTo={sqlDateTo}" role="button" title="export do CSV">export do CSV</a>
  <hr>
  <footer>
    <p>{copyright}</p>
  </footer>
  </div> <!-- /container -->
  </body>
</html>