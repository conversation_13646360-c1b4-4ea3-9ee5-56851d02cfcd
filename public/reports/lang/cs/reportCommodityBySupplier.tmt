<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="description" content="">
    <meta name="author" content="">
    <link rel="icon" href="../../favicon.ico">
    <title>Potravinová banka</title>
    <!-- Bootstrap core CSS -->
    <link href="/bootstrap/css/bootstrap.min.css?hpbankaversion={hpBankaVersion}" rel="stylesheet">
    <link href="/bootstrap-additions/bootstrap-additions.min.css?hpbankaversion={hpBankaVersion}" rel="stylesheet">
    <!-- Custom styles for this template -->
    <link href="/style/hpbanka.css?hpbankaversion={hpBankaVersion}" rel="stylesheet">
    <!-- HTML5 shim and Respond.js for IE8 support of HTML5 elements and media queries -->
    <!--[if lt IE 9]>
      <script src="https://oss.maxcdn.com/html5shiv/3.7.2/html5shiv.min.js?hpbankaversion={hpBankaVersion}"></script>
      <script src="https://oss.maxcdn.com/respond/1.4.2/respond.min.js?hpbankaversion={hpBankaVersion}"></script>
    <![endif]-->
    <script src="/js/angular/angular.min.js?hpbankaversion={hpBankaVersion}"></script>
    <script src="/js/angular/i18n/angular-locale_cs-cz.js?hpbankaversion={hpBankaVersion}"></script>
    <script src="/js/angular-strap/angular-strap.min.js?hpbankaversion={hpBankaVersion}"></script>
    <script src="/js/angular-strap/angular-strap.tpl.min.js?hpbankaversion={hpBankaVersion}"></script>
    <script type="text/javascript" src="/hpBanka.js?hpbankaversion={hpBankaVersion}"></script>
    <script type="text/javascript" src="/hpBankaMenu.js?hpbankaversion={hpBankaVersion}"></script>
    <script type="text/javascript" src="reportCommodityBySupplier.js?hpbankaversion={hpBankaVersion}"></script>
</head>

<body ng-app="PBanka" ng-controller="ReportCommodityBySupplierController"
    ng-init="basicInformation={basicInformationData}; rightList={rightListData}; report={reportData};">
    <div>
        <div class="container">
            <img src="/images/logo_hpbanka.png" alt="Potravinová banka" id="logoHpBanka">
            <img src="/images/logo/{logoName}" alt="Potravinová banka" id="logoPBanka">
            <div id="namePBanka">{pbName}</div>
        </div>
    </div>
    <hp-banka-admin-menu ng-init="activeMainMenu='reportList';"></hp-banka-admin-menu>
    <div class="container">
        <div class="page-header">
            <h4>Naskladněné komodity podle dodavatelů za řetězec za období od {{ report.dateFrom | date: fullDate }} do
                {{ report.dateTo | date: fullDate }}</h4>
            <form method="post" action="reportCommodityBySupplier.php" novalidate="novalidate">
                <div class="row">
                    <div class="col-xs-2">
                        <input type="text" class="form-control" name="actionSearchString"
                            ng-model="report.actionSearchString" placeholder="Název formuláře">
                    </div>
                    <div class="col-xs-2">
                        <input type="text" class="form-control" name="commoditySearchString"
                            ng-model="report.commoditySearchString" placeholder="Název komodity">
                    </div>
                    <div class="col-xs-2">
                        <input type="text" class="form-control" name="supplierNameSearchString"
                            ng-model="report.supplierNameSearchString" placeholder="Název dodavatele">
                    </div>
                    <div class="col-xs-3">
                        <input type="text" class="form-control" name="supplierGroupSearchString"
                            ng-model="report.supplierGroupSearchString" placeholder="Organizace dodavatele">
                    </div>
                </div>
                <br>
                <div class="row">
                    <div class="col-xs-2">
                        <input type="text" class="form-control" name="dateFrom" placeholder="Období od"
                            ng-model="report.filterDateFrom" data-max-date="{{report.filterDateTo}}" bs-datepicker
                            required>
                    </div>
                    <div class="col-xs-2">
                        <input type="text" class="form-control" name="dateTo" placeholder="Období do"
                            ng-model="report.filterDateTo" data-min-date="{{report.filterDateFrom}}" bs-datepicker
                            required>
                    </div>
                    <div ng-show="rightList.hasAccessToAllPbanks.read" class="col-xs-4"
                        ng-init="foodBankList={foodBankListData}">
                        <select class="form-control" ng-model="currentFoodBank"
                            ng-options="foodBank.name for foodBank in foodBankList"
                            ng-init="inicializeFoodBank({filterFoodBankId});"></select>
                        <input type="hidden" name="filterFoodBankId" ng-model="currentFoodBank.foodBankId"
                            value="{{ currentFoodBank.foodBankId }}" />
                    </div>
                    <div ng-class="{true:'col-xs-1', false:'col-xs-5'}[rightList.hasAccessToAllPbanks.read]">
                        <span ng-show="!rightList.hasAccessToAllPbanks.read"><input type="checkbox" id="includeValue"
                                name="includeValue" ng-model="includeValue">
                            <label for="includeValue">&nbsp;zahrnout hodnotu komodit</label></span>
                    </div>
                    <div class="col-xs-3">
                        <input type="hidden" name="defaultDateFrom" value="{defaultDateFrom}" />
                        <input type="hidden" name="defaultDateTo" value="{defaultDateTo}" />
                        <button type="submit" class="btn btn-warning" title="vybrat" name="filter"><span
                                class="glyphicon glyphicon-filter"></span>&nbsp;&nbsp;vybrat</button>
                        <a class="btn btn-warning" title="výchozí období"
                            href="reportCommodityBySupplier.php?defaultDateFrom={defaultDateFrom}&amp;defaultDateTo={defaultDateTo}&amp;resetFilter=1"><span
                                class="glyphicon glyphicon-erase"></span>&nbsp;&nbsp;výchozí období</a>
                    </div>
                </div>
            </form>
        </div>

        <div class="table-responsive" ng-show="!report.showEmptyReport"
            ng-init="commoditySummaryBySupplier={commoditySummaryBySupplier};totalAllGroups={totalAllGroups};">
            <table class="table" ng-repeat="supplierGroupData in commoditySummaryBySupplier">
                <thead>
                    <tr>
                        <th colspan="3">Řetězec: {{ supplierGroupData.supplierGroup }} </th>
                    </tr>
                    <tr>
                        <th width="70%">Dodavatel</th>
                        <th class="text-right">Množství (kg)</th>
                        <th ng-show="includeValue" class="text-right">Hodnota ({currencySymbol})</th>
                    </tr>
                </thead>
                <tbody>
                    <tr ng-repeat="supplier in supplierGroupData.suppliers">
                        <td>{{ supplier.supplierName }}</td>
                        <td class="text-right">{{ supplier.amount | number:3 }}</td>
                        <td ng-show="includeValue" class="text-right">{{ supplier.value | number:2 }}</td>
                    </tr>
                    <tr>
                        <th>Celkem za řetězec</th>
                        <th class="text-right">{{ supplierGroupData.totalAmount | number:3 }}</th>
                        <th ng-show="includeValue" class="text-right">{{ supplierGroupData.totalValue | number:2 }}</th>
                    </tr>
                </tbody>
            </table>
            <table class="table">
                <thead>
                    <tr>
                        <th ng-show="includeValue" colspan="3">Všichni dodavatelé dodali celkem {{ totalAllGroups.amount
                            | number:3 }} kg
                            komodit v hodnotě {{ totalAllGroups.value | number:2 }} {currencySymbol}.</th>
                    </tr>
                    <tr>
                        <th ng-show="!includeValue" colspan="3">Všichni dodavatelé dodali celkem {{
                            totalAllGroups.amount | number:3 }} kg komodit.</th>
                    </tr>
                </thead>
            </table>
        </div>

        <h5 ng-show="report.showEmptyReport">Pro zadané období nejsou žádná data.</h5>
        <a class="btn btn-success" href="reportList.php" role="button" title="zpět na seznam reportů"><span
                class="glyphicon glyphicon-hand-left"></span>&nbsp;&nbsp;zpět na seznam reportů</a>
        <a class="btn btn-success"
            href="reportCommodityBySupplier.php?export=1&includeValue={{ includeValue | number }}&dateFrom={sqlDateFrom}&dateTo={sqlDateTo}"
            role="button" title="export do CSV">export do CSV</a>
        <hr>
        <footer>
            <p>{copyright}</p>
        </footer>
    </div> <!-- /container -->
</body>

</html>
