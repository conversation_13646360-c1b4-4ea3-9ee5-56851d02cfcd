<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="description" content="">
    <meta name="author" content="">
    <link rel="icon" href="../../favicon.ico">
    <title>Potravinová banka</title>
    <!-- Bootstrap core CSS -->
    <link href="/bootstrap/css/bootstrap.min.css?hpbankaversion={hpBankaVersion}" rel="stylesheet">
    <link href="/bootstrap-additions/bootstrap-additions.min.css?hpbankaversion={hpBankaVersion}" rel="stylesheet">
    <!-- Custom styles for this template -->
    <link href="/style/hpbanka.css?hpbankaversion={hpBankaVersion}" rel="stylesheet">
    <!-- HTML5 shim and Respond.js for IE8 support of HTML5 elements and media queries -->
    <!--[if lt IE 9]>
      <script src="https://oss.maxcdn.com/html5shiv/3.7.2/html5shiv.min.js?hpbankaversion={hpBankaVersion}"></script>
      <script src="https://oss.maxcdn.com/respond/1.4.2/respond.min.js?hpbankaversion={hpBankaVersion}"></script>
    <![endif]-->
    <script src="/js/angular/angular.min.js?hpbankaversion={hpBankaVersion}"></script>
    <script src="/js/angular/i18n/angular-locale_cs-cz.js?hpbankaversion={hpBankaVersion}"></script>
    <script src="/js/jquery.min.js?hpbankaversion={hpBankaVersion}"></script>
    <script src="/bootstrap/js/bootstrap.min.js?hpbankaversion={hpBankaVersion}"></script>
    <script src="/js/angular-strap/angular-strap.min.js?hpbankaversion={hpBankaVersion}"></script>
    <script src="/js/angular-strap/angular-strap.tpl.min.js?hpbankaversion={hpBankaVersion}"></script>
    <script type="text/javascript" src="/hpBanka.js?hpbankaversion={hpBankaVersion}"></script>
    <script type="text/javascript" src="/hpBankaMenu.js?hpbankaversion={hpBankaVersion}"></script>
    <script type="text/javascript" src="/numberLimits.js?hpbankaversion={hpBankaVersion}"></script>
    <script type="text/javascript" src="reportList.js?hpbankaversion={hpBankaVersion}"></script>
</head>

<body ng-app="PBanka" ng-controller="ReportListController"
    ng-init="basicInformation={basicInformationData}; rightList={rightListData}; reportList={reportListData};">
    <div>
        <div class="container">
            <img src="/images/logo_hpbanka.png" alt="Potravinová banka" id="logoHpBanka">
            <img src="/images/logo/{logoName}" alt="Potravinová banka" id="logoPBanka">
            <div id="namePBanka">{pbName}</div>
        </div>
    </div>
    <hp-banka-admin-menu ng-init="activeMainMenu='reportList';"></hp-banka-admin-menu>
    <div class="container">
        <br>
        <div class="panel panel-default" ng-if="rightList.reportList.read">
            <div class="panel-body">
                <form class="form-inline" method="post" action="reportCommodity.php" name="reportCommodity">
                    <div class="form-group"
                        ng-class="{true:'has-error', false:''}[(reportCommodity.dateFrom.$error.required || reportCommodity.dateFrom.$error.date) && !reportCommodity.dateFrom.$pristine]">
                        <label for="reportCommodityDateFrom">1. Naskladněné komodity za období od:</label>
                        <input type="text" class="form-control" id="reportCommodityDateFrom" name="dateFrom" size="12"
                            placeholder="DD.MM.YYYY" ng-model="report.reportCommodityDateFrom"
                            data-max-date="{{report.reportCommodityDateTo}}" bs-datepicker required>
                        <label class="control-label" for=""
                            ng-show="reportCommodity.dateFrom.$error.required && !reportCommodity.dateFrom.$pristine">Tento
                            údaj je povinný.</label>
                        <label class="control-label" for=""
                            ng-show="reportCommodity.dateFrom.$error.date && !reportCommodity.dateFrom.$pristine">Zadejte
                            datum ve formátu DD.MM.YYYY.</label>
                        <input type="hidden" name="includeValue" value="1" />
                        <input type="hidden" name="includeReducedAmount" value="1" />
                    </div>
                    <div class="form-group"
                        ng-class="{true:'has-error', false:''}[(reportCommodity.dateTo.$error.required || reportCommodity.dateTo.$error.date) && !reportCommodity.dateTo.$pristine]">
                        <label for="reportCommodityDateTo">do:</label>
                        <input type="text" class="form-control" id="reportCommodityDateTo" name="dateTo" size="12"
                            placeholder="DD.MM.YYYY" ng-model="report.reportCommodityDateTo"
                            data-min-date="{{report.reportCommodityDateFrom}}" bs-datepicker required>
                        <label class="control-label" for=""
                            ng-show="reportCommodity.dateTo.$error.required && !reportCommodity.dateTo.$pristine">Tento
                            údaj je povinný.</label>
                        <label class="control-label" for=""
                            ng-show="reportCommodity.dateTo.$error.date && !reportCommodity.dateTo.$pristine">Zadejte
                            datum ve formátu DD.MM.YYYY.</label>
                    </div>
                    <div class="form-group"
                        ng-class="{true:'has-error', false:''}[(reportCommodity.percentReduction.$error.required || reportCommodity.percentReduction.$error.ngMin || reportCommodity.percentReduction.$error.ngMax || reportCommodity.percentReduction.$error.pattern) && !reportCommodity.percentReduction.$pristine]">
                        <label for=" percentReduction"> snížit o: </label>
                        <input ng-init="report.reportCommodityPercentReduction=0" type="text" class="form-control"
                            id="percentReduction" name="percentReduction" size="3"
                            ng-model="report.reportCommodityPercentReduction" ng-pattern="regNumber" ng-min="0"
                            ng-max="100" required>
                        <label> %</label>
                        <label class="control-label" for=""
                            ng-show="reportCommodity.percentReduction.$error.required && !reportCommodity.percentReduction.$pristine">Tento
                            údaj je povinný.</label>
                        <label class="control-label" for=""
                            ng-show="(reportCommodity.percentReduction.$error.ngMin || reportCommodity.percentReduction.$error.ngMax) && !reportCommodity.percentReduction.$pristine">Hodnota
                            musí být v rozsahu 0 - 100.</label>
                        <label class="control-label" for="" ng-show="reportCommodity.percentReduction.$error.pattern">
                            Prosím, zadejte kladné číslo.</label>
                    </div>
                    <input type="hidden" name="settingTheBaselinePeriod" value="1" />
                    <button type="submit" class="btn btn-success"
                        ng-disabled="!reportCommodity.$valid;">Zobrazit</button>
                    <button type="submit" class="btn btn-success" name="withoutDirectConsumption" value="1"
                        ng-disabled="!reportCommodity.$valid;">Zobrazit bez přímých odběrů</button>
                </form>
            </div>
        </div>

        <div class="panel panel-default" ng-if="rightList.reportList.read">
            <div class="panel-body">
                <form class="form-inline" method="post" action="reportCommodityBySupplierGroup.php"
                    name="reportCommodityBySupplierGroup">
                    <div class="form-group"
                        ng-class="{true:'has-error', false:''}[(reportCommodityBySupplierGroup.dateFrom.$error.required || reportCommodityBySupplierGroup.dateFrom.$error.date) && !reportCommodityBySupplierGroup.dateFrom.$pristine]">
                        <label for="reportCommodityBySupplierGroupDateFrom">2. Naskladněné komodity podle řetězce za
                            období od:</label>
                        <input type="text" class="form-control" id="reportCommodityBySupplierGroupDateFrom"
                            name="dateFrom" placeholder="DD.MM.YYYY"
                            ng-model="report.reportCommodityBySupplierGroupDateFrom"
                            data-max-date="{{report.reportCommodityBySupplierGroupDateTo}}" bs-datepicker required>
                        <label class="control-label" for=""
                            ng-show="reportCommodityBySupplierGroup.dateFrom.$error.required && !reportCommodityBySupplierGroup.dateFrom.$pristine">Tento
                            údaj je povinný.</label>
                        <label class="control-label" for=""
                            ng-show="reportCommodityBySupplierGroup.dateFrom.$error.date && !reportCommodityBySupplierGroup.dateFrom.$pristine">Zadejte
                            datum ve formátu DD.MM.YYYY.</label>
                    </div>
                    <div class="form-group"
                        ng-class="{true:'has-error', false:''}[(reportCommodityBySupplierGroup.dateTo.$error.required || reportCommodityBySupplierGroup.dateTo.$error.date) && !reportCommodityBySupplierGroup.dateTo.$pristine]">
                        <label for="reportCommodityBySupplierGroupDateTo">do:</label>
                        <input type="text" class="form-control" id="reportCommodityBySupplierGroupDateTo" name="dateTo"
                            placeholder="DD.MM.YYYY" ng-model="report.reportCommodityBySupplierGroupDateTo"
                            data-min-date="{{report.reportCommodityBySupplierGroupDateFrom}}" bs-datepicker required>
                        <label class="control-label" for=""
                            ng-show="reportCommodityBySupplierGroup.dateTo.$error.required && !reportCommodityBySupplierGroup.dateTo.$pristine">Tento
                            údaj je povinný.</label>
                        <label class="control-label" for=""
                            ng-show="reportCommodityBySupplierGroup.dateTo.$error.date && !reportCommodityBySupplierGroup.dateTo.$pristine">Zadejte
                            datum ve formátu DD.MM.YYYY.</label>
                    </div>
                    <div class="form-group"
                        ng-class="{true:'has-error', false:''}[(reportCommodityBySupplierGroup.percentReduction.$error.required || reportCommodityBySupplierGroup.percentReduction.$error.ngMin || reportCommodityBySupplierGroup.percentReduction.$error.ngMax || reportCommodityBySupplierGroup.percentReduction.$error.pattern) && !reportCommodityBySupplierGroup.percentReduction.$pristine]">
                        <label for=" percentReduction"> snížit o: </label>
                        <input ng-init="report.reportCommodityPercentReduction=0" type="text" class="form-control"
                            id="percentReduction" name="percentReduction" size="3"
                            ng-model="report.reportCommodityPercentReduction" ng-pattern="regNumber" ng-min="0"
                            ng-max="100" required>
                        <label> %</label>
                        <label class="control-label" for=""
                            ng-show="reportCommodityBySupplierGroup.percentReduction.$error.required && !reportCommodityBySupplierGroup.percentReduction.$pristine">Tento
                            údaj je povinný.</label>
                        <label class="control-label" for=""
                            ng-show="(reportCommodityBySupplierGroup.percentReduction.$error.ngMin || reportCommodityBySupplierGroup.percentReduction.$error.ngMax) && !reportCommodityBySupplierGroup.percentReduction.$pristine">Hodnota
                            musí být v rozsahu 0 - 100.</label>
                        <label class="control-label" for=""
                            ng-show="reportCommodityBySupplierGroup.percentReduction.$error.pattern">
                            Prosím, zadejte kladné číslo.</label>
                    </div>
                    <input type="hidden" name="settingTheBaselinePeriod" value="1" />
                    <button type="submit" class="btn btn-success"
                        ng-disabled="!reportCommodityBySupplierGroup.$valid;">Zobrazit</button>
                </form>
            </div>
        </div>

        <div class="panel panel-default" ng-if="rightList.reportList.read">
            <div class="panel-body">
                <form class="form-inline" method="post" action="reportCommodityBySupplier.php"
                    name="reportCommodityBySupplier">
                    <div class="form-group"
                        ng-class="{true:'has-error', false:''}[(reportCommodityBySupplier.dateFrom.$error.required || reportCommodityBySupplier.dateFrom.$error.date) && !reportCommodityBySupplier.dateFrom.$pristine]">
                        <label for="reportCommodityBySupplierDateFrom">3. Naskladněné komodity podle dodavatelů za
                            řetězec za období od:</label>
                        <input type="text" class="form-control" id="reportCommodityBySupplierDateFrom" name="dateFrom"
                            placeholder="DD.MM.YYYY" ng-model="report.reportCommodityBySupplierDateFrom"
                            data-max-date="{{report.reportCommodityBySupplierDateTo}}" bs-datepicker required>
                        <label class="control-label" for=""
                            ng-show="reportCommodityBySupplier.dateFrom.$error.required && !reportCommodityBySupplier.dateFrom.$pristine">Tento
                            údaj je povinný.</label>
                        <label class="control-label" for=""
                            ng-show="reportCommodityBySupplier.dateFrom.$error.date && !reportCommodityBySupplier.dateFrom.$pristine">Zadejte
                            datum ve formátu DD.MM.YYYY.</label>
                    </div>
                    <div class="form-group"
                        ng-class="{true:'has-error', false:''}[(reportCommodityBySupplier.dateTo.$error.required || reportCommodityBySupplier.dateTo.$error.date) && !reportCommodityBySupplier.dateTo.$pristine]">
                        <label for="reportCommodityBySupplierDateTo">do:</label>
                        <input type="text" class="form-control" id="reportCommodityBySupplierDateTo" name="dateTo"
                            placeholder="DD.MM.YYYY" ng-model="report.reportCommodityBySupplierDateTo"
                            data-min-date="{{report.reportCommodityBySupplierDateFrom}}" bs-datepicker required>
                        <label class="control-label" for=""
                            ng-show="reportCommodityBySupplier.dateTo.$error.required && !reportCommodityBySupplier.dateTo.$pristine">Tento
                            údaj je povinný.</label>
                        <label class="control-label" for=""
                            ng-show="reportCommodityBySupplier.dateTo.$error.date && !reportCommodityBySupplier.dateTo.$pristine">Zadejte
                            datum ve formátu DD.MM.YYYY.</label>
                    </div>
                    <input type="hidden" name="settingTheBaselinePeriod" value="1" />
                    <button type="submit" class="btn btn-success"
                        ng-disabled="!reportCommodityBySupplier.$valid;">Zobrazit</button>
                </form>
            </div>
        </div>

        <div class="panel panel-default" ng-if="rightList.reportList.read">
            <div class="panel-body">
                <form class="form-inline" method="post" action="reportCommodityBySupplierGroupList.php"
                    name="reportCommodityBySupplierGroupList">
                    <div class="form-group"
                        ng-class="{true:'has-error', false:''}[(reportCommodityBySupplierGroupList.dateFrom.$error.required || reportCommodityBySupplierGroupList.dateFrom.$error.date) && !reportCommodityBySupplierGroupList.dateFrom.$pristine]">
                        <label for="reportCommodityBySupplierGroupDateFromList">4. Naskladněné komodity celkem podle
                            řetězců za období od:</label>
                        <input type="text" class="form-control" id="reportCommodityBySupplierGroupDateFromList"
                            name="dateFrom" placeholder="DD.MM.YYYY"
                            ng-model="report.reportCommodityBySupplierGroupDateFromList"
                            data-max-date="{{report.reportCommodityBySupplierGroupDateToList}}" bs-datepicker required>
                        <label class="control-label" for=""
                            ng-show="reportCommodityBySupplierGroupList.dateFrom.$error.required && !reportCommodityBySupplierGroupList.dateFrom.$pristine">Tento
                            údaj je povinný.</label>
                        <label class="control-label" for=""
                            ng-show="reportCommodityBySupplierGroupList.dateFrom.$error.date && !reportCommodityBySupplierGroupList.dateFrom.$pristine">Zadejte
                            datum ve formátu DD.MM.YYYY.</label>
                    </div>
                    <div class="form-group"
                        ng-class="{true:'has-error', false:''}[(reportCommodityBySupplierGroupList.dateTo.$error.required || reportCommodityBySupplierGroupList.dateTo.$error.date) && !reportCommodityBySupplierGroupList.dateTo.$pristine]">
                        <label for="reportCommodityBySupplierGroupDateToList">do:</label>
                        <input type="text" class="form-control" id="reportCommodityBySupplierGroupDateToList"
                            name="dateTo" placeholder="DD.MM.YYYY"
                            ng-model="report.reportCommodityBySupplierGroupDateToList"
                            data-min-date="{{report.reportCommodityBySupplierGroupDateFromList}}" bs-datepicker
                            required>
                        <label class="control-label" for=""
                            ng-show="reportCommodityBySupplierGroupList.dateTo.$error.required && !reportCommodityBySupplierGroupList.dateTo.$pristine">Tento
                            údaj je povinný.</label>
                        <label class="control-label" for=""
                            ng-show="reportCommodityBySupplierGroupList.dateTo.$error.date && !reportCommodityBySupplierGroupList.dateTo.$pristine">Zadejte
                            datum ve formátu DD.MM.YYYY.</label>
                    </div>
                    <input type="hidden" name="settingTheBaselinePeriod" value="1" />
                    <button type="submit" class="btn btn-success"
                        ng-disabled="!reportCommodityBySupplierGroupList.$valid;">Zobrazit</button>
                </form>
            </div>
        </div>

        <div class="panel panel-default" ng-if="rightList.reportList.read">
            <div class="panel-body">
                <form class="form-inline" method="post" action="reportCommoditiesRemovedFromStorage.php"
                    name="reportCommoditiesRemovedFromStorage">
                    <div class="form-group"
                        ng-class="{true:'has-error', false:''}[(reportCommoditiesRemovedFromStorage.dateFrom.$error.required || reportCommoditiesRemovedFromStorage.dateFrom.$error.date) && !reportCommoditiesRemovedFromStorage.dateFrom.$pristine]">
                        <label for="reportCommoditiesRemovedFromStorageDateFrom">5. Vyskladněné komodity za období
                            od:</label>
                        <input type="text" class="form-control" id="reportCommoditiesRemovedFromStorageDateFrom"
                            name="dateFrom" placeholder="DD.MM.YYYY"
                            ng-model="report.reportCommoditiesRemovedFromStorageDateFrom"
                            data-max-date="{{report.reportCommoditiesRemovedFromStorageDateTo}}" bs-datepicker required>
                        <label class="control-label" for=""
                            ng-show="reportCommoditiesRemovedFromStorage.dateFrom.$error.required && !reportCommoditiesRemovedFromStorage.dateFrom.$pristine">Tento
                            údaj je povinný.</label>
                        <label class="control-label" for=""
                            ng-show="reportCommoditiesRemovedFromStorage.dateFrom.$error.date && !reportCommoditiesRemovedFromStorage.dateFrom.$pristine">Zadejte
                            datum ve formátu DD.MM.YYYY.</label>
                    </div>
                    <div class="form-group"
                        ng-class="{true:'has-error', false:''}[(reportCommoditiesRemovedFromStorage.dateTo.$error.required || reportCommoditiesRemovedFromStorage.dateTo.$error.date) && !reportCommoditiesRemovedFromStorage.dateTo.$pristine]">
                        <label for="reportCommoditiesRemovedFromStorageDateTo">do:</label>
                        <input type="text" class="form-control" id="reportCommoditiesRemovedFromStorageDateTo"
                            name="dateTo" placeholder="DD.MM.YYYY"
                            ng-model="report.reportCommoditiesRemovedFromStorageDateTo"
                            data-min-date="{{report.reportCommoditiesRemovedFromStorageDateFrom}}" bs-datepicker
                            required>
                        <label class="control-label" for=""
                            ng-show="reportCommoditiesRemovedFromStorage.dateTo.$error.required && !reportCommoditiesRemovedFromStorage.dateTo.$pristine">Tento
                            údaj je povinný.</label>
                        <label class="control-label" for=""
                            ng-show="reportCommoditiesRemovedFromStorage.dateTo.$error.date && !reportCommoditiesRemovedFromStorage.dateTo.$pristine">Zadejte
                            datum ve formátu DD.MM.YYYY.</label>
                    </div>
                    <input type="hidden" name="settingTheBaselinePeriod" value="1" />
                    <button type="submit" class="btn btn-success"
                        ng-disabled="!reportCommoditiesRemovedFromStorage.$valid;">Zobrazit</button>
                </form>
            </div>
        </div>

        <div class="panel panel-default" ng-if="rightList.reportList.read || rightList.companyReportList.read">
            <div class="panel-body">
                <form class="form-inline" method="post" action="reportCommodityByCustomer.php"
                    name="reportCommodityByCustomer">
                    <div class="form-group"
                        ng-class="{true:'has-error', false:''}[(reportCommodityByCustomer.dateFrom.$error.required || reportCommodityByCustomer.dateFrom.$error.date) && !reportCommodityByCustomer.dateFrom.$pristine]">
                        <label for="reportCommodityByCustomerDateFrom">6. Vyskladněné komodity podle odběratelů za
                            období od:</label>
                        <input type="text" class="form-control" id="reportCommodityByCustomerDateFrom" name="dateFrom"
                            placeholder="DD.MM.YYYY" ng-model="report.reportCommodityByCustomerDateFrom"
                            data-max-date="{{report.reportCommodityByCustomerDateTo}}" bs-datepicker required>
                        <label class="control-label" for=""
                            ng-show="reportCommodityByCustomer.dateFrom.$error.required && !reportCommodityByCustomer.dateFrom.$pristine">Tento
                            údaj je povinný.</label>
                        <label class="control-label" for=""
                            ng-show="reportCommodityByCustomer.dateFrom.$error.date && !reportCommodityByCustomer.dateFrom.$pristine">Zadejte
                            datum ve formátu DD.MM.YYYY.</label>
                    </div>
                    <div class="form-group"
                        ng-class="{true:'has-error', false:''}[(reportCommodityByCustomer.dateTo.$error.required || reportCommodityByCustomer.dateTo.$error.date) && !reportCommodityByCustomer.dateTo.$pristine]">
                        <label for="reportCommodityByCustomerDateTo">do:</label>
                        <input type="text" class="form-control" id="reportCommodityByCustomerDateTo" name="dateTo"
                            placeholder="DD.MM.YYYY" ng-model="report.reportCommodityByCustomerDateTo"
                            data-min-date="{{report.reportCommodityByCustomerDateFrom}}" bs-datepicker required>
                        <label class="control-label" for=""
                            ng-show="reportCommodityByCustomer.dateTo.$error.required && !reportCommodityByCustomer.dateTo.$pristine">Tento
                            údaj je povinný.</label>
                        <label class="control-label" for=""
                            ng-show="reportCommodityByCustomer.dateTo.$error.date && !reportCommodityByCustomer.dateTo.$pristine">Zadejte
                            datum ve formátu DD.MM.YYYY.</label>
                    </div>
                    <input type="hidden" name="settingTheBaselinePeriod" value="1" />
                    <button type="submit" class="btn btn-success"
                        ng-disabled="!reportCommodityByCustomer.$valid;">Zobrazit</button>
                </form>
            </div>
        </div>

        <div class="panel panel-default" ng-if="rightList.reportList.read || rightList.companyReportList.read">
            <div class="panel-body">
                <form class="form-inline" method="post" action="reportStockReleaseNotesSummaryByCustomer.php"
                    name="reportStockReleaseNotesSummaryByCustomer">
                    <div class="form-group"
                        ng-class="{true:'has-error', false:''}[(reportStockReleaseNotesSummaryByCustomer.dateFrom.$error.required || reportStockReleaseNotesSummaryByCustomer.dateFrom.$error.date) && !reportStockReleaseNotesSummaryByCustomer.dateFrom.$pristine]">
                        <label for="reportStockReleaseNotesSummaryByCustomerDateFrom">7. Vyskladněné celkové množství
                            komodit podle odběratelů za období od:</label>
                        <input type="text" class="form-control" id="reportStockReleaseNotesSummaryByCustomerDateFrom"
                            name="dateFrom" placeholder="DD.MM.YYYY"
                            ng-model="report.reportStockReleaseNotesSummaryByCustomerDateFrom"
                            data-max-date="{{report.reportStockReleaseNotesSummaryByCustomerDateTo}}" bs-datepicker
                            required>
                        <label class="control-label" for=""
                            ng-show="reportStockReleaseNotesSummaryByCustomer.dateFrom.$error.required && !reportStockReleaseNotesSummaryByCustomer.dateFrom.$pristine">Tento
                            údaj je povinný.</label>
                        <label class="control-label" for=""
                            ng-show="reportStockReleaseNotesSummaryByCustomer.dateFrom.$error.date && !reportStockReleaseNotesSummaryByCustomer.dateFrom.$pristine">Zadejte
                            datum ve formátu DD.MM.YYYY.</label>
                    </div>
                    <div class="form-group"
                        ng-class="{true:'has-error', false:''}[(reportStockReleaseNotesSummaryByCustomer.dateTo.$error.required || reportStockReleaseNotesSummaryByCustomer.dateTo.$error.date) && !reportStockReleaseNotesSummaryByCustomer.dateTo.$pristine]">
                        <label for="reportStockReleaseNotesSummaryByCustomerDateTo">do:</label>
                        <input type="text" class="form-control" id="reportStockReleaseNotesSummaryByCustomerDateTo"
                            name="dateTo" placeholder="DD.MM.YYYY"
                            ng-model="report.reportStockReleaseNotesSummaryByCustomerDateTo"
                            data-min-date="{{report.reportStockReleaseNotesSummaryByCustomerDateFrom}}" bs-datepicker
                            required>
                        <label class="control-label" for=""
                            ng-show="reportStockReleaseNotesSummaryByCustomer.dateTo.$error.required && !reportStockReleaseNotesSummaryByCustomer.dateTo.$pristine">Tento
                            údaj je povinný.</label>
                        <label class="control-label" for=""
                            ng-show="reportStockReleaseNotesSummaryByCustomer.dateTo.$error.date && !reportStockReleaseNotesSummaryByCustomer.dateTo.$pristine">Zadejte
                            datum ve formátu DD.MM.YYYY.</label>
                    </div>
                    <input type="hidden" name="settingTheBaselinePeriod" value="1" />
                    <button type="submit" class="btn btn-success"
                        ng-disabled="!reportStockReleaseNotesSummaryByCustomer.$valid;">Zobrazit</button>
                </form>
            </div>
        </div>

        <!--<div class="panel panel-default">-->
        <!--<div class="panel-body">-->
        <!--<form class="form-inline" method="post" action="reportSupportPersonListAfterMonthsForThePeriod.php">-->
        <!--<div class="form-group" ng-init="yearList={yearListData}">-->
        <!--<label>7. Počet podpořených osob po měsících s odběrateli za rok:</label>-->
        <!--<select ng-init="initializeYearList({initYear});" id="year" ng-model="reportList.selectedYearInReportSupportPersonListAfterMonths" ng-options="year.name for year in yearList" class="form-control"></select>-->
        <!--</div>-->
        <!--<input name="year" type="hidden" value="{{ reportList.selectedYearInReportSupportPersonListAfterMonths.id }}" />-->
        <!--<button type="submit" name="show" class="btn btn-success">Zobrazit</button>-->
        <!--</form>-->
        <!--</div>-->
        <!--</div>-->

        <div class="panel panel-default" ng-show="rightList.reportList.read || rightList.companyReportList.read">
            <div class="panel-body">
                <form class="form-inline" method="post" action="supported-persons/reportSupportedPersonsByFoodBanks.php"
                    name="reportSupportedPersonsByFoodBanks">
                    <div class="form-group" ng-init="yearList={yearListData}">
                        <label>8. Počet podpořených osob po měsících s odběrateli za rok:</label>
                        <select ng-init="initializeYearList({initYear});" id="year"
                            ng-model="reportList.selectedYearInReportSupportPersonListAfterMonths"
                            ng-options="year.name for year in yearList" class="form-control"></select>
                    </div>
                    <input name="year" type="hidden"
                        value="{{ reportList.selectedYearInReportSupportPersonListAfterMonths.id }}" />
                    <button type="submit" name="show" class="btn btn-success">Zobrazit</button>
                </form>
            </div>
        </div>

        <div class="panel panel-default" ng-show="rightList.reportList.read || rightList.companyReportList.read">
            <div class="panel-body">
                <form class="form-inline" method="post"
                    action="supported-persons/reportSupportedPersonsInDetailByFoodBanks.php"
                    name="reportSupportedPersonsInDetailByFoodBanks">
                    <div class="form-group" ng-init="yearList={yearListData}">
                        <label>9. Počet podpořených osob po měsících s odběrateli za rok -
                            podrobně:</label>
                        <select ng-init="initializeYearList({initYear});" id="year"
                            ng-model="reportList.selectedYearInReportSupportPersonListAfterMonths"
                            ng-options="year.name for year in yearList" class="form-control"></select>
                    </div>
                    <input name="year" type="hidden"
                        value="{{ reportList.selectedYearInReportSupportPersonListAfterMonths.id }}" />
                    <button type="submit" name="show" class="btn btn-success">Zobrazit</button>
                </form>
            </div>
        </div>

        <div ng-show="reportList.hasShowReportSupplyByStock" class="panel panel-default">
            <div class="panel-body">
                <form class="form-inline" method="post" action="reportSupplyByStock.php" name="reportSupplyByStock">
                    <div class="form-group" ng-init="stockList={stockListData}">
                        <label>10. Inventura stavu zásob podle skladu:</label>
                        <select id="stock" ng-init="initializeStockList({selectedStockId});"
                            ng-model="reportList.selectedStock" ng-options="stock.name for stock in stockList"
                            class="form-control"></select>
                    </div>
                    <div class="form-group"
                        ng-class="{true:'has-error', false:''}[(reportSupplyByStock.dateTo.$error.required || reportSupplyByStock.dateTo.$error.date) && !reportSupplyByStock.dateTo.$pristine]">
                        <label for="reportSupplyByStockDateTo">ke dni:</label>
                        <input type="text" class="form-control" id="reportSupplyByStockDateToDateTo" name="dateTo"
                            placeholder="DD.MM.YYYY" ng-model="report.reportSupplyByStockDateTo" bs-datepicker required>
                        <label class="control-label" for=""
                            ng-show="reportSupplyByStock.dateTo.$error.required && !reportSupplyByStock.dateTo.$pristine">Tento
                            údaj je povinný.</label>
                        <label class="control-label" for=""
                            ng-show="reportSupplyByStock.dateTo.$error.date && !reportSupplyByStock.dateTo.$pristine">Zadejte
                            datum ve formátu DD.MM.YYYY.</label>
                    </div>
                    <input name="stockId" type="hidden" value="{{ reportList.selectedStock.stockId }}" />
                    <input type="hidden" name="settingTheBaselinePeriod" value="1" />
                    <button type="submit" name="show" class="btn btn-success"
                        ng-disabled="!reportSupplyByStock.$valid;">Zobrazit</button>
                </form>
            </div>
        </div>

        <!--
        <div class="panel panel-default" ng-if="rightList.reportList.read">
            <div class="panel-body">
                <form class="form-inline" method="post" action="#" name="reportForMinistryOfHealth">
                    <div class="form-group"
                        ng-class="{true:'has-error', false:''}[(reportForMinistryOfHealth.dateFrom.$error.required || reportForMinistryOfHealth.dateFrom.$error.date) && !reportForMinistryOfHealth.dateFrom.$pristine]">
                        <label for="reportForMinistryOfHealthDateFrom">10. Výkaz pro MZ dle §11 za období
                            od:</label>
                        <input type="text" class="form-control" id="reportForMinistryOfHealthDateFrom" name="dateFrom"
                            placeholder="DD.MM.YYYY" ng-model="report.reportForMinistryOfHealthDateFrom"
                            data-max-date="{{report.reportForMinistryOfHealthDateTo}}" bs-datepicker required>
                        <label class="control-label" for=""
                            ng-show="reportForMinistryOfHealth.dateFrom.$error.required && !reportForMinistryOfHealth.dateFrom.$pristine">Tento
                            údaj je povinný.</label>
                        <label class="control-label" for=""
                            ng-show="reportForMinistryOfHealth.dateFrom.$error.date && !reportForMinistryOfHealth.dateFrom.$pristine">Zadejte
                            datum ve formátu DD.MM.YYYY.</label>
                    </div>
                    <div class="form-group"
                        ng-class="{true:'has-error', false:''}[(reportForMinistryOfHealth.dateTo.$error.required || reportForMinistryOfHealth.dateTo.$error.date) && !reportForMinistryOfHealth.dateTo.$pristine]">
                        <label for="reportForMinistryOfHealthDateTo">do:</label>
                        <input type="text" class="form-control" id="reportForMinistryOfHealthDateTo" name="dateTo"
                            placeholder="DD.MM.YYYY" ng-model="report.reportForMinistryOfHealthDateTo"
                            data-min-date="{{report.reportForMinistryOfHealthDateFrom}}" bs-datepicker required>
                        <label class="control-label" for=""
                            ng-show="reportForMinistryOfHealth.dateTo.$error.required && !reportForMinistryOfHealth.dateTo.$pristine">Tento
                            údaj je povinný.</label>
                        <label class="control-label" for=""
                            ng-show="reportForMinistryOfHealth.dateTo.$error.date && !reportForMinistryOfHealth.dateTo.$pristine">Zadejte
                            datum ve formátu DD.MM.YYYY.</label>
                    </div>
                    <div class="form-group"
                        ng-class="{true:'has-error', false:''}[(reportForMinistryOfHealth.percentReduction.$error.required || reportForMinistryOfHealth.percentReduction.$error.ngMin || reportForMinistryOfHealth.percentReduction.$error.ngMax || reportForMinistryOfHealth.percentReduction.$error.pattern) && !reportForMinistryOfHealth.percentReduction.$pristine]">
                        <label for=" percentReduction"> snížit o: </label>
                        <input ng-init="report.reportCommodityPercentReduction=0" type="text" class="form-control"
                            id="percentReduction" name="percentReduction" size="3"
                            ng-model="report.reportCommodityPercentReduction" ng-pattern="regNumber" ng-min="0"
                            ng-max="100" required>
                        <label> %</label>
                        <label class="control-label" for=""
                            ng-show="reportForMinistryOfHealth.percentReduction.$error.required && !reportForMinistryOfHealth.percentReduction.$pristine">Tento
                            údaj je povinný.</label>
                        <label class="control-label" for=""
                            ng-show="(reportForMinistryOfHealth.percentReduction.$error.ngMin || reportForMinistryOfHealth.percentReduction.$error.ngMax) && !reportForMinistryOfHealth.percentReduction.$pristine">Hodnota
                            musí být v rozsahu 0 - 100.</label>
                        <label class="control-label" for=""
                            ng-show="reportForMinistryOfHealth.percentReduction.$error.pattern">
                            Prosím, zadejte kladné číslo.</label>
                    </div>
                    <a class="btn btn-success"
                        href="reportForMinistryOfHealth.php?dateFrom={{ report.reportForMinistryOfHealthDateFrom }}&amp;dateTo={{ report.reportForMinistryOfHealthDateTo }}&amp;percentReduction={{ report.reportCommodityPercentReduction }}"
                        role="button" title="Export do XLSX" ng-disabled="!reportForMinistryOfHealth.$valid;">Export
                        výkazu do xlsx</a>

                </form>
            </div>
        </div>
-->




        <hr>
        <footer>
            <p>{copyright}</p>
        </footer>
    </div> <!-- /container -->
</body>

</html>