<?php

use form\StockManager;

header("Content-Type: text/html; charset=utf-8");

require_once '../../conf/commonApp.php';

doInit();
core\AuthenticationManager::verifyTheAuthenticity($_authenticationCode, $_loggedAppUserId);
$authorizationManager = new core\AuthorizationManager($_loggedAppUser);

if (
    !$authorizationManager->hasRightToPage(core\RightName::reportList) and
    !$authorizationManager->hasRightToPage(core\RightName::companyReportList)
) {
    core\HttpUtils::redirectAndExit();
}
$rights = $authorizationManager->getRights();

$stockManager = form\StockManager::createForFoodBank($_loggedAppUser->foodBankId);
$stockListData = $stockManager->getStockList();

$yearListData = date\DateUi::buildYearList(START_YEAR, END_YEAR);

$t = new ui\Template("./lang/cs");
$t->set_file("tmt", "reportList.tmt");

$reportListData = array(
    'selectedYearInReportSupportPersonListAfterMonths' => '',
    'hasShowReportSupplyByStock' => $_loggedAppUser->userType != core\AppUserRoleName::FEDERACE and
        $_loggedAppUser->userType != core\AppUserRoleName::COMPANY_REPORTER ? true : false
);

$t->set_var(array(
    'reportListData' => htmlspecialchars(json_encode($reportListData)),
    'rightListData' => htmlspecialchars(json_encode($rights)),
    'yearListData' => htmlspecialchars(json_encode($yearListData)),
    'initYear' => date\HpCalendar::getActualYear(),
    'stockListData' => htmlspecialchars(json_encode($stockListData)),
    'selectedStockId' => form\StockManager::summaryItemStockId
));
$t->set_global_var_to_template();
$t->pparse("out", "tmt");
