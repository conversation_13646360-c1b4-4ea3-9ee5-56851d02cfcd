<?php
// sumární report komodit (množství a hodnota v penězích) za měsíc nebo za období
header("Content-Type: text/html; charset=utf-8");

require_once '../../conf/commonApp.php';

doInit();
core\AuthenticationManager::verifyTheAuthenticity($_authenticationCode, $_loggedAppUserId);
$authorizationManager = new core\AuthorizationManager($_loggedAppUser);
if (!$authorizationManager->hasRightToPage(core\RightName::reportList)) {
    core\HttpUtils::redirectAndExit();
}
$rights = $authorizationManager->getRights();
$hasAccessToAllPbanks = $authorizationManager->hasAccessToAllPbanks();
$t = new ui\Template("./lang/cs");
$t->set_file("tmt", "reportCommoditiesRemovedFromStorage.tmt");

if ($_GET['resetFilter'] == 1) {
    $dateFrom = date('Y-m-d', strtotime($_GET['defaultDateFrom']));
    $dateTo = date('Y-m-d', strtotime($_GET['defaultDateTo']));
} else {
    $dateFrom = $_POST['dateFrom'] ? date('Y-m-d', strtotime($_POST['dateFrom'])) : date('Y-m-d', strtotime($_GET['dateFrom']));
    $dateTo = $_POST['dateTo'] ? date('Y-m-d', strtotime($_POST['dateTo'])) : date('Y-m-d', strtotime($_GET['dateTo']));
}

$filterFoodBankId = !empty($_POST['filterFoodBankId']) ? $_POST['filterFoodBankId'] : 0;
$foodBankList = array();
if ($hasAccessToAllPbanks) {
    $foodBankManager = party\FoodBankManager::createForFoodBank($_loggedAppUser->foodBankId);
    $foodBankList = $foodBankManager->getFoodBankList();
}

$actionSearchString = $_POST['actionSearchString'];
$customerNameSearchString = $_POST['customerNameSearchString'];
$customerGroupNameSearchString = $_POST['customerGroupNameSearchString'];
$commoditySearchString = $_POST['commoditySearchString'];

$reportManager = report\ReportManager::createForFoodBank($_loggedAppUser->foodBankId);

if ($authorizationManager->isCompanyReporter()) {
    $customerManager = new \party\CustomerManager();
    $customerGroupNameSearchString = $customerManager->getCustomerGroupName($_loggedAppUser->customerId);
    $reportManager->setExactCustomerGroupFilter($customerGroupNameSearchString);
} else {
    $reportManager->setCustomerGroupFilter($customerGroupNameSearchString);
}

$reportManager->setActionFilter($actionSearchString);
$reportManager->setCustomerFilter($customerNameSearchString);
$reportManager->setCommodityFilter($commoditySearchString);
$reportManager->setFoodBankIdFilter($filterFoodBankId);
$commoditySummary = $reportManager->getCommoditiesRemovedFromStoreSummaryForPeriod($dateFrom, $dateTo, $hasAccessToAllPbanks);

if ($_GET['export'] == 1) {
    $includeValue = $_GET['includeValue'];
    $separator = report\ReportManager::CSV_COLUMN_SEPARATOR;

    $csvContent = "Vyskladněné komodity za období od " . date('j.n.Y', strtotime($dateFrom)) . " do " . date('j.n.Y', strtotime($dateTo)) . "\n\n";
    $csvContent .= "Číslo komodity" . $separator . "Název komodity" . $separator . "Množství (kg)" . ($includeValue ? $separator . "Hodnota v (" . localization\LocalizationProvider::getCurrencySymbol() . ")" : "") . "\n\n";

    foreach ($commoditySummary['commodities'] as $commodity) {
        $csvContent .= $commodity['code'] . $separator . $commodity['name'] . $separator . core\HpNumberFormatter::toAmount($commodity['amount']) .
            ($includeValue ? $separator . core\HpNumberFormatter::toMoney($supplier->value) : '') . "\n";
    }

    $csvContent .= $separator . "Celkem" . $separator . core\HpNumberFormatter::toAmount($commoditySummary['totalAmount']) .
        ($includeValue ? $separator . core\HpNumberFormatter::toMoney($commoditiesBySupplierGroup['totalValue']) : "") . "\n\n";
    $csvFilename = report\ReportManager::getCsvFileName("Vyskladnene_komodity", $dateFrom, $dateTo);
    report\ReportManager::sendCsvReport($csvContent, $csvFilename);
    exit;
}

if ($_POST['settingTheBaselinePeriod'] == 1) {
    $t->set_var(array('defaultDateFrom' => $dateFrom, 'defaultDateTo' => $dateTo));
} else {
    $t->set_var(array('defaultDateFrom' => $_REQUEST['defaultDateFrom'], 'defaultDateTo' => $_REQUEST['defaultDateTo']));
}

$reportData = array(
    'dateFrom' => $dateFrom,
    'dateTo' => $dateTo,
    'filterDateFrom' => $dateFrom,
    'filterDateTo' => $dateTo,
    'actionSearchString' => $actionSearchString,
    'customerNameSearchString' => $customerNameSearchString,
    'customerGroupNameSearchString' => $customerGroupNameSearchString,
    'supplierSearchString' => $supplierSearchString,
    'supplierGroupNameSearchString' => $supplierGroupNameSearchString,
    'commoditySearchString' => $commoditySearchString,
    'showEmptyReport' => empty($commoditySummary) ? true : false
);

$t->set_var(array(
    'commoditySummary' => htmlspecialchars(json_encode($commoditySummary)),
    'reportData' => htmlspecialchars(json_encode($reportData)),
    'sqlDateFrom' => $dateFrom,
    'sqlDateTo' => $dateTo,
    'rightListData' => htmlspecialchars(json_encode($rights)),
    'foodBankListData' => htmlspecialchars(json_encode($foodBankList)),
    'filterFoodBankId' => $filterFoodBankId
));

$t->set_global_var_to_template();
$t->pparse("out", "tmt");
