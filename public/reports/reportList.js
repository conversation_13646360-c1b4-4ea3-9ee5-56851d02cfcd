angular.module('PBanka', ['mgcrea.ngStrap', 'hpBanka', 'hpBankaMenu', 'numberLimits'])

    .controller('ReportListController', function ($scope) {

        $scope.regNumber = /^(0|[1-9][0-9]*)((\.[0-9]*)|(\,[0-9]*))?$/; 

        $scope.initializeYearList = function (selectedYear) {

            for (var i = 0; i < $scope.yearList.length; i++) {
                if ($scope.yearList[i].id == selectedYear) {
                    $scope.reportList.selectedYearInReportSupportPersonListAfterMonths = $scope.yearList[i];
                }
            }
        };


        $scope.initializeStockList = function (selectedStockId) {

            for (var i = 0; i < $scope.stockList.length; i++) {
                if ($scope.stockList[i].stockId == selectedStockId) {
                    $scope.reportList.selectedStock = $scope.stockList[i];
                }
            }
        }


    });
