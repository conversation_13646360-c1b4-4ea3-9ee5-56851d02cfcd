<?php
// sumární report komodit (množství a hodnota v penězích) za odběratele za měsíc nebo za období

header("Content-Type: text/html; charset=utf-8");

require_once '../../conf/commonApp.php';

doInit();
core\AuthenticationManager::verifyTheAuthenticity($_authenticationCode, $_loggedAppUserId);
$authorizationManager = new core\AuthorizationManager($_loggedAppUser);

if (
    !$authorizationManager->hasRightToPage(core\RightName::reportList) &&
    !$authorizationManager->hasRightToPage(core\RightName::companyReportList)
) {
    core\HttpUtils::redirectAndExit();
}

$rights = $authorizationManager->getRights();
$hasAccessToAllPbanks = $authorizationManager->hasAccessToAllPbanks();

$t = new ui\Template("./lang/cs");
$t->set_file("tmt", "reportCommodityByCustomer.tmt");

if ($_GET['resetFilter'] == 1) {
    $dateFrom = date('Y-m-d', strtotime($_GET['defaultDateFrom']));
    $dateTo = date('Y-m-d', strtotime($_GET['defaultDateTo']));
} else {
    $dateFrom = $_POST['dateFrom'] ? date('Y-m-d', strtotime($_POST['dateFrom'])) : date('Y-m-d', strtotime($_GET['dateFrom']));
    $dateTo = $_POST['dateTo'] ? date('Y-m-d', strtotime($_POST['dateTo'])) : date('Y-m-d', strtotime($_GET['dateTo']));
}

$filterFoodBankId = !empty($_POST['filterFoodBankId']) ? $_POST['filterFoodBankId'] : 0;
$foodBankList = array();

if ($hasAccessToAllPbanks) {
    $foodBankManager = party\FoodBankManager::createForFoodBank($_loggedAppUser->foodBankId);
    $foodBankList = $foodBankManager->getFoodBankList();
}

$actionSearchString = $_POST['actionSearchString'];
$customerNameSearchString = $_POST['customerNameSearchString'];
$customerGroupNameSearchString = $_POST['customerGroupNameSearchString'];
$commoditySearchString = $_POST['commoditySearchString'];

$reportManager = report\ReportManager::createForFoodBank($_loggedAppUser->foodBankId);

if ($authorizationManager->isCompanyReporter()) {
    $customerManager = new \party\CustomerManager();
    $customerGroupNameSearchString = $customerManager->getCustomerGroupName($_loggedAppUser->customerId);
    $reportManager->setExactCustomerGroupFilter($customerGroupNameSearchString);
} else {
    $reportManager->setCustomerGroupFilter($customerGroupNameSearchString);
}

$reportManager->setActionFilter($actionSearchString);
$reportManager->setCustomerFilter($customerNameSearchString);
$reportManager->setCommodityFilter($commoditySearchString);
$reportManager->setFoodBankIdFilter($filterFoodBankId);
$commoditySummaryByCustomer = $reportManager->getCommoditySummaryByCustomerForPeriod($dateFrom, $dateTo, $hasAccessToAllPbanks);

if ($_GET['export'] == 1) {
    $separator = report\ReportManager::CSV_COLUMN_SEPARATOR;

    $csvContent = "Vyskladněné komodity podle odběratelů za období od " . date('j.n.Y', strtotime($dateFrom)) . " do " . date('j.n.Y', strtotime($dateTo)) . "\n\n";
    $csvContent .= "Odběratel" . $separator . "Číslo komodity" . $separator . "Název komodity" . $separator . "Množství (kg)" . $separator . "Hodnota v (" . localization\LocalizationProvider::getCurrencySymbol() . ")\n\n";

    foreach ($commoditySummaryByCustomer['customers'] as $customerName => $commodityByCustomer) {
        $csvContent .= $customerName . "\n";
        foreach ($commodityByCustomer['commodities'] as $commodity) {
            $csvContent .= "" . $separator . $commodity->code . $separator . $commodity->commodityName . $separator . core\HpNumberFormatter::toAmount($commodity->amount) . $separator . core\HpNumberFormatter::toMoney($commodity->value) . "\n";
        }
    }

    $csvContent .= "\nVšichni odběratelé odebrali celkem " . core\HpNumberFormatter::toAmount($commoditySummaryByCustomer['totalAllCustomers']['amount']) . " kg komodit v hodnotě " . core\HpNumberFormatter::toMoney($commoditySummaryByCustomer['totalAllCustomers']['value']) . " " . localization\LocalizationProvider::getCurrencySymbol() . ".\n\n";
    $csvFilename = report\ReportManager::getCsvFileName("Vyskladnene_komodity_podle_odberatelu", $dateFrom, $dateTo);
    report\ReportManager::sendCsvReport($csvContent, $csvFilename);
    exit;
}

if ($_POST['settingTheBaselinePeriod'] == 1) {
    $t->set_var(array('defaultDateFrom' => $dateFrom, 'defaultDateTo' => $dateTo));
} else {
    $t->set_var(array('defaultDateFrom' => $_REQUEST['defaultDateFrom'], 'defaultDateTo' => $_REQUEST['defaultDateTo']));
}

$reportData = array(
    'dateFrom' => date('j.n.Y', strtotime($dateFrom)),
    'dateTo' => date('j.n.Y', strtotime($dateTo)),
    'filterDateFrom' => $dateFrom,
    'filterDateTo' => $dateTo,
    'actionSearchString' =>  $actionSearchString,
    'customerNameSearchString' =>  $customerNameSearchString,
    'customerGroupNameSearchString' =>  $customerGroupNameSearchString,
    'commoditySearchString' =>  $commoditySearchString,
    'showEmptyReport' => empty($commoditySummaryByCustomer['customers']) ? true : false
);

$t->set_var(array(
    'commoditySummaryByCustomer' => htmlspecialchars(json_encode($commoditySummaryByCustomer['customers'])),
    'totalAllCustomers' => htmlspecialchars(json_encode($commoditySummaryByCustomer['totalAllCustomers'])),
    'reportData' => htmlspecialchars(json_encode($reportData)),
    'sqlDateFrom' => $dateFrom,
    'sqlDateTo' => $dateTo,
    'rightListData' => htmlspecialchars(json_encode($rights)),
    'foodBankListData' => htmlspecialchars(json_encode($foodBankList)),
    'filterFoodBankId' => $filterFoodBankId
));

$t->set_global_var_to_template();
$t->pparse("out", "tmt");
