angular.module('PBanka', ['mgcrea.ngStrap', 'hpBanka', 'hpBankaMenu'])

.controller('ReportCommodityBySupplierController', function($scope) {

	$scope.includeValue = true;

	$scope.inicializeFoodBank = function(selectedFoodBankId) {
		$scope.foodBankList.push({foodBankId: 0, name: 'Potravinové banky celkem'});
		for (var i=0; i<$scope.foodBankList.length; i++) {
			if ($scope.foodBankList[i].foodBankId == selectedFoodBankId) {
				$scope.currentFoodBank = $scope.foodBankList[i];
		    }
		}
	};
	
});
