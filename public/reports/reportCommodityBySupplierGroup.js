angular.module('PBanka', ['mgcrea.ngStrap', 'hpBanka', 'hpBankaMenu'])

	.controller('ReportCommodityBySupplierGroupController', function ($scope) {

		$scope.includeValue = true;
		$scope.groupNameAndCommodityIdWhichAreExcludedExport = [];

		$scope.inicializeFoodBank = function (selectedFoodBankId) {
			$scope.foodBankList.push({foodBankId: 0, name: 'Potravinové banky celkem'});
			for (var i = 0; i < $scope.foodBankList.length; i++) {
				if ($scope.foodBankList[i].foodBankId == selectedFoodBankId) {
					$scope.currentFoodBank = $scope.foodBankList[i];
				}
			}
		};


		$scope.totalAllGroupsSum = function () {
			$scope.totalAllGroups.amount = 0;
			$scope.totalAllGroups.value = 0;

			angular.forEach($scope.commoditySummaryBySupplierGroup, function (supplierGroupData) {
				angular.forEach(supplierGroupData.commodities, function (commodity) {
					if (commodity.print) {
						$scope.totalAllGroups.amount += commodity.amount;
						$scope.totalAllGroups.value += commodity.value;
					}
				});
			});
		}

		$scope.commoditySum = function (supplierGroupData) {
			supplierGroupData.totalAmount = 0;
			supplierGroupData.totalValue = 0;
			angular.forEach(supplierGroupData.commodities, function (commodity) {
				if (commodity.print) {
					supplierGroupData.totalAmount += commodity.amount;
					supplierGroupData.totalValue += commodity.value;
				}
			});
			$scope.totalAllGroupsSum();
		}

		$scope.changeIncludedInTheReport = function (commodity, supplierGroupData) {
			if ($scope.rightList.settingReports.fullAccess) {
				commodity.print = commodity.print ? false : true
				$scope.commoditySum(supplierGroupData);
				$scope.setExcludedIdsForExport();
			}
		}

		$scope.setExcludedIdsForExport = function () {
			$scope.groupNameAndCommodityIdWhichAreExcludedExport = [];
			angular.forEach($scope.commoditySummaryBySupplierGroup, function (supplierGroupData) {
				angular.forEach(supplierGroupData.commodities, function (commodity) {
					if (!commodity.print) {
						$scope.groupNameAndCommodityIdWhichAreExcludedExport.push(commodity.excludedIdForExport);
					}
				});
			});
		}
	});
