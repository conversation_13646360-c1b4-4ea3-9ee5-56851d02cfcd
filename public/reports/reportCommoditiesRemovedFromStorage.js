angular.module('PBanka', ['mgcrea.ngStrap', 'hpBanka', 'hpBankaMenu'])

	.controller('ReportCommoditiesRemovedFromStorageController', function ($scope, $window) {

		$scope.commoditySum = function () {
			$scope.commoditySummary.totalAmount = 0;
			$scope.commoditySummary.totalValue = 0;
			angular.forEach($scope.commoditySummary.commodities, function (commodity) {
				if (commodity.print) {
					$scope.commoditySummary.totalAmount += commodity.amount;
					$scope.commoditySummary.totalValue += commodity.value;
				}
			});
		}

		$scope.inicializeFoodBank = function (selectedFoodBankId) {
			$scope.foodBankList.push({foodBankId: 0, name: 'Potravinové banky celkem'});
			for (var i = 0; i < $scope.foodBankList.length; i++) {
				if ($scope.foodBankList[i].foodBankId == selectedFoodBankId) {
					$scope.currentFoodBank = $scope.foodBankList[i];
				}
			}
		};

		$scope.changeIncludedInTheReport = function (commodity) {
			if ($scope.rightList.settingReports.fullAccess) {
				commodity.print = commodity.print ? false : true
				$scope.commoditySum();
			}
		}
	});
