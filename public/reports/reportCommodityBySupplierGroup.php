<?php
// sumární report komodit (množství a hodnota v penězích) podle skupin dodavatelů (podle řetězců) za měsíc nebo za období

header("Content-Type: text/html; charset=utf-8");

require_once '../../conf/commonApp.php';

doInit();
core\AuthenticationManager::verifyTheAuthenticity($_authenticationCode, $_loggedAppUserId);
$authorizationManager = new core\AuthorizationManager($_loggedAppUser);
if (!$authorizationManager->hasRightToPage(core\RightName::reportList)) {core\HttpUtils::redirectAndExit();}
$rights = $authorizationManager->getRights();
$hasAccessToAllPbanks = $authorizationManager->hasAccessToAllPbanks();

$t = new ui\Template("./lang/cs");
$t->set_file("tmt", "reportCommodityBySupplierGroup.tmt");

if ($_GET['resetFilter'] == 1) {
    $dateFrom = date('Y-m-d', strtotime($_GET['defaultDateFrom']));
    $dateTo = date('Y-m-d', strtotime($_GET['defaultDateTo']));
    $percentReduction = intval($_GET['percentReduction']);
} else {
    $dateFrom = $_POST['dateFrom'] ? date('Y-m-d', strtotime($_POST['dateFrom'])) : date('Y-m-d', strtotime($_GET['dateFrom']));
    $dateTo = $_POST['dateTo'] ? date('Y-m-d', strtotime($_POST['dateTo'])) : date('Y-m-d', strtotime($_GET['dateTo']));
    $percentReduction = $_POST['percentReduction'] ? intval($_POST['percentReduction']) : intval($_GET['percentReduction']);
}

$filterFoodBankId = !empty($_POST['filterFoodBankId']) ? $_POST['filterFoodBankId'] : 0;
$foodBankList = array();
if ($hasAccessToAllPbanks) {
    $foodBankManager = party\FoodBankManager::createForFoodBank($_loggedAppUser->foodBankId);
    $foodBankList = $foodBankManager->getFoodBankList();
}

$actionSearchString = $_POST['actionSearchString'];
$supplierNameSearchString = $_POST['supplierNameSearchString'];
$supplierGroupSearchString = $_POST['supplierGroupSearchString'];
$commoditySearchString = $_POST['commoditySearchString'];

$reportManager = report\ReportManager::createForFoodBank($_loggedAppUser->foodBankId);
$reportManager->setActionFilter($actionSearchString);
$reportManager->setSupplierFilter($supplierNameSearchString);
$reportManager->setSupplierGroupFilter($supplierGroupSearchString);
$reportManager->setCommodityFilter($commoditySearchString);
$reportManager->setFoodBankIdFilter($filterFoodBankId);
$reportManager->setPercentReduction($percentReduction);

$groupNameAndCommodityIdWhichAreExcludedExport = array();
if ($_GET['export'] == 1) {
    $groupNameAndCommodityIdWhichAreExcludedExport = json_decode($_GET['groupNameAndCommodityIdWhichAreExcludedExport']);
}

$commoditySummaryBySupplierGroup = $reportManager->getCommoditySummaryBySupplierGroupForPeriod($dateFrom, $dateTo, $hasAccessToAllPbanks, $groupNameAndCommodityIdWhichAreExcludedExport);

if ($_GET['export'] == 1) {
    $includeValue = $_GET['includeValue'];

    $separator = report\ReportManager::CSV_COLUMN_SEPARATOR;

    $csvContent = "Naskladněné komodity podle organizace dodavatele (řetězce) za období od " . date('j.n.Y', strtotime($dateFrom)) . " do " . date('j.n.Y', strtotime($dateTo)) . "\n\n";
    $csvContent .= "Organizace dodavatele (řetězec)" . $separator . "Číslo komodity" . $separator . "Název komodity" . $separator . "Množství (kg)" . ($includeValue ? $separator . "Hodnota v (" . localization\LocalizationProvider::getCurrencySymbol() . ")" : "") . "\n\n";

    foreach ($commoditySummaryBySupplierGroup['groups'] as $supplierGroup => $commoditiesBySupplierGroup) {
        $csvContent .= $supplierGroup . "\n";
        foreach ($commoditiesBySupplierGroup['commodities'] as $commodity) {
            $csvContent .= "" . $separator . $commodity->code . $separator . $commodity->commodityName . $separator . core\HpNumberFormatter::toAmount($commodity->amount) .
                ($includeValue ? $separator . core\HpNumberFormatter::toMoney($commodity->value) : '') . "\n";
        }
        $csvContent .= "" . $separator . $separator . "Celkem" . $separator . core\HpNumberFormatter::toAmount($commoditiesBySupplierGroup['totalAmount']) .
            ($includeValue ? $separator . core\HpNumberFormatter::toMoney($commoditiesBySupplierGroup['totalValue']) : "") . "\n\n";
    }
    $csvContent .= "Všechny organizace dodaly celkem " . core\HpNumberFormatter::toAmount($commoditySummaryBySupplierGroup['totalAllGroups']['amount']) . " kg komodit" .
        ($includeValue ? " v hodnotě " . core\HpNumberFormatter::toMoney($commoditySummaryBySupplierGroup['totalAllGroups']['value']) . " " . localization\LocalizationProvider::getCurrencySymbol() : ".") . "\n\n";

    if ($percentReduction > 0) {
        $csvContent .= "Pro stanovení čisté hmotnosti potravin použita srážka " . $percentReduction . "% z celkové hmotnosti.\n\n";
    }
    $csvFilename = report\ReportManager::getCsvFileName("Naskladnene_komodity_podle_organizace_dodavatele", $dateFrom, $dateTo);
    report\ReportManager::sendCsvReport($csvContent, $csvFilename);
    exit;
}

if ($_POST['settingTheBaselinePeriod'] == 1) {
    $t->set_var(array('defaultDateFrom' => $dateFrom, 'defaultDateTo' => $dateTo));
} else {
    $t->set_var(array('defaultDateFrom' => $_REQUEST['defaultDateFrom'], 'defaultDateTo' => $_REQUEST['defaultDateTo']));
}

$reportData = array(
    'dateFrom' => date('j.n.Y', strtotime($dateFrom)),
    'dateTo' => date('j.n.Y', strtotime($dateTo)),
    'filterDateFrom' => $dateFrom,
    'filterDateTo' => $dateTo,
    'actionSearchString' => $actionSearchString,
    'supplierNameSearchString' => $supplierNameSearchString,
    'supplierGroupSearchString' => $supplierGroupSearchString,
    'commoditySearchString' => $commoditySearchString,
    'showEmptyReport' => empty($commoditySummaryBySupplierGroup['groups']) ? true : false,
    'percentReduction' => $percentReduction
);

$t->set_var(array(
    'commoditySummaryBySupplierGroup' => htmlspecialchars(json_encode($commoditySummaryBySupplierGroup['groups'])),
    'totalAllGroups' => htmlspecialchars(json_encode($commoditySummaryBySupplierGroup['totalAllGroups'])),
    'reportData' => htmlspecialchars(json_encode($reportData)),
    'sqlDateFrom' => $dateFrom,
    'sqlDateTo' => $dateTo,
    'rightListData' => htmlspecialchars(json_encode($rights)),
    'foodBankListData' => htmlspecialchars(json_encode($foodBankList)),
    'filterFoodBankId' => $filterFoodBankId
));

$t->set_global_var_to_template();
$t->pparse("out", "tmt");
