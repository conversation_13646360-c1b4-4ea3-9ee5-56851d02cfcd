<?php
header("Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
header("Content-Disposition: attachment; filename=export.xlsx");
header("Cache-Control: max-age=0");

require_once '../../conf/commonApp.php';

doInit();
core\AuthenticationManager::verifyTheAuthenticity($_authenticationCode, $_loggedAppUserId);
$authorizationManager = new core\AuthorizationManager($_loggedAppUser);
if (!$authorizationManager->hasRightToPage(core\RightName::reportList)) {
    core\HttpUtils::redirectAndExit();
}
$rights = $authorizationManager->getRights();

$postData = file_get_contents('php://input');
$data = json_decode($postData, true);

$dateFrom = date('Y-m-d', strtotime($data['dateFrom']));
$dateTo = date('Y-m-d', strtotime($data['dateTo']));

$reportManager = report\ReportManager::createForFoodBank($_loggedAppUser->foodBankId);

$commodityList = $data['commodityList'];

$xlsxExportCommodityIdAndPercentReductionList = array();
$xlsxExportCommodityIdList = array();

foreach ($commodityList as $commodity) {
    if ($commodity['print']) {
        $commodityId = intval($commodity['commodityId']);
        $percentReduction = intval($commodity['percentReduction']);
        $xlsxExportCommodityIdAndPercentReductionList[$commodityId] = $percentReduction;
        $xlsxExportCommodityIdList[$commodityId] = $commodityId;
    }
}

$excelData = $reportManager->getStatementCommodityWithSupplierForMinistryOfHealth($dateFrom, $dateTo, $xlsxExportCommodityIdAndPercentReductionList, $xlsxExportCommodityIdList);
$xlsxFilename = report\ReportManager::getXlsxFileName("MZ_tabulka_vykazu", $dateFrom, $dateTo);
$xlsx = party\PhpXlsxGenerator::fromArray($excelData);
$xlsx->downloadAs($xlsxFilename);
exit;
