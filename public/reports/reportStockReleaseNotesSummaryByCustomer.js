angular.module('PBanka', ['mgcrea.ngStrap', 'hpBanka', 'hpBankaMenu'])

.controller('StockReleaseNotesSummaryByCustommerController', function($scope) {
	
	Array.prototype.sum = function (propertyName) {
	    var total = 0.0;
	    for (var i = 0; i < this.length; i++) {
	        total += this[i][propertyName];
	    }
	    return total;
	}
	
	$scope.inicializeFoodBank = function(selectedFoodBankId) {
		$scope.foodBankList.push({foodBankId: 0, name: 'Potravinové banky celkem'});
		for (var i=0; i<$scope.foodBankList.length; i++) {
			if ($scope.foodBankList[i].foodBankId == selectedFoodBankId) {
				$scope.currentFoodBank = $scope.foodBankList[i];
		    }
		}
	};
});


