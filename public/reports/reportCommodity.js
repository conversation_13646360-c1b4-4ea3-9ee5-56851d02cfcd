angular.module('PBanka', ['mgcrea.ngStrap', 'hpBanka', 'hpBankaMenu'])

	.controller('ReportCommodityController', ['$scope', '$window', '$http', function ($scope, $window, $http) {

		$scope.commodityIsExcludedExport = [];


		$scope.commoditySum = function () {
			$scope.commoditySummary.totalAmount = 0;
			$scope.commoditySummary.totalReducedAmount = 0;
			$scope.commoditySummary.totalValue = 0;
			angular.forEach($scope.commoditySummary.commodities, function (commodity) {
				if (commodity.print) {
					$scope.commoditySummary.totalAmount += commodity.amount;
					$scope.commoditySummary.totalReducedAmount += commodity.reducedAmount;
					$scope.commoditySummary.totalValue += commodity.value;
				}
			});
		}


		$scope.inicializeFoodBank = function (selectedFoodBankId) {
			$scope.foodBankList.push({foodBankId: 0, name: 'Potravinové banky celkem'});
			for (var i = 0; i < $scope.foodBankList.length; i++) {
				if ($scope.foodBankList[i].foodBankId == selectedFoodBankId) {
					$scope.currentFoodBank = $scope.foodBankList[i];
				}
			}
		};

		$scope.changeIncludedInTheReport = function (commodity) {
			if ($scope.rightList.settingReports.fullAccess) {
				if (commodity.print) {
					commodity.print = false;
					$scope.commodityIsExcludedExport.push(parseInt(commodity.commodityId));
				} else {
					commodity.print = true;
					angular.forEach($scope.commodityIsExcludedExport, function (commodityId) {
						if (commodityId == parseInt(commodity.commodityId)) {
							$scope.commodityIsExcludedExport.splice($scope.commodityIsExcludedExport.indexOf(commodityId), 1);
						}
					});
				}
				$scope.commoditySum();
			}
		}

		$scope.changePercentReductionByCommodity = function (commodity) {

			if ($scope.rightList.settingReports.fullAccess) {
				let percentReduction = parseFloat(commodity.percentReduction);
				if (isNumber(percentReduction)) {
					if (percentReduction > 100) {
						commodity.reducedAmount = 0
					} else {
						commodity.reducedAmount = commodity.amount * (100 - commodity.percentReduction) / 100;
					}
				} else {
					commodity.reducedAmount = commodity.amount
				}
				$scope.commoditySum();
			}
		}
		function isNumber(value) {
			return typeof value === 'number' && !isNaN(value);
		}

		$scope.checkIfValueIsNumber = function (commodity) {
			commodity.commodityValueIsNotNumber = !isPositiveNumberOrZero(parseFloat(commodity.percentReduction));
			$scope.canExport();
			return commodity.commodityValueIsNotNumber;
		}

		function isPositiveNumberOrZero(value) {
			return typeof value === 'number' && value >= 0;
		}
		$scope.checkIfThereIsNoEmpty = function (commodity) {

			commodity.isEmpty = !isPercentReductionDefinedAndNotEmpty(commodity.percentReduction);
			$scope.canExport();
			return commodity.isEmpty;
		}
		function isPercentReductionDefinedAndNotEmpty(percentReduction) {
			return typeof percentReduction !== 'undefined' && percentReduction !== null && percentReduction !== '';
		}

		$scope.checkValueIsInRange = function (commodity) {

			let percentReduction = parseFloat(commodity.percentReduction);
			if (isNumber(percentReduction)) {
				commodity.commodityValueIsNotRange = !isPercentReductionValid(percentReduction);
			}
			$scope.canExport();
			return commodity.commodityValueIsNotRange;
		}
		function isPercentReductionValid(percentReduction) {
			return percentReduction >= 0 && percentReduction <= 100;
		}

		$scope.canExport = function () {
			let canExport = true;
			let hasValidCommodity = false;
			if ($scope.commoditySummary && $scope.commoditySummary.commodities) {
				$scope.commoditySummary.commodities.forEach(function (commodity) {
					if (commodity && commodity.isEmpty !== undefined) {
						canExport = canExport && !commodity.isEmpty && !commodity.commodityValueIsNotRange && !commodity.commodityValueIsNotNumber;
						hasValidCommodity = true;
					} else {
						canExport = false;
					}
				});
			} else {
				canExport = false;
			}

			if (!hasValidCommodity) {
				canExport = false;
			}
			return canExport;
		}


		$scope.exportToCSV = function () {
			if (!$scope.canExport()) {
				return;
			}

			const postData = {
				includeReducedAmount: $scope.report.includeReducedAmount,
				includeValue: $scope.report.includeValue,
				dateFrom: $scope.report.dateFrom,
				dateTo: $scope.report.dateTo,
				withoutDirectConsumption: $scope.report.withoutDirectConsumption,
				commodityIsExcludedExport: $scope.commodityIsExcludedExport,
				commodityList: $scope.commoditySummary.commodities
			};

			const filename = getCSVFileName("Naskladnene_komodity", postData.dateFrom, postData.dateTo);

			$http.post('getReportCommodityInCSV.php', postData, {responseType: 'blob'})
				.then(function (response) {
					downloadFile(response.data, filename, 'text/csv');
				})
				.catch(function (error) {
					console.error('Chyba při odesílání dat.', error);
				});
		};



		$scope.getReportDataInXlsx = function () {
			if (!$scope.canExport()) {
				return;
			}

			const postData = {
				dateFrom: $scope.report.filterDateFrom,
				dateTo: $scope.report.filterDateTo,
				commodityList: $scope.commoditySummary.commodities
			};

			const filename = getXlsxFileName("MZ_tabulka_vykazu", postData.dateFrom, postData.dateTo);

			$http.post('reportForMinistryOfHealth.php', postData, {responseType: 'arraybuffer'})
				.then(response => downloadFile(response.data, filename, 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'))
				.catch(error => console.error('Chyba při odesílání dat.', error));
		};

		function getCSVFileName(reportName, since, to) {
			let formattedDate = getFormattedDate();
			return `PB_${reportName}_${since}_${to}_${formattedDate}.csv`;
		}

		function getXlsxFileName(reportName, since, to) {
			let formattedDate = getFormattedDate();
			return `PB_${reportName}_${since}_${to}_${formattedDate}.xlsx`;
		}

		function getFormattedDate() {
			let date = new Date();
			let formattedDate = date.getFullYear().toString() +
				('0' + (date.getMonth() + 1)).slice(-2) +
				('0' + date.getDate()).slice(-2) +
				('0' + date.getHours()).slice(-2) +
				('0' + date.getMinutes()).slice(-2) +
				('0' + date.getSeconds()).slice(-2);
			return formattedDate;
		}

		function downloadFile(data, filename, type) {
			let blob = new Blob([data], {type: type});
			let downloadUrl = URL.createObjectURL(blob);
			let a = document.createElement('a');
			a.href = downloadUrl;
			a.download = filename;
			document.body.appendChild(a);
			a.click();
			document.body.removeChild(a);
			URL.revokeObjectURL(downloadUrl);
		}
	}]);
