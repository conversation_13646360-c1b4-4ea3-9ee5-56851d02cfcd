<?php
header("Content-Type: text/html; charset=utf-8");

require_once '../../conf/commonApp.php';

doInit();
core\AuthenticationManager::verifyTheAuthenticity($_authenticationCode, $_loggedAppUserId);
$authorizationManager = new core\AuthorizationManager($_loggedAppUser);
if (!$authorizationManager->hasRightToPage(core\RightName::reportList)) {
    core\HttpUtils::redirectAndExit();
}
$rights = $authorizationManager->getRights();
$hasAccessToAllPbanks = $authorizationManager->hasAccessToAllPbanks();

$t = new ui\Template("./lang/cs");
$t->set_file("tmt", "reportCommodityBySupplierGroupList.tmt");

if ($_GET['resetFilter'] == 1) {
    $dateFrom = date('Y-m-d', strtotime($_GET['defaultDateFrom']));
    $dateTo = date('Y-m-d', strtotime($_GET['defaultDateTo']));
} else {
    $dateFrom = $_POST['dateFrom'] ? date('Y-m-d', strtotime($_POST['dateFrom'])) : date('Y-m-d', strtotime($_GET['dateFrom']));
    $dateTo = $_POST['dateTo'] ? date('Y-m-d', strtotime($_POST['dateTo'])) : date('Y-m-d', strtotime($_GET['dateTo']));
}

$filterFoodBankId = !empty($_POST['filterFoodBankId']) ? $_POST['filterFoodBankId'] : 0;
$foodBankList = array();
if ($hasAccessToAllPbanks) {
    $foodBankManager = party\FoodBankManager::createForFoodBank($_loggedAppUser->foodBankId);
    $foodBankList = $foodBankManager->getFoodBankList();
}

$supplierGroupSearchString = $_POST['supplierGroupSearchString'];
$reportManager = report\ReportManager::createForFoodBank($_loggedAppUser->foodBankId);
$reportManager->setSupplierGroupFilter($supplierGroupSearchString);
$reportManager->setFoodBankIdFilter($filterFoodBankId);
$commoditySummaryBySupplierGroupList = $reportManager->getCommoditySummaryBySupplierGroupListForPeriod($dateFrom, $dateTo, $hasAccessToAllPbanks);

if ($_GET['export'] == 1) {
    $separator = report\ReportManager::CSV_COLUMN_SEPARATOR;

    $csvContent = "Naskladněné komodity podle řetězce za období od " . date('j.n.Y', strtotime($dateFrom)) . " do " . date('j.n.Y', strtotime($dateTo)) . "\n\n";
    $csvContent .= "Název řetězece" . $separator . "Množství (kg)" . $separator . "Hodnota v (" . localization\LocalizationProvider::getCurrencySymbol() . ")\n\n";

    foreach ($commoditySummaryBySupplierGroupList['supplierGroupList'] as $supplierGroupData) {
        $csvContent .= $supplierGroupData['name'] . $separator . core\HpNumberFormatter::toAmount($supplierGroupData['amount']) . $separator . core\HpNumberFormatter::toMoney($supplierGroupData['value']) . "\n";
    }

    $csvContent .= "Celkem " . $separator . core\HpNumberFormatter::toAmount($commoditySummaryBySupplierGroupList['totalAllGroups']['amount']) . $separator . core\HpNumberFormatter::toMoney($commoditySummaryBySupplierGroupList['totalAllGroups']['value']) . "\n\n";
    $csvFilename = report\ReportManager::getCsvFileName("Naskladnene_komodity_podle_retezce", $dateFrom, $dateTo);
    report\ReportManager::sendCsvReport($csvContent, $csvFilename);
    exit;
}

if ($_POST['settingTheBaselinePeriod'] == 1) {
    $t->set_var(array('defaultDateFrom' => $dateFrom, 'defaultDateTo' => $dateTo));
} else {
    $t->set_var(array('defaultDateFrom' => $_REQUEST['defaultDateFrom'], 'defaultDateTo' => $_REQUEST['defaultDateTo']));
}

$reportData = array(
    'dateFrom' => date('j.n.Y', strtotime($dateFrom)),
    'dateTo' => date('j.n.Y', strtotime($dateTo)),
    'filterDateFrom' => $dateFrom,
    'filterDateTo' => $dateTo,
    'supplierGroupSearchString' => $supplierGroupSearchString,
    'showEmptyReport' => empty($commoditySummaryBySupplierGroupList['supplierGroupList']) ? true : false
);

$t->set_var(array(
    'commoditySummaryBySupplierGroupList' => htmlspecialchars(json_encode($commoditySummaryBySupplierGroupList['supplierGroupList'])),
    'totalAllGroups' => htmlspecialchars(json_encode($commoditySummaryBySupplierGroupList['totalAllGroups'])),
    'reportData' => htmlspecialchars(json_encode($reportData)),
    'sqlDateFrom' => $dateFrom,
    'sqlDateTo' => $dateTo,
    'rightListData' => htmlspecialchars(json_encode($rights)),
    'foodBankListData' => htmlspecialchars(json_encode($foodBankList)),
    'filterFoodBankId' => $filterFoodBankId
));

$t->set_global_var_to_template();
$t->pparse("out", "tmt");
