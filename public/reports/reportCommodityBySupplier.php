<?php
// sumární report komodit (množství a hodnota v penězích) podle dodavatelů za měsíc nebo za období

header("Content-Type: text/html; charset=utf-8");

require_once '../../conf/commonApp.php';

doInit();
core\AuthenticationManager::verifyTheAuthenticity($_authenticationCode, $_loggedAppUserId);
$authorizationManager = new core\AuthorizationManager($_loggedAppUser);
if (!$authorizationManager->hasRightToPage(core\RightName::reportList)) {
    core\HttpUtils::redirectAndExit();
}
$rights = $authorizationManager->getRights();
$hasAccessToAllPbanks = $authorizationManager->hasAccessToAllPbanks();

$t = new ui\Template("./lang/cs");
$t->set_file("tmt", "reportCommodityBySupplier.tmt");

if ($_GET['resetFilter'] == 1) {
    $dateFrom = date('Y-m-d', strtotime($_GET['defaultDateFrom']));
    $dateTo = date('Y-m-d', strtotime($_GET['defaultDateTo']));
} else {
    $dateFrom = $_POST['dateFrom'] ? date('Y-m-d', strtotime($_POST['dateFrom'])) : date('Y-m-d', strtotime($_GET['dateFrom']));
    $dateTo = $_POST['dateTo'] ? date('Y-m-d', strtotime($_POST['dateTo'])) : date('Y-m-d', strtotime($_GET['dateTo']));
}

$filterFoodBankId = !empty($_POST['filterFoodBankId']) ? $_POST['filterFoodBankId'] : 0;
$foodBankList = array();
if ($hasAccessToAllPbanks) {
    $foodBankManager = party\FoodBankManager::createForFoodBank($_loggedAppUser->foodBankId);
    $foodBankList = $foodBankManager->getFoodBankList();
}

$actionSearchString = $_POST['actionSearchString'];
$supplierNameSearchString = $_POST['supplierNameSearchString'];
$supplierGroupSearchString = $_POST['supplierGroupSearchString'];
$commoditySearchString = $_POST['commoditySearchString'];

$reportManager = report\ReportManager::createForFoodBank($_loggedAppUser->foodBankId);
$reportManager->setActionFilter($actionSearchString);
$reportManager->setSupplierFilter($supplierNameSearchString);
$reportManager->setSupplierGroupFilter($supplierGroupSearchString);
$reportManager->setCommodityFilter($commoditySearchString);
$reportManager->setFoodBankIdFilter($filterFoodBankId);
$commoditySummaryBySupplier = $reportManager->getCommoditySummaryBySupplierForPeriod($dateFrom, $dateTo, $hasAccessToAllPbanks);

if ($_GET['export'] == 1) {
    $includeValue = $_GET['includeValue'];
    $separator = report\ReportManager::CSV_COLUMN_SEPARATOR;

    $csvContent = "Naskladněné komodity podle dodavatelů za řetězec za období od " . date('j.n.Y', strtotime($dateFrom)) . " do " . date('j.n.Y', strtotime($dateTo)) . "\n\n";
    $csvContent .= "Řetězec" . $separator . "Dodavatel" . $separator . "Množství (kg)" . ($includeValue ? $separator . "Hodnota v (" . localization\LocalizationProvider::getCurrencySymbol() . ")" : "") . "\n\n";

    foreach ($commoditySummaryBySupplier['groups'] as $supplierGroup => $commoditiesBySupplierGroup) {
        $csvContent .= $supplierGroup . "\n";
        foreach ($commoditiesBySupplierGroup['suppliers'] as $supplier) {
            $csvContent .= "" . $separator . $supplier->supplierName . $separator . core\HpNumberFormatter::toAmount($supplier->amount) .
                ($includeValue ? $separator . core\HpNumberFormatter::toMoney($supplier->value) : '') . "\n";
        }
        $csvContent .= "Celkem za řetězec" . $separator . $separator . core\HpNumberFormatter::toAmount($commoditiesBySupplierGroup['totalAmount']) .
            ($includeValue ? $separator . core\HpNumberFormatter::toMoney($commoditiesBySupplierGroup['totalValue']) : "") . "\n\n";
    }
    $csvContent .= "Všichni dodavatelé dodali celkem " . core\HpNumberFormatter::toAmount($commoditySummaryBySupplier['totalAllGroups']['amount']) . " kg komodit." .
        ($includeValue ? " Hodnota komodit činí " . core\HpNumberFormatter::toMoney($commoditySummaryBySupplier['totalAllGroups']['value']) . " " . localization\LocalizationProvider::getCurrencySymbol() . "." : "") . "\n\n";
    $csvFilename = report\ReportManager::getCsvFileName("Naskladnene_komodity_podle_organizace_dodavatele", $dateFrom, $dateTo);
    report\ReportManager::sendCsvReport($csvContent, $csvFilename);
    exit;
}

if ($_POST['settingTheBaselinePeriod'] == 1) {
    $t->set_var(array('defaultDateFrom' => $dateFrom, 'defaultDateTo' => $dateTo));
} else {
    $t->set_var(array('defaultDateFrom' => $_REQUEST['defaultDateFrom'], 'defaultDateTo' => $_REQUEST['defaultDateTo']));
}

$reportData = array(
    'dateFrom' => date('j.n.Y', strtotime($dateFrom)),
    'dateTo' => date('j.n.Y', strtotime($dateTo)),
    'filterDateFrom' => $dateFrom,
    'filterDateTo' => $dateTo,
    'actionSearchString' => $actionSearchString,
    'supplierNameSearchString' => $supplierNameSearchString,
    'supplierGroupSearchString' => $supplierGroupSearchString,
    'commoditySearchString' => $commoditySearchString,
    'showEmptyReport' => empty($commoditySummaryBySupplier['groups']) ? true : false
);

$t->set_var(array(
    'commoditySummaryBySupplier' => htmlspecialchars(json_encode($commoditySummaryBySupplier['groups'])),
    'totalAllGroups' => htmlspecialchars(json_encode($commoditySummaryBySupplier['totalAllGroups'])),
    'reportData' => htmlspecialchars(json_encode($reportData)),
    'sqlDateFrom' => $dateFrom,
    'sqlDateTo' => $dateTo,
    'rightListData' => htmlspecialchars(json_encode($rights)),
    'foodBankListData' => htmlspecialchars(json_encode($foodBankList)),
    'filterFoodBankId' => $filterFoodBankId
));

$t->set_global_var_to_template();
$t->pparse("out", "tmt");
