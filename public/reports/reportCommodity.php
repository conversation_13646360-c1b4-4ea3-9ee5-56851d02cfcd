<?php
// sumární report komodit (množství a hodnota v penězích) za měsíc nebo za období
header("Content-Type: text/html; charset=utf-8");

require_once '../../conf/commonApp.php';

doInit();
core\AuthenticationManager::verifyTheAuthenticity($_authenticationCode, $_loggedAppUserId);
$authorizationManager = new core\AuthorizationManager($_loggedAppUser);
if (!$authorizationManager->hasRightToPage(core\RightName::reportList)) {
    core\HttpUtils::redirectAndExit();
}
$rights = $authorizationManager->getRights();
$hasAccessToAllPbanks = $authorizationManager->hasAccessToAllPbanks();
$t = new ui\Template("./lang/cs");
$t->set_file("tmt", "reportCommodity.tmt");

if ($_POST['includeReducedAmount'] || $_GET['includeReducedAmount']) {
    $includeReducedAmount = true;
}
if ($_POST['includeValue'] || $_GET['includeValue']) {
    $includeValue = true;
}

if ($_GET['resetFilter'] == 1) {
    $dateFrom = date('Y-m-d', strtotime($_GET['defaultDateFrom']));
    $dateTo = date('Y-m-d', strtotime($_GET['defaultDateTo']));
    $withoutDirectConsumption = ($_GET['defaultWithoutDirectConsumption'] == 1) ? true : false;
    $percentReduction = intval($_GET['percentReduction']);
} else {
    $dateFrom = $_POST['dateFrom'] ? date('Y-m-d', strtotime($_POST['dateFrom'])) : date('Y-m-d', strtotime($_GET['dateFrom']));
    $dateTo = $_POST['dateTo'] ? date('Y-m-d', strtotime($_POST['dateTo'])) : date('Y-m-d', strtotime($_GET['dateTo']));
    $withoutDirectConsumption = false;
    $percentReduction = $_POST['percentReduction'] ? intval($_POST['percentReduction']) : intval($_GET['percentReduction']);
    if ($_POST['withoutDirectConsumption'] == 1) {
        $withoutDirectConsumption = true;
    } else {
        $withoutDirectConsumption = ($_GET['withoutDirectConsumption'] == 1) ? true : false;
    }
}

$filterFoodBankId = !empty($_POST['filterFoodBankId']) ? $_POST['filterFoodBankId'] : 0;
$foodBankList = array();
if ($hasAccessToAllPbanks) {
    $foodBankManager = party\FoodBankManager::createForFoodBank($_loggedAppUser->foodBankId);
    $foodBankList = $foodBankManager->getFoodBankList();
}

$actionSearchString = $_POST['actionSearchString'];
$supplierSearchString = $_POST['supplierSearchString'];
$supplierGroupNameSearchString = $_POST['supplierGroupNameSearchString'];
$commoditySearchString = $_POST['commoditySearchString'];

$reportManager = report\ReportManager::createForFoodBank($_loggedAppUser->foodBankId);
$reportManager->setActionFilter($actionSearchString);
$reportManager->setSupplierFilter($supplierSearchString);
$reportManager->setSupplierGroupFilter($supplierGroupNameSearchString);
$reportManager->setCommodityFilter($commoditySearchString);
$reportManager->setFoodBankIdFilter($filterFoodBankId);
$reportManager->setPercentReduction($percentReduction);

$commodityIdExcludedList = array();
$commodityIdWithPercentReductionList = array();
$commoditySummary = $reportManager->getCommoditySummaryForPeriod($dateFrom, $dateTo, $hasAccessToAllPbanks, $commodityIdExcludedList, $withoutDirectConsumption, $commodityIdWithPercentReductionList);

if ($_POST['settingTheBaselinePeriod'] == 1) {
    $t->set_var(array('defaultDateFrom' => $dateFrom, 'defaultDateTo' => $dateTo));
} else {
    $t->set_var(array('defaultDateFrom' => $_REQUEST['defaultDateFrom'], 'defaultDateTo' => $_REQUEST['defaultDateTo']));
}

$reportData = array(
    'dateFrom' => $dateFrom,
    'dateTo' => $dateTo,
    'filterDateFrom' => $dateFrom,
    'filterDateTo' => $dateTo,
    'includeReducedAmount' => $includeReducedAmount,
    'includeValue' => $includeValue,
    'actionSearchString' => $actionSearchString,
    'customerNameSearchString' => $customerNameSearchString,
    'customerGroupNameSearchString' => $customerGroupNameSearchString,
    'supplierSearchString' => $supplierSearchString,
    'supplierGroupNameSearchString' => $supplierGroupNameSearchString,
    'commoditySearchString' => $commoditySearchString,
    'showEmptyReport' => empty($commoditySummary) ? true : false,
    'withoutDirectConsumption' => $withoutDirectConsumption,
    'percentReduction' => $percentReduction
);

$t->set_var(array(
    'commoditySummary' => htmlspecialchars(json_encode($commoditySummary)),
    'reportData' => htmlspecialchars(json_encode($reportData)),
    'rightListData' => htmlspecialchars(json_encode($rights)),
    'foodBankListData' => htmlspecialchars(json_encode($foodBankList)),
    'filterFoodBankId' => $filterFoodBankId,
    'defaultWithoutDirectConsumption' => $withoutDirectConsumption ? 1 : 0
));

$t->set_global_var_to_template();
$t->pparse("out", "tmt");
