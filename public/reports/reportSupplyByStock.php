<?php
header("Content-Type: text/html; charset=utf-8");

require_once '../../conf/commonApp.php';

doInit();
core\AuthenticationManager::verifyTheAuthenticity($_authenticationCode, $_loggedAppUserId);
$authorizationManager = new core\AuthorizationManager($_loggedAppUser);
if (!$authorizationManager->hasRightToPage(core\RightName::reportList)) { core\HttpUtils::redirectAndExit(); }
$rights = $authorizationManager->getRights();

$stockManager = form\StockManager::createForFoodBank($_loggedAppUser->foodBankId);
$stockListData = $stockManager->getStockList();

$t = new ui\Template("./lang/cs");
$t->set_file("tmt", "reportSupplyByStock.tmt");

if ($_GET['resetFilter'] == 1) {
    $stockId = $_GET['defaultStockId'];
    $dateTo = date('Y-m-d', strtotime($_GET['defaultDateTo']));
} else {
    $stockId = $_POST['stockId'];
    $dateTo = $_POST['dateTo'] ? date('Y-m-d', strtotime($_POST['dateTo'])) : date('Y-m-d', strtotime($_GET['dateTo']));
}

$reportManager = report\ReportManager::createForFoodBank($_loggedAppUser->foodBankId);
$reportManager->setFoodBankIdFilter($filterFoodBankId);
$commoditySummary = $reportManager->getStocks($dateTo, null, $stockId);


if ($_GET['export'] == 1) {
    $stockManager = form\StockManager::createForFoodBank($_loggedAppUser->foodBankId);
    
    $stockId = $_GET['stockId'];
    $stockName = $stockManager->getNameStockByStockId($stockId);
    $separator = report\ReportManager::CSV_COLUMN_SEPARATOR;

    $csvContent = "Inventura skladu: $stockName ke dni " . date('j.n.Y', strtotime($dateTo)) . "\n\n";
    $csvContent .= "Číslo komodity" . $separator . "Název komodity" . $separator . "Množství (kg)" . $separator . "Hodnota (" . localization\LocalizationProvider::getCurrencySymbol() . ")\n";

    foreach ($commoditySummary['commodities'] as $commodity) {
        $csvContent .= $commodity['code'] . $separator . $commodity['name'] . $separator . core\HpNumberFormatter::toAmount($commodity['amount']) . $separator . core\HpNumberFormatter::toMoney($commodity['value']) . "\n";
    }
    
    $csvContent .= $separator . "Celkem" . $separator . core\HpNumberFormatter::toAmount($commoditySummary['totalAmount']) . $separator . core\HpNumberFormatter::toMoney($commoditySummary['totalValue']);
    $csvFilename = report\ReportManager::getCsvFileName("Zasoby_skladu_id_".$stockId, '', $dateTo);
    report\ReportManager::sendCsvReport($csvContent, $csvFilename);
    exit;
}

if ($_POST['settingTheBaselinePeriod'] == 1) {
    $t->set_var(array('defaultStockId' => $stockId, 'defaultDateTo' => $dateTo));
} else {
    $t->set_var(array('defaultStockId' => $_REQUEST['defaultStockId'], 'defaultDateTo' => $_REQUEST['defaultDateTo']));
}

$reportData = array(
         'dateTo' => date('j.n.Y', strtotime($dateTo))
        ,'stockName' => $stockManager->getNameStockByStockId($stockId)
        ,'filterDateTo' => $dateTo
        ,'stockId' =>  $stockId
        ,'showEmptyReport' => empty($commoditySummary['commodities']) ? true : false
);

$t->set_var(array(
        'commoditySummary' => htmlspecialchars(json_encode($commoditySummary))
        ,'reportData' => htmlspecialchars(json_encode($reportData))
        ,'sqlDateTo' => $dateTo
        ,'rightListData' => htmlspecialchars(json_encode($rights))
        ,'stockListData' => htmlspecialchars(json_encode($stockListData)) 
        ,'filterStockId' => $stockId
));

$t->set_global_var_to_template();
$t->pparse("out", "tmt");
