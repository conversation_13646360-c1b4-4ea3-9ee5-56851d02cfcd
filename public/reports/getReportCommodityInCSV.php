<?php
header("Content-Type: text/html; charset=utf-8");

require_once '../../conf/commonApp.php';

doInit();
core\AuthenticationManager::verifyTheAuthenticity($_authenticationCode, $_loggedAppUserId);
$authorizationManager = new core\AuthorizationManager($_loggedAppUser);
if (!$authorizationManager->hasRightToPage(core\RightName::reportList)) {
    core\HttpUtils::redirectAndExit();
}
$rights = $authorizationManager->getRights();
$hasAccessToAllPbanks = $authorizationManager->hasAccessToAllPbanks();

$postData = file_get_contents('php://input');
$data = json_decode($postData, true);
$includeReducedAmount = $data['includeReducedAmount'];
$includeValue = $data['includeValue'];
$dateFrom = $data['dateFrom'];
$dateTo = $data['dateTo'];
$withoutDirectConsumption = $data['withoutDirectConsumption'];

$commodityIdExcludedList = array();
foreach ($data['commodityIsExcludedExport'] as $commodityId) {
    $commodityIdExcludedList[$commodityId] = $commodityId;
}

$commodityIdWithPercentReductionList = array();
foreach ($data['commodityList'] as $commodity) {
    $commodityIdWithPercentReductionList[$commodity['commodityId']] = intval($commodity['percentReduction']);
}

$reportManager = report\ReportManager::createForFoodBank($_loggedAppUser->foodBankId);
$commoditySummary = $reportManager->getCommoditySummaryForPeriod($dateFrom, $dateTo, $hasAccessToAllPbanks, $commodityIdExcludedList, $withoutDirectConsumption, $commodityIdWithPercentReductionList);
$separator = report\ReportManager::CSV_COLUMN_SEPARATOR;

$csvContent = "Naskladněné komodity za období od " . date('j.n.Y', strtotime($dateFrom)) . " do " . date('j.n.Y', strtotime($dateTo)) . "\n\n";
$csvContent .= "Číslo komodity" . $separator . "Název komodity" . $separator . "Množství (kg)" .
    ($includeValue ? $separator . "Hodnota v (" . localization\LocalizationProvider::getCurrencySymbol() . ")" : "") .
    ($includeReducedAmount ? $separator . "Procento snížení hmotnosti" : "") .
    ($includeReducedAmount ? $separator . "Snížené množství (kg)" : "") . "\n\n";

foreach ($commoditySummary['commodities'] as $commodity) {
    $csvContent .= $commodity['code'] . $separator . $commodity['name'] . $separator . core\HpNumberFormatter::toAmountInCsv($commodity['amount']) .
        ($includeValue ? $separator . core\HpNumberFormatter::toMoney($commodity->value) : '') .
        ($includeReducedAmount ? $separator . $commodity['percentReduction'] : '') .
        ($includeReducedAmount ? $separator . core\HpNumberFormatter::toAmountInCsv($commodity['reducedAmount']) : '') . "\n";
}

$csvContent .= $separator . "Celkem" . $separator . core\HpNumberFormatter::toAmountInCsv($commoditySummary['totalAmount']) .
    ($includeValue ? $separator . core\HpNumberFormatter::toMoney($commoditySummary['totalValue']) : "") .
    ($includeReducedAmount ? $separator : "") .
    ($includeReducedAmount ? $separator . core\HpNumberFormatter::toAmountInCsv($commoditySummary['totalReducedAmount']) : "") . "\n\n";

if ($withoutDirectConsumption) {
    $csvContent .= "Report je bez přímých odběrů.\n\n";
}

$csvFilename = report\ReportManager::getCsvFileName("Naskladnene_komodity", $dateFrom, $dateTo);
report\ReportManager::sendCsvReport($csvContent, $csvFilename);
exit;
