<?php
header("Content-Type: text/html; charset=utf-8");

require_once '../conf/commonApp.php';
doInit();
core\AuthenticationManager::verifyTheAuthenticity($_authenticationCode, $_loggedAppUserId);
$authorizationManager = new core\AuthorizationManager($_loggedAppUser);
$fullAccessToFormList = $authorizationManager->hasFullAccessToPage(core\RightName::formList);

if ($fullAccessToFormList) {
  $foodBankId = $_loggedAppUser->foodBankId;
  $supplierId = intval($_GET['supplierId']);
  $formId = intval($_GET['formId']);
  
  $result = \dibi::select('max([itemOrder])')->from('FormSupplier')->where("[formId] = %i AND [foodBankId] = %i", $formId, $foodBankId)->execute();
  $itemOrder = intval($result->fetchSingle());
  $itemOrder++;
  $values = array('formId' => $formId, 'supplierId' => $supplierId, 'itemOrder' => $itemOrder, 'foodBankId' => $foodBankId);
  \dibi::insert('FormSupplier', $values)->execute();
  core\HttpUtils::sendSuccessfulEmptyAjaxResponseAndExit();
}
