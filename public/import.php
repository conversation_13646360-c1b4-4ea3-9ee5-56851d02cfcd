<?php
header("Content-Type: text/html; charset=utf-8");

require_once '../conf/commonApp.php';

doInit();
core\AuthenticationManager::verifyTheAuthenticity($_authenticationCode, $_loggedAppUserId);
$authorizationManager = new core\AuthorizationManager($_loggedAppUser);
if (!$authorizationManager->hasRightToPage(core\RightName::import)) { core\HttpUtils::redirectAndExit(); }
$rights = $authorizationManager->getRights();
$hasAccessAllStocks = $authorizationManager->canRead('allStocks');

$t = new ui\Template("./lang/cs");
$t->set_file("tmt", "import.tmt");

$importer = import\Importer::createForFoodBank($_loggedAppUser->foodBankId);
$importedData = $importer->getImportedData($_loggedAppUser->currentStockId);
$appUserStockRelation = form\AppUserStockRelation::createForFoodBank($_loggedAppUser->foodBankId);
$addSummaryItem = false;
$stockListData = $appUserStockRelation->getSelectedStockListByAppUser($_loggedAppUser->appUserId, $hasAccessAllStocks, $addSummaryItem);

$t->set_var(array(
    'importedData' => htmlspecialchars(json_encode($importedData)),
    'stockListData' => htmlspecialchars(json_encode($stockListData)),
    'currentStockId' => is_null($_loggedAppUser->currentStockId) ? 'null' : $_loggedAppUser->currentStockId,
    'summaryItem' => $addSummaryItem ? 'true' : 'false'
));

$t->set_global_var_to_template();
$t->pparse("out", "tmt");
exit;