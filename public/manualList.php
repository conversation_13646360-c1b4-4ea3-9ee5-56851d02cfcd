<?php
// Správa manuálů pro potravinovou banku

header("Content-Type: text/html; charset=utf-8");

require_once '../conf/commonApp.php';

doInit();
core\AuthenticationManager::verifyTheAuthenticity($_authenticationCode, $_loggedAppUserId);
$authorizationManager = new core\AuthorizationManager($_loggedAppUser);

if (!$authorizationManager->hasRightToPage(core\RightName::manualList)) {
    core\HttpUtils::redirectAndExit();
}

$rights = $authorizationManager->getRights();
$fullAccessToManualList = $authorizationManager->hasFullAccessToPage(core\RightName::manualList);

$manualManager = manual\ManualManager::createForFoodBank($_loggedAppUser->foodBankId);

// Zpracování uložení manuálu
if (isset($_POST['saveManual']) && $fullAccessToManualList) {
    $manualData = json_decode($_POST['manual'], true);
    
    if ($manualData) {
        $success = $manualManager->saveManual($manualData, $_loggedAppUser->appUserId);
        
        if ($success) {
            // Přesměrování pro zabránění opětovnému odeslání formuláře
            header('Location: manualList.php?saved=1');
            exit;
        }
    }
}

// Zpracování smazání manuálu
if (isset($_POST['deleteManual']) && $fullAccessToManualList) {
    $success = $manualManager->deleteManual();
    
    if ($success) {
        header('Location: manualList.php?deleted=1');
        exit;
    }
}

// Načtení dat manuálu
$manualData = $manualManager->getManual();

// Pokud manuál neexistuje, vytvoříme prázdný objekt
if (!$manualData) {
    $manualData = (object) array(
        'title' => '',
        'content' => ''
    );
}

// Zprávy o úspěchu
$showSaveMessage = isset($_GET['saved']) && $_GET['saved'] == '1';
$showDeleteMessage = isset($_GET['deleted']) && $_GET['deleted'] == '1';

$t = new ui\Template("./lang/cs");
$t->set_file("tmt", "manualList.tmt");
$t->set_var('manualData', htmlspecialchars(json_encode($manualData)));
$t->set_var('showSaveMessage', $showSaveMessage ? 1 : 0);
$t->set_var('showDeleteMessage', $showDeleteMessage ? 1 : 0);
$t->set_global_var_to_template();
$t->pparse("out", "tmt");
