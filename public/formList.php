<?php
// přehled (seznam) vytvořených formulářů

header("Content-Type: text/html; charset=utf-8");

require_once '../conf/commonApp.php';

doInit();
core\AuthenticationManager::verifyTheAuthenticity($_authenticationCode, $_loggedAppUserId);
$authorizationManager = new core\AuthorizationManager($_loggedAppUser);
if (!$authorizationManager->hasRightToPage(core\RightName::formList)) { core\HttpUtils::redirectAndExit(); }
$rights = $authorizationManager->getRights();
$fullAccessToFormList = $authorizationManager->hasFullAccessToPage(core\RightName::formList);

$formManager = form\FormManager::createForFoodBank($_loggedAppUser->foodBankId);

if (isset($_POST['compose'])) {
	header('Location: formEditor.php');
	exit;
}

if (isset($_GET['delete'])) {
	$formManager->deleteForm($_GET['formId']);
}

if (isset($_POST['save'])) {

	$formToBeSaved = array(
	         'actionName' => $_POST['actionName']
	        ,'validFrom' => date('Y-m-d', strtotime($_POST['validFrom']))
	        ,'validTo' => date('Y-m-d', strtotime($_POST['validTo']))
	        ,'backDays' => $_POST['backDays']
	        ,'stockId' => $_POST['stockId']
	        ,'directConsumption' => is_null($_POST['directConsumption']) ? false : true
	        ,'incomePerStock' => is_null($_POST['incomePerStock']) ? false : true
	        ,'outputFromStock' => is_null($_POST['outputFromStock']) ? false : true
	        ,'appUserId' => $_loggedAppUserId
	        ,'foodBankId' => $_loggedAppUser->foodBankId
	        ,'formId' => $_POST['formId']
	);

	if ($fullAccessToFormList) {
	    $formManager->saveForm($formToBeSaved);
	}
}

$forms = $formManager->getFormList();
$t = new ui\Template("./lang/cs");
$t->set_file("tmt", "formList.tmt");
$t->set_block("tmt", "formList", "{tmtformList}");
$t->set_block("tmt", "form", "{tmtform}");

$stockManager = form\StockManager::createForFoodBank($_loggedAppUser->foodBankId);
$stockListData = $stockManager->getStockList(false);

foreach ($forms as $form) {

  $formId = $form->formId;
  $formListData[] = array(
      'formId' => $formId
     ,'actionName' => $form->actionName
     ,'validFrom' => substr($form->validFrom, 0, 10)
     ,'validTo' => substr($form->validTo, 0, 10)
     ,'backDays' => intval($form->backDays)
     ,'editedItem' => $formManager->isFormFilled($formId)
     ,'stockId' => $form->stockId
     ,'directConsumption' => $form->directConsumption
     ,'incomePerStock' => $form->incomePerStock
     ,'outputFromStock' => $form->outputFromStock
  );
}

$t->set_var(array(
     'formListData' => htmlspecialchars(json_encode($formListData))
    ,'stockListData' => htmlspecialchars(json_encode($stockListData))
));

$t->set_global_var_to_template();
$t->pparse("out", "tmt");
