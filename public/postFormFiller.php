<?php
header("Content-Type: text/html; charset=utf-8");

require_once '../conf/commonApp.php';

doInit();
core\AuthenticationManager::verifyTheAuthenticity($_authenticationCode, $_loggedAppUserId);
$authorizationManager = new core\AuthorizationManager($_loggedAppUser);

if (!$authorizationManager->hasRightToPage(core\RightName::formFiller)) {
    core\HttpUtils::redirectAndExit();
}

$postData = json_decode(file_get_contents('php://input'), true);

//uloz prijem
$filledFormManager = form\FilledForm::createForFoodBank($_loggedAppUser->foodBankId);
$formId = intval($postData['formFillerList']['formId']);
$giftValue = core\HpNumberFormatter::replaceCommaToDotInNumber($postData['formFillerList']['giftValue']);
$formManager = form\FormManager::createForFoodBank($_loggedAppUser->foodBankId);
$stockId = $formManager->getStockIdByFormId($formId);
$consumptionDateAndIssuedAt = date('Y-m-d', strtotime($postData['consumptionDate'])); // datum naskladneni je take datum vyskladneni
$indirectly = !empty($postData['formFillerList']['indirectly']);

//prijemce je Potravinova banka
$foodBankManager = party\FoodBankManager::createForFoodBank($_loggedAppUser->foodBankId);
$foodBank = $foodBankManager->getFoodBankByFoodBankId();
//dodavatel je retezec supplier
$supplierManager = party\SupplierManager::createForFoodBank($_loggedAppUser->foodBankId);
$supplierId = intval($postData['currentSupplier']['supplierId']);
$supplier = $supplierManager->getSupplierBySupplierId($supplierId);
$note = trim($postData['formFillerList']['note']);

$totalAmount = 0;
$commodityList = $postData['commodityList'];

if (!empty($commodityList)) {
    foreach ($commodityList as $commodity) {
        $commodity = (object) $commodity;
        $amount = (float) core\HpNumberFormatter::replaceCommaToDotInNumber($commodity->amount);
        $totalAmount += $amount;
    }
}

$filledFormToBeSaved = array(
    'consumptionDate' => $consumptionDateAndIssuedAt,
    'supplierId' => $supplierId,
    'giftValue' => $giftValue,
    'formId' => $formId,
    'stockId' => $stockId,
    'filledFormId' => 0,
    'note' => $note,
    'appUserId' => $_loggedAppUserId,
    'supplierName' => $supplier->name,
    'supplierAddress' => $supplier->address,
    'supplierIC' => $supplier->IC,
    'foodBankName' => $foodBank->name,
    'foodBankAddress' => $foodBank->address,
    'foodBankIC' => $foodBank->ic,
    'totalAmount' => $totalAmount * 1000
);

$filledForm = $filledFormManager->saveFilledForm($filledFormToBeSaved);

$filledFormId = $filledForm->filledFormId;
$foodBankFilledFormId = $filledForm->foodBankFilledFormId;
$stockFilledFormId = $filledForm->stockFilledFormId;

$commodityToBeSaved = array();

if (!empty($commodityList)) {
    foreach ($commodityList as $commodity) {
        $commodity = (object) $commodity;
        $amount = core\HpNumberFormatter::replaceCommaToDotInNumber($commodity->amount);
        $commodity->amount = $amount;
        $commodity->filledFormId = $filledFormId;
        $commodityToBeSaved[] = $commodity;
    }
    $filledFormCommodity = form\FilledFormCommodity::createForFoodBank($_loggedAppUser->foodBankId);
    $filledFormCommodity->saveFilledFormCommodity($commodityToBeSaved);
}

//uloz odber
$stockReleaseNoteManager = form\StockReleaseNote::createForFoodBank($_loggedAppUser->foodBankId);
$formManager = form\FormManager::createForFoodBank($_loggedAppUser->foodBankId);
$customerManager = new party\CustomerManager();
$customerId = $indirectly ? intval($postData['formFillerList']['customerId']) : intval($_loggedAppUser->customerId);
$customer = $customerManager->getCustomerByCustomerId($customerId);

$stockReleaseNoteToBeSaved = array(
    'issuedAt' => $consumptionDateAndIssuedAt,
    'customerId' => $customerId,
    'formId' => $formId,
    'stockId' => $stockId,
    'filledFormId' => $filledFormId,
    'stockReleaseNoteId' => 0,
    'appUserId' => $_loggedAppUserId,
    'customerName' => $customer->name,
    'customerAddress' => $customer->address,
    'customerIC' => $customer->IC,
    'foodBankName' => $foodBank->name,
    'foodBankAddress' => $foodBank->address,
    'foodBankIC' => $foodBank->ic,
    'note' => $note,
    'totalAmount' => $totalAmount * 1000
);

$stockReleaseNote = $stockReleaseNoteManager->saveStockReleaseNote($stockReleaseNoteToBeSaved);
$stockReleaseNoteId = $stockReleaseNote->stockReleaseNoteId;

reset($commodityList);
$commodityToBeSaved = array();

if (!empty($commodityList)) {
    foreach ($commodityList as $commodity) {
        $commodity = (object) $commodity;
        $amount = core\HpNumberFormatter::replaceCommaToDotInNumber($commodity->amount);
        $commodity->amount = $amount;
        $commodity->stockReleaseNoteId = $stockReleaseNoteId;
        $commodityToBeSaved[] = $commodity;
    }
    $stockReleaseNoteCommodity = form\StockReleaseNoteCommodity::createForFoodBank($_loggedAppUser->foodBankId);
    $stockReleaseNoteCommodity->saveStockReleaseNoteCommodity($commodityToBeSaved);
}

// email
$filledFormCommodity = form\FilledFormCommodity::createForFoodBank($_loggedAppUser->foodBankId);
$filledFormManager = form\FilledForm::createForFoodBank($_loggedAppUser->foodBankId);
$foodBankManager = party\FoodBankManager::createForFoodBank($_loggedAppUser->foodBankId);

$foodBankManagerData = $foodBankManager->getFoodBankByFoodBankId();
$filledFormData = $filledFormCommodity->getFilledFormCommodityByFilledFormId($filledFormId);
$sentFormData = $filledFormManager->getSentFormByFilledFormId($filledFormId);
$sentFormData['totalKg'] = $filledFormData['totalAmount'];
$sentFormData['priceCalculation'] = $filledFormData['priceCalculation'];

$emailText = "Akce: " . $sentFormData['actionName'] . " \r\n";
$emailText .= "Číslo vytvořené příjemky: " . $stockFilledFormId . " \r\n";
$emailText .= "Číslo vytvořené výdejky: " . $stockReleaseNote->stockStockReleaseNoteId . " \r\n";
$emailText .= "Odběr se uskutečnil dne: " . date('j.n.Y', strtotime($sentFormData['consumptionDate'])) . " \r\n";
$emailText .= "Odběratel: " . $_loggedAppUser->name . " \r\n";
$emailText .= "Dodavatel: " . $sentFormData['supplierName'] . " \r\n";
$emailText .= "Hodnota daru uvedená dodavatelem: " . core\HpNumberFormatter::toMoney($sentFormData['giftValue']) . " " . localization\LocalizationProvider::getCurrencySymbol() . " \r\n";
$emailText .= "Hodnota daru v kalkulační ceně: " . core\HpNumberFormatter::toMoney($sentFormData['priceCalculation']) . " " . localization\LocalizationProvider::getCurrencySymbol() . " \r\n";
$emailText .= "Množství celkem: " . core\HpNumberFormatter::toTwoDecimals($sentFormData['totalKg']) . " kg \r\n \r\n";

$emailText .= "Množství odebraných komodit \r\n";

foreach ($filledFormData['filledFormCommodityListData'] as $filledForm) {
    $emailText .= "Číslo komodity: " . trim($filledForm['code']) . "; ";
    $emailText .= "Název komodity: " . trim($filledForm['name']) . "; ";
    $emailText .= "Množství: " . core\HpNumberFormatter::toTwoDecimals($filledForm['amount']) . " kg \r\n";
}

$subject = "Kopie formuláře z aplikace hpbanka: Akce " . $sentFormData['actionName'];

$headers = array();
$headers[] = "MIME-Version: 1.0";
$headers[] = "Content-type: text/plain; charset=utf-8";
$headers[] = "From: " . $foodBankManagerData->senderEmails;
$headers[] = "Cc: " . $foodBankManagerData->senderEmails;
$headers[] = "Bcc: " . $foodBankManagerData->blandCopyEmails;
$headers[] = "Reply-To: " . $foodBankManagerData->recipientEmails;
$headers[] = "X-Mailer: PHP/" . phpversion();

if (!DEVEL) {
    $email = new email\HpEmail(
        $foodBankManagerData->senderEmails,
        $foodBankManagerData->senderEmails,
        $foodBankManagerData->blandCopyEmails,
        $foodBankManagerData->recipientEmails
    );
    $email->setSubject($subject);
    $email->setContent($emailText);
    $email->sendTo($_loggedAppUser->email);
} else {
    /*
$email .= "From: " . $foodBankManagerData->senderEmails . " \r\n";
$email .= "Cc: " . $foodBankManagerData->senderEmails . " \r\n";
$email .= "Bcc: " . $foodBankManagerData->blandCopyEmails . " \r\n";
$email .= "Reply-To: " . $foodBankManagerData->recipientEmails . " \r\n";
$email .= "Příjemce emailu: $_loggedAppUser->email \r\n";
$email .= "Předmět emailu: $subject \r\n";
$email .= "Obsah emailu: \r\n";
header('Content-Description: File Transfer');
header('Content-Type: text/plain; charset=utf-8');
header('Content-Disposition: attachment; filename=email.txt');
header('Expires: 0');
header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
header('Pragma: public');
header('Content-Length: '. strlen($email . $emailText));
echo $email . $emailText;
 */
}

$formFillerResult = array(
    'stockReleaseNoteId' => $stockReleaseNoteId,
    'stockFilledFormId' => $stockFilledFormId,
    'stockStockReleaseNoteId' => $stockReleaseNote->stockStockReleaseNoteId,
    'formHasBeenFilledAndSent' => 1,
    'indirectly' => $indirectly
);
core\HttpUtils::sendSuccessfulJsonAjaxResponseAndExit($formFillerResult);
