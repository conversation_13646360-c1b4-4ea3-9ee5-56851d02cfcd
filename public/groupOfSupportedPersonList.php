<?php
header("Content-Type: text/html; charset=utf-8");

require_once '../conf/commonApp.php';

doInit();
core\AuthenticationManager::verifyTheAuthenticity($_authenticationCode, $_loggedAppUserId);
$authorizationManager = new core\AuthorizationManager($_loggedAppUser);

if (!$authorizationManager->hasRightToPage(core\RightName::groupOfSupportedPersonList)) {
    core\HttpUtils::redirectAndExit();
}

$rights = $authorizationManager->getRights();
$fullAccessToGroupOfSupportedPersonList = $authorizationManager->hasFullAccessToPage(core\RightName::groupOfSupportedPersonList);
$hasAccessToGroupOfSupportedPersonList = $authorizationManager->hasFullAccessToPage(core\RightName::hasAccessToGroupOfSupportedPersonList);
$groupOfSupportedPersonManager = form\GroupOfSupportedPersonManager::create();
$foodBankManager = party\FoodBankManager::createForFoodBank($_loggedAppUser->foodBankId);

if ($_GET['delete'] and $hasAccessToGroupOfSupportedPersonList) {
    $groupOfSupportedPersonManager->deleteGroupOfSupportedPerson($_GET['groupId']);
}

if (isset($_POST['save']) and $hasAccessToGroupOfSupportedPersonList) {
    $groupOfSupportedPersonToBeSaved = array(
        'name' => $_POST['name'],
        'groupId' => $_POST['groupId']
    );
    $groupOfSupportedPersonManager->saveGroupOfSupportedPerson($groupOfSupportedPersonToBeSaved);
}

if (isset($_POST['saveSettings']) and $fullAccessToGroupOfSupportedPersonList) {
    $foodBankManager->setDaysToFillOutSupportedPersons($_POST['daysToFillOutSupportedPersons']);

    if (!empty($_POST['lockoutDateForSupportedPersons'])) {
        $lockoutDateForSupportedPersons = date('Y-m-d', strtotime($_POST['lockoutDateForSupportedPersons']));
    } else {
        $lockoutDateForSupportedPersons = null;
    }

    $foodBankManager->setLockoutDateForSupportedPersons($lockoutDateForSupportedPersons);
}

$t = new ui\Template("./lang/cs");
$t->set_file("tmt", "groupOfSupportedPersonList.tmt");

$usedGroupIdList = $groupOfSupportedPersonManager->getUsedGroupIdListByFoodBankId($_loggedAppUser->foodBankId);
$groupOfSupportedPersonList = $groupOfSupportedPersonManager->getGroupOfSupportedPersonList();
$groupOfSupportedPersonListData = array();

foreach ($groupOfSupportedPersonList as $groupOfSupportedPerson) {
    $groupId = $groupOfSupportedPerson['groupId'];
    $groupOfSupportedPersonListData[] = array(
        'groupId' => $groupId,
        'name' => $groupOfSupportedPerson['name'],
        'isUsedGroupId' => array_key_exists($groupId, $usedGroupIdList)
    );
}

$daysToFillOutSupportedPersonsData = $foodBankManager->getDaysToFillOutSupportedPersons();
$lockoutDateForSupportedPersons = $foodBankManager->getLockoutDateForSupportedPersons();
$foodBankData = array(
    'daysToFillOutSupportedPersons' => $daysToFillOutSupportedPersonsData->daysToFillOutSupportedPersons,
    'lockoutDateForSupportedPersons' => substr($lockoutDateForSupportedPersons, 0, 10)
);

$stockListData = array();
$t->set_var(array(
    'groupOfSupportedPersonListData' => htmlspecialchars(json_encode($groupOfSupportedPersonListData)),
    'foodBankData' => htmlspecialchars(json_encode($foodBankData))
));
$t->set_global_var_to_template();
$t->pparse("out", "tmt");
