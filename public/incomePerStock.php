<?php
header("Content-Type: text/html; charset=utf-8");

require_once '../conf/commonApp.php';

doInit();
core\AuthenticationManager::verifyTheAuthenticity($_authenticationCode, $_loggedAppUserId);
$authorizationManager = new core\AuthorizationManager($_loggedAppUser);
if (!$authorizationManager->hasRightToPage(core\RightName::incomePerStock)) {
    core\HttpUtils::redirectAndExit();
}
$rights = $authorizationManager->getRights();
$fullAccessToIncomePerStock = $authorizationManager->hasFullAccessToPage(core\RightName::incomePerStock);

if (isset($_POST['save']) and $fullAccessToIncomePerStock) {

    //prijemce je Potravinova banka
    $foodBankManager = party\FoodBankManager::createForFoodBank($_loggedAppUser->foodBankId);
    $foodBank = $foodBankManager->getFoodBankByFoodBankId();
    //dodavatel je retezec supplier
    $supplierManager = party\SupplierManager::createForFoodBank($_loggedAppUser->foodBankId);

    $incomePerStock = json_decode($_POST['incomePerStock']);
    $filledFormManager = form\FilledForm::createForFoodBank($_loggedAppUser->foodBankId);
    $formId = intval($incomePerStock->formId);
    $giftValue = core\HpNumberFormatter::replaceCommaToDotInNumber($incomePerStock->giftValue);

    $supplierId = intval($_POST['supplierId']);
    $supplier = $supplierManager->getSupplierBySupplierId($supplierId);

    $filledFormToBeSaved = array(
        'consumptionDate' => date('Y-m-d', strtotime($incomePerStock->consumptionDate)),
        'supplierId' => $supplierId,
        'giftValue' => $giftValue,
        'formId' => $formId,
        'stockId' => intval($_POST['stockId']),
        'filledFormId' => 0,
        'appUserId' => $_loggedAppUserId,
        'supplierName' => $supplier->name,
        'supplierAddress' => $supplier->address,
        'supplierIC' => $supplier->IC,
        'foodBankName' => $foodBank->name,
        'foodBankAddress' => $foodBank->address,
        'foodBankIC' => $foodBank->ic,
        'note' => $incomePerStock->note,
        'totalAmount' => ($incomePerStock->totalAmount * 1000)
    );

    $filledFormIds = $filledFormManager->saveFilledForm($filledFormToBeSaved);
    $filledFormId = $filledFormIds->filledFormId;

    $commodityList = json_decode($_POST['commodityList']);
    $commodityToBeSaved = array();

    if (!empty($commodityList)) {

        foreach ($commodityList as $commodity) {
            $commodity = (object) $commodity;
            $amount = core\HpNumberFormatter::replaceCommaToDotInNumber($commodity->amount);
            $commodity->amount = $amount;
            $commodity->filledFormId = $filledFormId;
            $commodityToBeSaved[] = $commodity;
        }

        $filledFormCommodity = form\FilledFormCommodity::createForFoodBank($_loggedAppUser->foodBankId);
        $filledFormCommodity->saveFilledFormCommodity($commodityToBeSaved);
    }
    header('Location: incomePerStockList.php');
    exit;
}

$t = new ui\Template("./lang/cs");
$t->set_file("tmt", "incomePerStock.tmt");
$formManager = form\FormManager::createForFoodBank($_loggedAppUser->foodBankId);
$formId = $_GET['formId'];
$form = $formManager->getForm($formId);

$commodityManager = new form\CommodityManager();
$commodities = $commodityManager->getSelectedCommodityList($formId);

foreach ($commodities as $commodity) {

    $commodityListData[] = array(
        'commodityId' => $commodity->commodityId, 'name' => $commodity->name, 'unit' => $commodity->unit, 'amount' => '', 'pricePerUnit' => $commodity->pricePerUnit, 'code' => $commodity->code
    );
}

$stockManager = form\StockManager::createForFoodBank($_loggedAppUser->foodBankId);
$supplierManager = party\SupplierManager::createForFoodBank($_loggedAppUser->foodBankId);
$suppliersResult = $supplierManager->getSelectedSupplierList($formId);
$today = date\HpCalendar::getToday();

$incomePerStockData = array(
    'actionName' => $form->actionName, 'supplierId' => 1, 'formId' => $form->formId, 'customerId' => $_loggedCustomer->customerId, 'giftValue' => null, 'consumptionDate' => date\DateFormatter::formatToSql($today), 'nameStock' => $stockManager->getNameStockByStockId($_loggedAppUser->currentStockId)
);

$t->set_var(array(
    'commodityListData' => htmlspecialchars(json_encode($commodityListData)), 'supplierListData' => htmlspecialchars(json_encode($suppliersResult->fetchAll())), 'incomePerStockData' => htmlspecialchars(json_encode($incomePerStockData)), 'rightListData' => htmlspecialchars(json_encode($rights)), 'currentStockId' => $_loggedAppUser->currentStockId
));

$t->set_global_var_to_template();
$t->pparse("out", "tmt");
