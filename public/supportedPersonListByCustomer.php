<?php
header("Content-Type: text/html; charset=utf-8");

require_once '../conf/commonApp.php';

doInit();

core\AuthenticationManager::verifyTheAuthenticity($_authenticationCode, $_loggedAppUserId);
$authorizationManager = new core\AuthorizationManager($_loggedAppUser);

if (!$authorizationManager->hasRightToPage(core\RightName::supportedPersonListByCustomer)) {
    core\HttpUtils::redirectAndExit();
}

$rights = $authorizationManager->getRights();
$fullAccessToSupportedPersonListByCustomer = $authorizationManager->hasFullAccessToPage(core\RightName::supportedPersonListByCustomer);
$supportedPersonManager = form\SupportedPersonManager::createForFoodBank($_loggedAppUser->foodBankId);

if (isset($_POST['filter'])) {
    $filterListData = array(
        'name' => $_POST['filterByName'],
        'contactPerson' => $_POST['filterByContactPerson'],
        'groupName' => $_POST['filterByGroupName'],
        'dateFrom' => empty($_POST['filterByDateFrom']) ? '' : date('Y-m-d', strtotime($_POST['filterByDateFrom']))
    );
} else {
    $filterListData = array('name' => '', 'contactPerson' => '', 'groupName' => '', 'dateFrom' => '');
}

$t = new ui\Template("./lang/cs");
$t->set_file("tmt", "supportedPersonListByCustomer.tmt");

$supportedPersonManager->setCustomerNameFilter($filterListData['name']);
$supportedPersonManager->setContactPersonFilter($filterListData['contactPerson']);
$supportedPersonManager->setGroupNameFilter($filterListData['groupName']);
$supportedPersonManager->setDateFromFilter($filterListData['dateFrom']);
$customerListWithSumNumberOfSupportedPersonData = $supportedPersonManager->getCustomerListWithSumNumberOfSupportedPersons();

$t->set_var(array(
    'supportedPersonListByCustomerData' => htmlspecialchars(json_encode($customerListWithSumNumberOfSupportedPersonData)),
    'filterListData' => htmlspecialchars(json_encode($filterListData))
));
$t->set_global_var_to_template();
$t->pparse("out", "tmt");
