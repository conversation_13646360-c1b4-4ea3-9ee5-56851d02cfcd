<?php
header("Content-Type: text/html; charset=utf-8");

require_once '../conf/commonApp.php';
doInit();
core\AuthenticationManager::verifyTheAuthenticity($_authenticationCode, $_loggedAppUserId);
$authorizationManager = new core\AuthorizationManager($_loggedAppUser);
if (!$authorizationManager->hasRightToPage(core\RightName::stockReleaseNoteList)) { core\HttpUtils::redirectAndExit(); }

$postData = json_decode(file_get_contents('php://input'), true);
$stockReleaseNote = form\StockReleaseNote::createForFoodBank($_loggedAppUser->foodBankId);
$stockReleaseNote->setStockByAppUser($_loggedAppUserId);
$filterList = $postData['filterList'];
$stockReleaseNote->setCustomerNameFilter($filterList['customer']);
$stockReleaseNote->setDateFromFilter($filterList['dateFrom']);
$stockReleaseNote->setDateToFilter($filterList['dateTo']);
$stockReleaseNoteListData = $stockReleaseNote->getStockReleaseNoteList();

$result = array('stockReleaseNoteListData' => $stockReleaseNoteListData);
core\HttpUtils::sendSuccessfulJsonAjaxResponseAndExit($result);
