angular.module('PBanka', ['hpBankaMenu'])

.controller('CustomerListController', function($scope, $http) {
	
	$scope.showEdit = false;
	$scope.showPassword = false;
	
	$scope.editCustomer = function(customerId) {
		angular.forEach($scope.customerList, function(customer) {
			if (customer.customerId == customerId) {
				$scope.customer = customer;
			} 
		});
		$scope.showEdit = true;
	};
	
	$scope.editPassword = function(customerId) {
		$scope.editCustomer(customerId);
		$scope.showPassword = true;
	}
	
	$scope.addCustomer = function() {
		var customer = {
				name: '',
				contactPerson: '',
				address: '',
				email: '',
				phone: '',
				groupName: '',
				IC: '',
				login: '',
				password: '',
				customerId: 0,
				appUserId: 0,
				archived : false
		}
		$scope.customer = customer;
		$scope.showEdit = true;
	};

	$scope.showCustomer = function (customerId) {
		$scope.editCustomer(customerId);
	};
	
	$scope.saveArchivingCustomer = function(customerId, archived) {
		
        $http.get('saveArchivingCustomer.php?customerId=' + encodeURIComponent(customerId) + '&archived=' + encodeURIComponent(archived))
        .success(function(result) {
        })
        .error(function(data) {
        });
	}
	
	$scope.customerArchive = function(customerId) {
		$scope.saveArchivingCustomer(customerId, true);
	};
	
	$scope.customerUnArchive = function(customerId) {
		$scope.saveArchivingCustomer(customerId, false);
	};
	
	$scope.canEdit = function() {
		return $scope.rightList.customerList.fullAccess;
	};
	
	$scope.canSave = function() {
		return $scope.customerListForm.$valid;
	};
	
	$scope.reverse = false;
	$scope.sordBy = 'login';
	$scope.order = function(sordBy) {
		$scope.reverse = ($scope.sordBy === sordBy) ? !$scope.reverse : false;
	    $scope.sordBy = sordBy;
	};	
})

.directive('username', function($q, $http) {
  return {
    require: 'ngModel',
    link: function(scope, elm, attrs, ctrl) {

      ctrl.$asyncValidators.username = function(modelValue, viewValue) {
    	  
        if (ctrl.$isEmpty(modelValue)) {
          // consider empty model valid
          return $q.when();
        }

        var def = $q.defer();
        
    	$http.get('getLogin.php?username=' + encodeURIComponent(modelValue))
        .success(function(result) {
            if (result.login != modelValue || result.appUserId == scope.customer.appUserId) {
                // The username is available
                def.resolve();
              } else {
                def.reject();
              }
        })
        .error(function(data) {
        });

        return def.promise;
      };
    }
  };
})

.directive('sameasusername', function($q) {
  return {
    require: 'ngModel',
    link: function(scope, elm, attrs, ctrl) {
    	
    	ctrl.$validators.sameasusername = function(modelValue, viewValue) {
      	  
            if (ctrl.$isEmpty(modelValue)) {
              // consider empty model valid
              return true;
            }
            return scope.customer.login != modelValue;
    	};
    }
  };
});


