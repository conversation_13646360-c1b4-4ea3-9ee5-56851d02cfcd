<?php
use date\DateFormatter;
use date\HpCalendar;
// přehled odběrů
header("Content-Type: text/html; charset=utf-8");

require_once '../conf/commonApp.php';

doInit();
core\AuthenticationManager::verifyTheAuthenticity($_authenticationCode, $_loggedAppUserId);
$authorizationManager = new core\AuthorizationManager($_loggedAppUser);
if (!$authorizationManager->hasRightToPage(core\RightName::sentFormList)) {
    core\HttpUtils::redirectAndExit();
}
$rights = $authorizationManager->getRights();

$t = new ui\Template("./lang/cs");

$t->set_file("tmt", "sentFormList.tmt");

if (isset($_POST['filter'])) {
    $filterListData = array(
        'dateFrom' => empty($_POST['filterByDateFrom']) ? '' : date('Y-m-d', strtotime($_POST['filterByDateFrom'])),
        'dateTo' => empty($_POST['filterByDateTo']) ? '' : date('Y-m-d', strtotime($_POST['filterByDateTo'])),
        'stockStockReleaseNoteId' => $_POST['filterByStockStockReleaseNoteId'],
        'actionName' => $_POST['filterByActionName'],
        'supplierName' => $_POST['filterBySupplierName']
    );
} else {
    $actualYear = HpCalendar::getActualYear();
    $filterListData = array(
        'dateFrom' => DateFormatter::formatToSql(HpCalendar::getFirstDayOfTheYear($actualYear)),
        'dateTo' => DateFormatter::formatToSql(HpCalendar::getLastDayOfTheYear($actualYear)),
        'stockStockReleaseNoteId' => '',
        'actionName' => '',
        'supplierName' => ''
    );
}

$filledForm = form\FilledForm::createForFoodBank($_loggedAppUser->foodBankId);
$filledForm->setDateFromFilter($filterListData['dateFrom']);
$filledForm->setDateToFilter($filterListData['dateTo']);
$filledForm->setSupplierNameFilter($filterListData['supplierName']);
$filledForm->setActionName($filterListData['actionName']);
$filledForm->setStockStockReleaseNoteId($filterListData['stockStockReleaseNoteId']);
$sentFormListData = $filledForm->getSentFormListByCustomerId($_loggedAppUser->customerId);

$t->set_var(array(
    'sentFormListData' => htmlspecialchars(json_encode($sentFormListData)),
    'rightListData' => htmlspecialchars(json_encode($rights)),
    'filterListData' => htmlspecialchars(json_encode($filterListData))
));

$t->set_global_var_to_template();
$t->pparse("out", "tmt");
exit;