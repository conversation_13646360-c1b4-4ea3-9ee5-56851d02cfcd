<?php
header("Content-Type: text/html; charset=utf-8");

require_once '../conf/commonApp.php';

doInit();
core\AuthenticationManager::verifyTheAuthenticity($_authenticationCode, $_loggedAppUserId);
$authorizationManager = new core\AuthorizationManager($_loggedAppUser);
if (!$authorizationManager->hasRightToPage(core\RightName::stockTransfersList)) {core\HttpUtils::redirectAndExit();}
$rights = $authorizationManager->getRights();
// TODO doresit pravo

$transferId = $_GET['transferId'];
$stockTransfer = form\StockTransfer::createForFoodBank($_loggedAppUser->foodBankId);
$stockTransferData = $stockTransfer->getStockTransferByTransferId($transferId);

$transferCommodity = form\TransferCommodity::createForFoodBank($_loggedAppUser->foodBankId);
$transferCommodityList = $transferCommodity->getTransferCommodityByTransferIdAndStockId($transferId, $stockTransferData['sourceStockId']);
$transferCommodityData['transferCommodityList'] = $transferCommodityList;

$data = array('stockTransferData' => array_merge($transferCommodityData, $stockTransferData));
core\HttpUtils::sendSuccessfulJsonAjaxResponseAndExit($data);
