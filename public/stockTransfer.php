<?php
header("Content-Type: text/html; charset=utf-8");

require_once '../conf/commonApp.php';

doInit();
core\AuthenticationManager::verifyTheAuthenticity($_authenticationCode, $_loggedAppUserId);
$authorizationManager = new core\AuthorizationManager($_loggedAppUser);
if (!$authorizationManager->hasRightToPage(core\RightName::stockTransfersList)) {
    core\HttpUtils::redirectAndExit();
}
$rights = $authorizationManager->getRights();
// TODO doresit pravo

if (isset($_POST['save'])) {

    $stockTransferData = json_decode($_POST['stockTransferData']);
    $stockTransfer = form\StockTransfer::createForFoodBank($_loggedAppUser->foodBankId);
    $stockTranferToBeSaved = array(
        'issuedAt' => date('Y-m-d', strtotime($stockTransferData->issuedAt)),
        'sourceStockId' => intval($stockTransferData->sourceStockId),
        'targetStockId' => $stockTransferData->currentTargetStock->stockId,
        'note' => $stockTransferData->note,
        'appUserId' => $_loggedAppUserId
    );

    $transferData = $stockTransfer->saveStockTransfer($stockTranferToBeSaved);
    $transferId = $transferData->transferId;
    $transferCommodityList = $stockTransferData->transferCommodityList;
    $transferCommodityToBeSaved = array();

    if (!empty($transferCommodityList)) {

        foreach ($transferCommodityList as $commodity) {
            $commodity = (object) $commodity;
            $amount = core\HpNumberFormatter::replaceCommaToDotInNumber($commodity->amount);
            $commodity->amount = $amount;
            $commodity->transferId = $transferId;
            $transferCommodityToBeSaved[] = $commodity;
        }

        $transferCommodity = form\TransferCommodity::createForFoodBank($_loggedAppUser->foodBankId);
        $transferCommodity->saveTransferCommodity($transferCommodityToBeSaved);
    }

    header('Location: stockTransfersList.php');
    exit;
}

$t = new ui\Template("./lang/cs");
$t->set_file("tmt", "stockTransfer.tmt");

$dateTo = date('Y-m-d');
$stockId = !empty($_GET['stockId']) ? $_GET['stockId'] : 0;

$stockManager = form\StockManager::createForFoodBank($_loggedAppUser->foodBankId);
$stockListData = $stockManager->getStockListWhitoutStockId($stockId);

$reportManager = report\ReportManager::createForFoodBank($_loggedAppUser->foodBankId);
$commoditySummaryList = $reportManager->getAvailableCommodities($dateTo, null, $stockId);

$commodityListData = array();

foreach ($commoditySummaryList['commodities'] as $commodity) {
    if ($commodity->amount < 0.1) {
        continue;
    }

    $commodityListData[] = array(
        'commodityId' => $commodity->commodityId,
        'name' => $commodity->name,
        'unit' => $commodity->unit,
        'availableAmount' => $commodity->amount,
        'code' => $commodity->code,
        'amount' => '',
        'value' => $commodity->value,
        'pricePerUnit' => $commodity->pricePerUnit,
        'currencySymbol' => getCurrency()
    );
}

$data = array(
    'transferCommodityList' => $commodityListData,
    'stockList' => $stockListData,
    'nameStock' => $stockManager->getNameStockByStockId($stockId),
    'issuedAt' => date\DateFormatter::formatToSql(date\HpCalendar::getToday()),
    'sourceStockId' => $stockId
);

$t->set_var(array('stockTransferData' => htmlspecialchars(json_encode($data))));

$t->set_global_var_to_template();
$t->pparse("out", "tmt");
exit;
