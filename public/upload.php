<?php
header("Content-Type: text/html; charset=utf-8");

require_once '../conf/commonApp.php';

doInit();
core\AuthenticationManager::verifyTheAuthenticity($_authenticationCode, $_loggedAppUserId);
$authorizationManager = new core\AuthorizationManager($_loggedAppUser);
if (!$authorizationManager->hasRightToPage(core\RightName::import)) { core\HttpUtils::redirectAndExit(); }
$rights = $authorizationManager->getRights();
$hasAccessAllStocks = $authorizationManager->hasFullAccessToPage('allStocks');

$importer = Importer::createForFoodBank($_loggedAppUser->foodBankId);

if (!empty($_FILES)) {
    $tempPath = $_FILES['file']['tmp_name'];
    $importer->importFrom($_loggedAppUser->currentStockId, $tempPath);
    @unlink($tempPath);
}

core\HttpUtils::sendSuccessfulEmptyAjaxResponseAndExit();
