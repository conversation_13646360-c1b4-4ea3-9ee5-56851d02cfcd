<?php
header("Content-Type: text/html; charset=utf-8");

require_once '../conf/commonApp.php';

doInit();
core\AuthenticationManager::verifyTheAuthenticity($_authenticationCode, $_loggedAppUserId);
$authorizationManager = new core\AuthorizationManager($_loggedAppUser);
if (!$authorizationManager->hasRightToPage(core\RightName::stockTransfersList)) {
    core\HttpUtils::redirectAndExit();
}

$fullAccessToStockTransferList = true; // TODO doresit pravo

if ($fullAccessToStockTransferList) {
    $postData = json_decode(file_get_contents('php://input'), true);

    $stockTransferData = (object) $postData['stockTransfer'];
    $transferId = $stockTransferData->transferId;
    $stockTransfer = form\StockTransfer::createForFoodBank($_loggedAppUser->foodBankId);
    $stockTranferToBeSaved = array(
            'transferId' => $transferId,
            'note' => $stockTransferData->note,
    );
    $stockTransfer->saveStockTransfer($stockTranferToBeSaved);

    $transferCommoditiesToBeSaved = array();
    foreach ($stockTransferData->transferCommodityList as $transferCommodity) {

        $transferCommodity = (object) $transferCommodity;
        if (empty($transferCommodity->amount)) {
            continue;
        }
        $amount = core\HpNumberFormatter::replaceCommaToDotInNumber($transferCommodity->amount);
        $transferCommodity->amount = $amount;
        $transferCommodity->transferId = $transferId;
        $transferCommoditiesToBeSaved[] = $transferCommodity;
    }

    $tranferCommodity = form\TransferCommodity::createForFoodBank($_loggedAppUser->foodBankId);
    $tranferCommodity->removeTransferCommodityByTransferId($transferId);
    $tranferCommodity->saveTransferCommodity($transferCommoditiesToBeSaved);

    $filterList = $postData['filterList'];
    $stockTransfer->setStockByAppUser($_loggedAppUserId);
    $stockTransfer->setDateFromFilter($filterList['dateFrom']);
    $stockTransfer->setDateToFilter($filterList['dateTo']);
    $stockTransfer->setStockTransferIdToFilter($filterList['stockTransfersId']);
    $stockTransfersListData = $stockTransfer->getStockTransfersList();
    $result = array('stockTransfersListData' => $stockTransfersListData);
    core\HttpUtils::sendSuccessfulJsonAjaxResponseAndExit($result);
}
