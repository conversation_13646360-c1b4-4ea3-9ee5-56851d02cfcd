<?php
header("Content-Type: text/html; charset=utf-8");

require_once '../conf/commonApp.php';

doInit();
core\AuthenticationManager::verifyTheAuthenticity($_authenticationCode, $_loggedAppUserId);
$authorizationManager = new core\AuthorizationManager($_loggedAppUser);

if (!$authorizationManager->hasRightToPage(core\RightName::supportedPersonList)) {
    core\HttpUtils::redirectAndExit();
}

$rights = $authorizationManager->getRights();
$fullAccessToSupportedPersonList = $authorizationManager->hasFullAccessToPage(core\RightName::supportedPersonList);
$foodBankManager = party\FoodBankManager::createForFoodBank($_loggedAppUser->foodBankId);
$lockoutDateForSupportedPersons = $foodBankManager->getLockoutDateForSupportedPersons();
$supportedPersonManager = form\SupportedPersonManager::createForFoodBank($_loggedAppUser->foodBankId);
$yearListData = date\DateUi::buildYearList(START_YEAR, END_YEAR);
$monthListData = date\DateUi::buildMonthList();

if ($authorizationManager->isUser()) {
    $customerId = $_loggedAppUser->customerId;
} else {
    $customerId = $_REQUEST['customerId'];
}

if (isset($_POST['save']) and $fullAccessToSupportedPersonList) {
    $supportPersonList = json_decode($_POST['supportPersonList']);
    $year = $supportPersonList->selectedYear->id;
    $month = $supportPersonList->selectedMonth->id;
    $supportedPersonManager->saveSupportedPersons($supportPersonList->supportedPersons, $customerId, $year, $month);
    setBasicInformationInPage();
} else {
    if ($authorizationManager->isUser()) {
        $year = date\HpCalendar::getActualYear();
        $month = date\HpCalendar::getPreviousMonth();
    } else {
        $period = $_GET['period'];
        $periodDate = date\Date::createBySqlDate($period);
        $year = $periodDate->getYear();
        $month = $periodDate->getMonth();
    }
}

$t = new ui\Template("./lang/cs");
$t->set_file("tmt", "supportedPersonList.tmt");

$supportedPersons = $supportedPersonManager->getSupportedPersonListByPeriodAndCustomerId($year, $month, $customerId);

$supportPersonListData = array(
    'selectedYear' => '',
    'selectedMonth' => '',
    'supportedPersons' => $supportedPersons,
    'lockoutDateForSupportedPersons' => $lockoutDateForSupportedPersons,
    'today' => date\DateFormatter::formatToSql(date\HpCalendar::getToday()),
    'isUser' => $authorizationManager->isUser() ? true : false
);

$t->set_var(array(
    'supportPersonListData' => htmlspecialchars(json_encode($supportPersonListData)),
    'yearListData' => htmlspecialchars(json_encode($yearListData)),
    'monthListData' => htmlspecialchars(json_encode($monthListData)),
    'initYear' => $year,
    'initMonth' => $month,
    'isUser' => $authorizationManager->isUser()?ui\Visibility::show : ui\Visibility::hide,
    'customerId' => $customerId
));

$t->set_global_var_to_template();
$t->pparse("out", "tmt");
