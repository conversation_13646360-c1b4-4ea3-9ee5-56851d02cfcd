angular.module('PBanka', ['mgcrea.ngStrap', 'hpBanka', 'hpBankaMenu', 'commodityAmount', 'stockSelectList', 'numberLimits', 'selectPicker'])

    .controller('StockReleaseNoteListController', function ($scope, $http, $timeout, $window) {
        $scope.showEdit = false;
        $scope.totalAmount = 0;
        $scope.showStockReleaseNoteList = true;
        $scope.regNumber = /^(0|[1-9][0-9]*)((\.[0-9]*)|(\,[0-9]*))?$/;


        $scope.countAmount = function () {
            var formTotalAmount = 0.0;
            angular.forEach($scope.commodityList, function (commodity) {
                var amount = commodity.amount !== undefined ? commodity.amount.toString().replace(/,/, ".") : '0.0';
                if (!Number.isNaN(parseFloat(amount))) {
                    formTotalAmount += parseFloat(amount);
                }
            });
            $scope.receivedStockReleaseNote.formTotalAmount = formTotalAmount;
        };

        $scope.goToReceiptToStockList = function (filledFormId) {
            $window.location.replace('receiptToStockList.php?filledFormId=' + filledFormId);
        };

        $scope.showFirstStockReleaseNote = function (stockReleaseNoteId) {
            if (stockReleaseNoteId > 0) {
                $scope.editStockReleaseNote(false, stockReleaseNoteId);
            }
        };

        $scope.editStockReleaseNote = function (isCustomerArchived, stockReleaseNoteId) {
            if (!isCustomerArchived) {
                $http.get('getStockReleaseNote.php?stockReleaseNoteId=' + encodeURIComponent(stockReleaseNoteId))
                    .success(function (result) {
                        $scope.receivedStockReleaseNote = result.stockReleaseNote;
                        $scope.commodityList = result.commodityList;
                        $scope.customerList = result.customerList;
                        $scope.countAmount();
                        $scope.current = {customer: $scope.receivedStockReleaseNote};
                    })
                    .error(function (data) {
                    });

                $scope.showEdit = true;
            }
        };

        $scope.showStockReleaseNote = function (isCustomerArchived, stockReleaseNoteId) {
            $scope.editStockReleaseNote(isCustomerArchived, stockReleaseNoteId);
        };

        $scope.canEdit = function () {
            return $scope.rightList.stockReleaseNoteList.fullAccess;
        };

        $scope.canSave = function () {
            return ($scope.stockReleaseNoteForm.$valid);
        };

        $scope.canCreateDonationAreement = function () {
            var countDonationAgreement = 0;
            angular.forEach($scope.stockReleaseNoteList, function (stockReleaseNote) {
                if (stockReleaseNote.donationAgreementId > 0) {
                    countDonationAgreement++;
                }
            });

            return ($scope.stockReleaseNoteList.length > 0 && $scope.filterList.dateFrom != null && $scope.filterList.dateTo != null && $scope.stockReleaseNoteList.length != countDonationAgreement) ? false : true;
        };

        $scope.countTotalItems = function () {
            var stockReleaseNoteTotalAmount = 0.0;
            angular.forEach($scope.stockReleaseNoteList, function (stockReleaseNote) {
                stockReleaseNoteTotalAmount += parseFloat(stockReleaseNote.totalAmount);
            });

            $scope.totalAmount = stockReleaseNoteTotalAmount;
        };

        $scope.backToList = function () {
            $scope.showStockReleaseNoteList = true;
        };

        $scope.saveReleaseNote = function () {
            var request = $http({
                method: "POST",
                url: "saveReleaseNote.php",
                data: {
                    receivedStockReleaseNote: $scope.receivedStockReleaseNote,
                    commodityList: $scope.commodityList,
                    filterList: $scope.filterList,
                    customerId: $scope.current.customer.customerId
                },
                headers: {'Content-Type': 'application/x-www-form-urlencoded'}
            });

            request.success(function (result) {
                $scope.stockReleaseNoteList = result.stockReleaseNoteListData;
                $scope.showEdit = false;
            });
        };

        $scope.changeStock = function (newCurrentStock) {
            var requestChangeStock = $http({
                method: "POST",
                url: "saveCurrentStock.php",
                data: {currentStockId: newCurrentStock.stockId},
                headers: {'Content-Type': 'application/x-www-form-urlencoded'}
            });

            requestChangeStock.success(function (result) {
                var request = $http({
                    method: "POST",
                    url: "getStockReleaseNoteList.php",
                    data: {filterList: $scope.filterList},
                    headers: {'Content-Type': 'application/x-www-form-urlencoded'}
                });

                request.success(function (result) {
                    $scope.stockReleaseNoteList = result.stockReleaseNoteListData;
                    $scope.showEdit = false;
                });
            });
        }
    });

