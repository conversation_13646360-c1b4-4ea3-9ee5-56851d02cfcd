angular.module('PBanka', ['mgcrea.ngStrap', 'hpBanka', 'hpBankaMenu', 'stockSelectList', 'transferCommodityAmount', 'numberLimits'])

    .controller('StockTransfersListController', function ($scope, $http, $timeout, $window, $modal) {
    	$scope.stockTransfer = [];
        $scope.showEdit = false;
        $scope.totalAmount = 0;
        $scope.showStockTransfersList = true;
        $scope.regNumber = /^(0|[1-9][0-9]*)((\.[0-9]*)|(\,[0-9]*))?$/;

    	var confirmDeleteModal = $modal({scope: $scope, templateUrl: 'lang/cs/modalDeleteTransferConfirm.tpl.html', title: '', content: '', show: false});

    	$scope.showModal = function(transferId) {
    		$scope.transferId = transferId;
    		confirmDeleteModal.$promise.then(confirmDeleteModal.show);
    	};

    	$scope.goToStockTransfer = function(currentStock) {
    		if ($scope.currentStock.stockId > 0) {
    			$window.location='stockTransfer.php?stockId=' + $scope.currentStock.stockId;
    		}
    	}

        $scope.countAmount = function () {
            var stockTransferTotalAmount = 0.0;
            angular.forEach($scope.stockTransfer.transferCommodityList, function (commodity) {
                var amount = commodity.amount !== undefined ? commodity.amount.toString().replace(/,/, ".") : '0.0';
                if (!Number.isNaN(parseFloat(amount))) {
                	stockTransferTotalAmount += parseFloat(amount);
                }
            });
            $scope.stockTransferTotalAmount = stockTransferTotalAmount;
        };
/*
        $scope.goToReceiptToStockList = function (filledFormId) {
            $window.location.replace('receiptToStockList.php?filledFormId=' + filledFormId);
        };
*/
        /* TODO asi se to pouziva proverit
        $scope.showFirstStockReleaseNote = function (stockReleaseNoteId) {
            if (stockReleaseNoteId > 0) {
                $scope.editStockReleaseNote(stockReleaseNoteId);
            }
        };
*/
        $scope.editStockTransfer = function (transferId) {
             $http.get('getTransfer.php?transferId=' + encodeURIComponent(transferId))
                 .success(function (result) {
                	 $scope.stockTransfer = result.stockTransferData;
                     $scope.countAmount();
                })
                 .error(function (data) {
                 });

            $scope.showEdit = true;
        };


/*
        $scope.canEdit = function () {
            return $scope.rightList.stockReleaseNoteList.fullAccess;
        };
*/
        $scope.canSave = function () {
            return ($scope.stockTransferForm.$valid);
        };


        $scope.countTotalAmount = function () {
            var totalAmount = 0.0;
            angular.forEach($scope.stockTransfersList, function (stockTransfer) {
                totalAmount += parseFloat(stockTransfer.totalAmount);
            });

            $scope.totalAmount = totalAmount;
        };

    	$scope.inicializeSelectedTargetStock = function(selectedTargetStockId) {
    		for (var i=0; i<$scope.targetStockList.length; i++) {
    			if ($scope.targetStockList[i].stockId == selectedTargetStockId) {
    				$scope.filterList.stockId = $scope.targetStockList[i].stockId;
    		    }
    		}
    	};

/*
        $scope.backToList = function () {
            $scope.showStockTransfersList = true;
        };
*/
        $scope.saveTransferCommodity = function () {

            const request = $http({
                method: "POST",
                url: "saveTransferCommodity.php",
                data: {
                	stockTransfer: $scope.stockTransfer,
                    filterList: $scope.filterList,
                },
                headers: {'Content-Type': 'application/x-www-form-urlencoded'}
            });

            request.success(function (result) {
                $scope.stockTransfersList = result.stockTransfersListData;
                $scope.showEdit = false;
            }).error(function (data) {
            });

        };

        $scope.changeStock = function (newCurrentStock) {
            const requestChangeStock = $http({
                method: "POST",
                url: "saveCurrentStock.php",
                data: {currentStockId: newCurrentStock.stockId},
                headers: {'Content-Type': 'application/x-www-form-urlencoded'}
            });

            requestChangeStock.success(function (result) {
                $scope.filterList.stockId = newCurrentStock.stockId;

                const request = $http({
                    method: "POST",
                    url: "getStockTransfersList.php",
                    data: {filterList: $scope.filterList},
                    headers: {'Content-Type': 'application/x-www-form-urlencoded'}
                });

                request.success(function (result) {
                    $scope.stockTransfersList = result.stockTransfersListData;
                    $scope.showEdit = false;
                }).error(function (data) {
                });
            });
            requestChangeStock.error(function (data) {
            });

        }
    });
