<?php
// přehled aktivních formulářů

header("Content-Type: text/html; charset=utf-8");

require_once '../conf/commonApp.php';

doInit();
core\AuthenticationManager::verifyTheAuthenticity($_authenticationCode, $_loggedAppUserId);
$authorizationManager = new core\AuthorizationManager($_loggedAppUser);
$rights = $authorizationManager->getRights();
if (!$authorizationManager->hasRightToPage(core\RightName::activeFormSelector)) {
    core\HttpUtils::redirectAndExit();
}

$showNoForm = false;

$formManager = form\FormManager::createForFoodBank($_loggedAppUser->foodBankId);
$sqlToday = date("Y-m-d");
$forms = $formManager->getActiveFormListForDirectConsumption($sqlToday);

$formCount = count($forms);

if ($formCount == 0) {
    $showNoForm = true;
    // neni formular k vyplneni
    //	header('Location: sentFormList.php');
    //	exit;
} elseif ($formCount == 1) {
    // je jen jeden formular, netreba zobrazovat seznam k vyberu
    $form = $forms->fetch();
    $url = "formFiller.php?formId=" . $form->formId;
    header('Location: ' . $url);
    exit;
}

$t = new ui\Template("./lang/cs");
$t->set_file("tmt", "activeFormSelector.tmt");

$formOrder = 0;

foreach ($forms as $form) {

    $formOrder++;

    $formListData[] = array(
        'formId' => $form->formId,
        'order' => $formOrder,
        'actionName' => $form->actionName,
        'validTo' => date('j.n.Y', strtotime($form->validTo)),
        'editedItem' => false,
        'showEdit' => false
    );
}

$t->set_var(array(
    'formListData' => htmlspecialchars(json_encode($formListData)),
    'showNoForm' => $showNoForm ? 1 : 0,
    'rightListData' => htmlspecialchars(json_encode($rights))
));


$t->set_global_var_to_template();
$t->pparse("out", "tmt");

exit;