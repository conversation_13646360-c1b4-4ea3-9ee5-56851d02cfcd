angular.module('PBanka', ['mgcrea.ngStrap','hpBankaMenu','stockSelectList', 'angularFileUpload'])

.controller('ImportController', function($scope, $http, $modal, $window, FileUploader) {
	
	var uploader = $scope.uploader = new FileUploader({
         url: 'upload.php'
     });
	
    // FILTERS
    // a sync filter
    uploader.filters.push({
        name: 'syncFilter',
        fn: function(item /*{File|FileLikeObject}*/, options) {
            return this.queue.length < 10;
        }
    });
  
    // an async filter
    uploader.filters.push({
        name: 'asyncFilter',
        fn: function(item /*{File|FileLikeObject}*/, options, deferred) {
            setTimeout(deferred.resolve, 1e3);
        }
    });

    // CALLBACKS

    uploader.onWhenAddingFileFailed = function(item /*{File|FileLikeObject}*/, filter, options) {
    };
    uploader.onAfterAddingFile = function(fileItem) {
    };
    uploader.onAfterAddingAll = function(addedFileItems) {
    };
    uploader.onBeforeUploadItem = function(item) {
    };
    uploader.onProgressItem = function(fileItem, progress) {
    };
    uploader.onProgressAll = function(progress) {
    };
    uploader.onSuccessItem = function(fileItem, response, status, headers) {
    };
    uploader.onErrorItem = function(fileItem, response, status, headers) {
    };
    uploader.onCancelItem = function(fileItem, response, status, headers) {
    };
    uploader.onCompleteItem = function(fileItem, response, status, headers) {
    };
    uploader.onCompleteAll = function() {
        $window.location.reload();
    };
	
	var removeImportedDataConfirmationDialog = $modal({scope: $scope, templateUrl: 'lang/cs/removeImportedDataConfirmationDialog.tpl.html', title: '', content: '', show: false});
	
	$scope.showRemoveImportedDataConfirmationDialog = function(customerId, issuedAt) {
		$scope.customerId = customerId;
		$scope.issuedAt = issuedAt;
		removeImportedDataConfirmationDialog.$promise.then(removeImportedDataConfirmationDialog.show);
	};	
	
	$scope.changeStock = function(newCurrentStock) {
		$http.get('saveCurrentStockId.php?currentStockId=' + encodeURIComponent(newCurrentStock.stockId))
        .success(function(result) {
        })
        .error(function(data) {
        });
	}	
	
	$scope.removeImportedDataFor = function(customerId, issuedAt) {
		$http.post('removeImportedData.php', {customerId: customerId, issuedAt: issuedAt})
	        .success(function(result) {
	        	delete($scope.importedData[customerId + '_' + issuedAt]);
	        })
	        .error(function(data, status) {
	        });
	}	
	
	$scope.canFill = function() {
		return ($scope.currentStock.stockId > 0);
	};
});

