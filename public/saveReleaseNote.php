<?php
header("Content-Type: text/html; charset=utf-8");

require_once '../conf/commonApp.php';

doInit();
core\AuthenticationManager::verifyTheAuthenticity($_authenticationCode, $_loggedAppUserId);
$authorizationManager = new core\AuthorizationManager($_loggedAppUser);

if (!$authorizationManager->hasRightToPage(core\RightName::stockReleaseNoteList)) {
    core\HttpUtils::redirectAndExit();
}

$fullAccessToStockReleaseNoteList = $authorizationManager->hasFullAccessToPage(core\RightName::stockReleaseNoteList);

if ($fullAccessToStockReleaseNoteList) {
    $postData = json_decode(file_get_contents('php://input'), true);

    $receivedStockReleaseNote = (object) $postData['receivedStockReleaseNote'];
    $stockReleaseNote = form\StockReleaseNote::createForFoodBank($_loggedAppUser->foodBankId);
    $stockReleaseNoteId = intval($receivedStockReleaseNote->stockReleaseNoteId);
    $customerId = intval($postData['customerId']);
    $stockReleaseNoteData = array(
        'issuedAt' => $receivedStockReleaseNote->issuedAt,
        'customerId' => $customerId,
        'formId' => intval($receivedStockReleaseNote->formId),
        'foodBankStockReleaseNoteId' => $receivedStockReleaseNote->foodBankStockReleaseNoteId,
        'stockReleaseNoteId' => $stockReleaseNoteId,
        'appUserId' => $_loggedAppUserId,
        'filledFormId' => $receivedStockReleaseNote->filledFormId,
        'note' => $receivedStockReleaseNote->note,
        'totalAmount' => $receivedStockReleaseNote->formTotalAmount * 1000
    );

    $customerIdBeforeUpdate = $stockReleaseNote->getCustomerIdByStockReleaseNoteId($stockReleaseNoteId);

    if ($customerIdBeforeUpdate != $customerId) {
        $customerManager = new party\CustomerManager();
        $customer = $customerManager->getCustomerByCustomerId($customerId);
        $customerData = array(
            'customerName' => $customer->name,
            'customerAddress' => $customer->address,
            'customerIC' => $customer->IC
        );
        $stockReleaseNoteToBeSaved = array_merge($stockReleaseNoteData, $customerData);
    } else {
        $stockReleaseNoteToBeSaved = $stockReleaseNoteData;
    }
    $stockReleaseNote->saveStockReleaseNote($stockReleaseNoteToBeSaved);

    $commodityList = (object) $postData['commodityList'];
    $commoditiesToBeSaved = array();

    foreach ($commodityList as $commodity) {
        $commodity = (object) $commodity;

        if (empty($commodity->amount)) {
            continue;
        }

        $amount = core\HpNumberFormatter::replaceCommaToDotInNumber($commodity->amount);
        $commodity->amount = $amount;
        $commodity->stockReleaseNoteId = $stockReleaseNoteId;
        $commodity->filledFormId = $receivedStockReleaseNote->filledFormId;
        $commoditiesToBeSaved[] = $commodity;
    }

    $stockReleaseNoteCommodity = form\StockReleaseNoteCommodity::createForFoodBank($_loggedAppUser->foodBankId);
    $stockReleaseNoteCommodity->removeStockReleaseNoteCommodityByStockReleaseNoteId($stockReleaseNoteId);
    $stockReleaseNoteCommodity->saveStockReleaseNoteCommodity($commoditiesToBeSaved);

    if ($receivedStockReleaseNote->filledFormId) {
        $filledFormCommodity = form\FilledFormCommodity::createForFoodBank($_loggedAppUser->foodBankId);
        $filledFormCommodity->removeFilledFormCommodityByFilledFormId($receivedStockReleaseNote->filledFormId);
        $filledFormCommodity->saveFilledFormCommodity($commoditiesToBeSaved);

        $filledForm = form\FilledForm::createForFoodBank($_loggedAppUser->foodBankId);
        $filledForm->updateFilledFormNote($receivedStockReleaseNote->note, $receivedStockReleaseNote->filledFormId);
        $filledForm->updateTotalAmount($receivedStockReleaseNote->formTotalAmount * 1000, $receivedStockReleaseNote->filledFormId);
    }

    $filterList = $postData['filterList'];

    $stockReleaseNote = form\StockReleaseNote::createForFoodBank($_loggedAppUser->foodBankId);
    $stockReleaseNote->setStockByAppUser($_loggedAppUserId);
    $stockReleaseNote->setCustomerNameFilter($filterList['customer']);
    $stockReleaseNote->setDateFromFilter($filterList['dateFrom']);
    $stockReleaseNote->setDateToFilter($filterList['dateTo']);
    $stockReleaseNoteListData = $stockReleaseNote->getStockReleaseNoteList();

    $result = array('stockReleaseNoteListData' => $stockReleaseNoteListData);
    core\HttpUtils::sendSuccessfulJsonAjaxResponseAndExit($result);
}
