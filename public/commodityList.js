angular.module('PBanka', ['hpBankaMenu'])

.controller('CommodityListController', function($scope, $window) {

	$scope.showEdit = false;

	$scope.editCommodity = function(commodityId) {

		angular.forEach($scope.commodityList, function(commodity) {
			if (commodity.commodityId == commodityId) {
				$scope.commodity = commodity;
			}
		});

		$scope.showEdit = true;
		return;
	};

	$scope.addCommodity = function() {

		$scope.commodity = [{code: '', name: '', pricePerUnit: '', commodityId: 0 }];
		$scope.showEdit = true;
		return;
	};

	$scope.canSave = function() {
		return $scope.commodityListForm.$valid;
	};

	$scope.reverse = true;
	$scope.sordBy = 'code';
	$scope.order = function(sordBy) {
		$scope.reverse = ($scope.sordBy === sordBy) ? !$scope.reverse : false;
	    $scope.sordBy = sordBy;
	};

});
