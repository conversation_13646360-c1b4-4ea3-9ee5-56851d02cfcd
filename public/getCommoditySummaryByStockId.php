<?php
header("Content-Type: text/html; charset=utf-8");

require_once '../conf/commonApp.php';

doInit();
core\AuthenticationManager::verifyTheAuthenticity($_authenticationCode, $_loggedAppUserId);
$authorizationManager = new core\AuthorizationManager($_loggedAppUser);
if (!$authorizationManager->hasRightToPage(core\RightName::supply)) { core\HttpUtils::redirectAndExit(); }
$rights = $authorizationManager->getRights();

$dateTo = date('Y-m-d');
$stockId = !empty($_GET['stockId']) ? $_GET['stockId'] : 0;

$reportManager = report\ReportManager::createForFoodBank($_loggedAppUser->foodBankId);
$commoditySummaryData = $reportManager->getStocks($dateTo, null, $stockId);

$commoditySummaryListData = array('commoditySummary' => $commoditySummaryData);
core\HttpUtils::sendSuccessfulJsonAjaxResponseAndExit($commoditySummaryListData);
