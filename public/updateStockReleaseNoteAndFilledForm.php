<?php
header("Content-Type: text/html; charset=utf-8");

require_once '../conf/commonApp.php';

doInit();

echo "Started UPDATE StockReleaseNote.";

// SELECT DISTINCT "foodBankId", "customerId" FROM "StockReleaseNote" GROUP BY "foodBankId", "customerId"
$query = array();
$query[] = 'SELECT DISTINCT [foodBankId], [customerId] FROM [StockReleaseNote] GROUP BY [foodBankId], [customerId]';
$list = \dibi::query($query);

foreach ($list as $row) {
    
    $query = array();
    $query[] = 'UPDATE [StockReleaseNote]';
    $query[] = 'SET ([customerName],[customerAddress],[customerIC])=(SELECT [name],[address],[IC] FROM [Customer]';
    array_push($query, ' WHERE [foodBankId] = %i', $row->foodBankId);
    array_push($query, ' AND [customerId] = %i', $row->customerId);
    array_push($query, ') WHERE [foodBankId] = %i', $row->foodBankId);
    array_push($query, ' AND [customerId] = %i', $row->customerId);
    \dibi::query($query);
    
    $query = array();
    $query[] = 'UPDATE [StockReleaseNote]';
    $query[] = 'SET ([foodBankName],[foodBankAddress],[foodBankIC])=(SELECT [name],[address],[ic] FROM [FoodBank]';
    array_push($query, ' WHERE [foodBankId] = %i', $row->foodBankId);
    array_push($query, ') WHERE [foodBankId] = %i', $row->foodBankId);
    \dibi::query($query);
    
}

echo "Finished UPDATE StockReleaseNote.";

echo "Started UPDATE FilledForm.";


// SELECT DISTINCT "foodBankId", "supplierId" FROM "FilledForm" GROUP BY "foodBankId", "supplierId"
$query = array();
$query[] = 'SELECT DISTINCT [foodBankId], [supplierId] FROM [FilledForm] GROUP BY [foodBankId], [supplierId]';
$list = \dibi::query($query);

foreach ($list as $row) {
    
    $query = array();
    $query[] = 'UPDATE [FilledForm]';
    $query[] = 'SET ([supplierName],[supplierAddress],[supplierIC])=(SELECT [name],[address],[IC] FROM [Supplier]';
    array_push($query, ' WHERE [foodBankId] = %i', $row->foodBankId);
    array_push($query, ' AND [supplierId] = %i', $row->supplierId);
    array_push($query, ') WHERE [foodBankId] = %i', $row->foodBankId);
    array_push($query, ' AND [supplierId] = %i', $row->supplierId);
    \dibi::query($query);

    $query = array();
    $query[] = 'UPDATE [FilledForm]';
    $query[] = 'SET ([foodBankName],[foodBankAddress],[foodBankIC])=(SELECT [name],[address],[ic] FROM [FoodBank]';
    array_push($query, ' WHERE [foodBankId] = %i', $row->foodBankId);
    array_push($query, ') WHERE [foodBankId] = %i', $row->foodBankId);
    \dibi::query($query);

}

echo "Finished UPDATE FilledForm.";