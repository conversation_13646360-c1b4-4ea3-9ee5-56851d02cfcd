angular.module('PBanka', ['hpBankaMenu','stockSelectList'])

.controller('OutputFromStockListController', function($scope, $http, $window) {
	
	$scope.changeStock = function(newCurrentStock) {
		
		var requestChangeStock = $http({
            method: "POST",
            url: "saveCurrentStock.php",
            data: {currentStockId : newCurrentStock.stockId},
			headers : {'Content-Type': 'application/x-www-form-urlencoded'}
        });
		
		requestChangeStock.success(function(result) {
        })
        requestChangeStock.error(function(data) {
        });
	}	
	
	$scope.canFill = function() {
		return ($scope.currentStock.stockId > 0);
	};
	
	$scope.goToOutputFromStock = function(formId) {

		if ($scope.currentStock.stockId > 0) {
			$window.location='outputFromStock.php?formId=' + formId  + '&customerId=' + $scope.dataForOutputFromStock.customerId + '&issuedAt=' + $scope.dataForOutputFromStock.issuedAt + '&import=' + $scope.dataForOutputFromStock.import;
		}
	}
});

