angular.module('PBanka', ['hpBankaMenu', 'angularFileUpload'])

    .controller('SettingFoodBankController', function ($scope, $window, FileUploader) {

        if (sessionStorage.getItem('showStampAndSignatureSave')) {
            $scope.showStampAndSignatureSave = true;
            sessionStorage.removeItem('showStampAndSignatureSave');
        } else {
            $scope.showStampAndSignatureSave = false;
        }

        if (sessionStorage.getItem('showStampAndSignatureErrorSave')) {
            $scope.showStampAndSignatureErrorSave = true;
            sessionStorage.removeItem('showStampAndSignatureErrorSave');
        } else {
            $scope.showStampAndSignatureErrorSave = false;
        }


        var uploader = $scope.uploader = new FileUploader({
            url: 'uploadStampAndSignature.php',
            queueLimit: 1, // only allow one file to be uploaded at a time
            filters: [
                {
                    name: 'imageFilter',
                    fn: function (item /*{File|FileLikeObject}*/, options) {
                        var type = '|' + item.type.slice(item.type.lastIndexOf('/') + 1) + '|';
                        console.log(type);
                        return '|jpeg|png|'.indexOf(type) !== -1;
                    }
                }
            ]
        });
        uploader.onCompleteItem = function (fileItem, response, status, headers) {
            if (response.uploadOkStampAndSignature) {
                sessionStorage.setItem('showStampAndSignatureSave', true);
            } else {
                sessionStorage.setItem('showStampAndSignatureErrorSave', true);
            }
        };

        uploader.onErrorItem = function (fileItem, response, status, headers) {
            console.info('onErrorItem', fileItem, response, status, headers);
        };

        uploader.onCompleteAll = function () {
            $window.location.href = $window.location.href;
        };

        $scope.canSave = function () {
            return $scope.settingFoodBankForm.$valid;
        };
    });