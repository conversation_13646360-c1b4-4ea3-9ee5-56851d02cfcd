<?php
// číselník odběratelů um<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, z<PERSON><PERSON><PERSON> a <PERSON> odběratele (data: název, adresa, kontaktní osoba, email, login, heslo, telefon, ID odběratele)

header("Content-Type: text/html; charset=utf-8");

require_once '../conf/commonApp.php';

doInit();

core\AuthenticationManager::verifyTheAuthenticity($_authenticationCode, $_loggedAppUserId);
$authorizationManager = new core\AuthorizationManager($_loggedAppUser);

if (!$authorizationManager->hasRightToPage(core\RightName::customerList)) {
    core\HttpUtils::redirectAndExit();
}

$rights = $authorizationManager->getRights();
$fullAccessToCustomerList = $authorizationManager->hasFullAccessToPage(core\RightName::customerList);

$customerManager = new party\CustomerManager();
$appUserManager = new core\AppUserManager();

if (isset($_POST['filter'])) {
    $filterListData = array(
        'name' => $_POST['filterByName'],
        'contactPerson' => $_POST['filterByContactPerson'],
        'groupName' => $_POST['filterByGroupName'],
        'login' => $_POST['filterByLogin']
    );
} else {
    $filterListData = array(
        'name' => '',
        'contactPerson' => '',
        'groupName' => '',
        'login' => ''
    );
}

if ($_GET['delete'] and $fullAccessToCustomerList) {
    $customerManager->deleteCustomer($_GET['customerId'], $_loggedAppUser->foodBankId);
    $appUserManager->deleteAppUserByCustomerId($_GET['customerId'], $_loggedAppUser->foodBankId);
}

if (isset($_POST['save']) and $fullAccessToCustomerList) {

    $password = trim($_POST['password']);
    $customerToBeSaved = array(
        'name' => $_POST['name'],
        'contactPerson' => $_POST['contactPerson'],
        'address' => $_POST['address'],
        'email' => $_POST['email'],
        'phone' => $_POST['phone'],
        'groupName' => $_POST['groupName'],
        'IC' => trim(substr(core\HpNumberFormatter::removeGaps($_POST['IC']), 0, 8)),
        'customerId' => $_POST['customerId'],
        'foodBankId' => $_loggedAppUser->foodBankId,
        'appUserId' => $_loggedAppUserId
    );

    $isCompanyReporter = isset($_POST['companyReporter']);
    $appUserType = core\AppUserRoleName::USER;

    if (empty($_POST['customerId']) && $isCompanyReporter) {
        $appUserType = core\AppUserRoleName::COMPANY_REPORTER;
    }

    $customerId = $customerManager->saveCustomer($customerToBeSaved);

    $appUserId = $_POST['appUserId'];
    $appUserToBeSaved = array(
        'login' => $_POST['login'],
        'customerId' => $customerId,
        'appUserId' => $appUserId,
        'foodBankId' => $_loggedAppUser->foodBankId
    );

    if (!empty($password)) {
        $appUserToBeSaved['password'] = md5($password);
    }

    if (empty($appUserId)) {
        $appUserToBeSaved['userType'] = $appUserType;
    }

    $appUserManager->saveAppUser($appUserToBeSaved);
}

$t = new ui\Template("./lang/cs");
$t->set_file("tmt", "customerList.tmt");

$customerManager->setCustomerNameFilter($filterListData['name']);
$customerManager->setContactPersonFilter($filterListData['contactPerson']);
$customerManager->setGroupNameFilter($filterListData['groupName']);
$customerManager->setLoginFilter($filterListData['login']);

if ($authorizationManager->isSuperAdmin()) {
    $customerManager->showCompanyReporters();
}

$customers = $customerManager->getCustomerList($_loggedAppUser->foodBankId);

foreach ($customers as $customer) {
    $customerId = $customer->customerId;
    $customerListData[] = array(
        'customerId' => $customerId,
        'appUserId' => $customer->appUserId,
        'name' => $customer->name,
        'contactPerson' => $customer->contactPerson,
        'address' => $customer->address,
        'email' => $customer->email,
        'phone' => $customer->phone,
        'groupName' => $customer->groupName,
        'IC' => core\HpNumberFormatter::toIC($customer->IC),
        'login' => $customer->login,
        'password' => '',
        'usedItem' => $customerManager->isCustomerUsed($customerId, $_loggedAppUser->foodBankId),
        'archived' => $customer->archived
    );
}

$t->set_var(array(
    'customerListData' => htmlspecialchars(json_encode($customerListData)),
    'filterListData' => htmlspecialchars(json_encode($filterListData)),
    'isLoggedUserSuperAdmin' => json_encode($authorizationManager->isSuperAdmin())
));
$t->set_global_var_to_template();
$t->pparse("out", "tmt");
