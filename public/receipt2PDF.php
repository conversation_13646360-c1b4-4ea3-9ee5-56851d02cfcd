<?php
require_once '../conf/commonApp.php';
require_once '../lib/thirdparty/vendor/autoload.php';

doInit();
core\AuthenticationManager::verifyTheAuthenticity($_authenticationCode, $_loggedAppUserId);
$authorizationManager = new core\AuthorizationManager($_loggedAppUser);
if (!$authorizationManager->hasRightToPage(core\RightName::receiptToStockList)) {
    core\HttpUtils::redirectAndExit();
}
$rights = $authorizationManager->getRights();
$filledForm = form\FilledForm::createForFoodBank($_loggedAppUser->foodBankId);
$filledFormIds = array();
$filledFormIds[] = $_GET['filledFormId'];
$receiptToStockList = array();

if (!empty($filledFormIds)) {
    $receiptToStockList = $filledForm->getReceiptToStockListByFilledFormIds($filledFormIds);
}

$receiptToStock = (object) $receiptToStockList[0];

$t = new ui\Template("./lang/cs");
$t->set_file("tmt", "receipt2PDF.tmt");
$t->set_block("tmt", "commoditiesList", "{tmtcommoditiesList}");
$t->set_block("tmt", "commodity", "{tmtcommodity}");

$creator = core\AppUserManager::createAppUser($receiptToStock->appUserId);

$t->set_var(array(
    'stockFilledFormId' => $receiptToStock->stockFilledFormId,
    'actionName' => $receiptToStock->actionName,
    'consumptionDate' => date\DateFormatter::formatToDateFormatAppUserOrEmptyString($receiptToStock->consumptionDate),
    'giftValue' => core\HpNumberFormatter::toMoney($receiptToStock->giftValue),
    'priceCalculation' => core\HpNumberFormatter::toMoney($receiptToStock->commodityListData['priceCalculation']),
    'totalAmount' => core\HpNumberFormatter::toAmount($receiptToStock->commodityListData['totalAmount']),
    'supplierName' => $receiptToStock->supplierName,
    'supplierAddress' => $receiptToStock->supplierAddress,
    'supplierIC' => $receiptToStock->supplierIC,
    'foodBankName' => $receiptToStock->foodBankName,
    'foodBankAddress' => $receiptToStock->foodBankAddress,
    'foodBankIC' => $receiptToStock->foodBankIC,
    'stockName' => $receiptToStock->stockName,
    'isNote' => $receiptToStock->isNote,
    'note' => $receiptToStock->note,
    'createdBy' => $creator->login . ' (' . $creator->localizedUserType . ')'
));

foreach ($receiptToStock->commodityListData['filledFormCommodityListData'] as $commodity) {
    $t->set_var(array(
        'name' => $commodity['name'],
        'amount' => core\HpNumberFormatter::toAmount($commodity['amount']),
        'code' => $commodity['code']
    ));
    $t->parse("tmtcommoditiesList", "commodity", true);
}

$t->set_global_var_to_template();
$receiptToStockInHTML = $t->finish($t->parse("out", "tmt"));

$pdf = new party\ReceiptAndIssue2PDF(PDF_PAGE_ORIENTATION, PDF_UNIT, PDF_PAGE_FORMAT, true, 'UTF-8', false);
$pdf->setLanguageArray($l);
$pdf->setHeaderData($_foodBank->logoName);
$pdf->setPrintHeader(true);
$pdf->setDefaultPDFLayout();

$html .= '<html><head></head><body><style>' . file_get_contents(__DIR__ . '/style/print2PDF.css') . '</style>';
$html .= $receiptToStockInHTML;
$pdf->writeHTML($html, true, false, true, false);

$receiptToStockNumber = str_pad($receiptToStock->stockFilledFormId, numberLengthInFilename, "0", STR_PAD_LEFT);
$pdf->Output('prijemka_' . $receiptToStockNumber . '.pdf', "I", "I");
exit;
