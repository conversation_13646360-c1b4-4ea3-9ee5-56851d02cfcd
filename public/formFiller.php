<?php
// stránka na vyplnění formuláře odběratelem (data: název formuláře, platnost od do, seznam komodit)

header("Content-Type: text/html; charset=utf-8");
require_once '../conf/commonApp.php';

doInit();
core\AuthenticationManager::verifyTheAuthenticity($_authenticationCode, $_loggedAppUserId);
$authorizationManager = new core\AuthorizationManager($_loggedAppUser);
$rights = $authorizationManager->getRights();

if (!$authorizationManager->hasRightToPage(core\RightName::formFiller)) {
    core\HttpUtils::redirectAndExit();
}

$t = new ui\Template("./lang/cs");
$t->set_file("tmt", "formFiller.tmt");

$formHasBeenFilledAndSent = !empty($_GET['formHasBeenFilledAndSent']);
$stockReleaseNoteId = intval($_GET['stockReleaseNoteId']);
$stockFilledFormId = intval($_GET['stockFilledFormId']);
$stockStockReleaseNoteId = intval($_GET['stockStockReleaseNoteId']);
$indirectly = intval($_GET['indirectly']);

$formManager = form\FormManager::createForFoodBank($_loggedAppUser->foodBankId);
$formId = $_GET['formId'];
$form = $formManager->getForm($formId);

$commodityManager = new form\CommodityManager();
$commodities = $commodityManager->getSelectedCommodityList($formId);

foreach ($commodities as $commodity) {
    $commodityListData[] = array(
        'commodityId' => $commodity->commodityId,
        'name' => $commodity->name,
        'unit' => $commodity->unit,
        'amount' => '',
        'pricePerUnit' => $commodity->pricePerUnit,
        'code' => $commodity->code
    );
}

$supplierManager = party\SupplierManager::createForFoodBank($_loggedAppUser->foodBankId);
$suppliersResult = $supplierManager->getSelectedSupplierList($formId);
$customer = party\CustomerManager::createCustomer($_loggedAppUser->customerId);
$customerManager = new party\CustomerManager();
$customersResult = $customerManager->getSelectedCustomerList($_loggedAppUser->foodBankId);

$formFillerListData = array(
    'actionName' => $form->actionName,
    'supplierId' => 1,
    'formId' => $form->formId,
    'formHasBeenFilledAndSent' => $formHasBeenFilledAndSent ? 1 : 0,
    'stockReleaseNoteId' => $stockReleaseNoteId,
    'stockFilledFormId' => $stockFilledFormId,
    'stockStockReleaseNoteId' => $stockStockReleaseNoteId,
    'minDate' => date("Y-m-d", strtotime("-" . $form->backDays . " days")),
    'customerEmail' => $customer->email,
    'giftValue' => null,
    'indirectly' => !empty($indirectly)
        
);

$t->set_var(array(
    'commodityListData' => htmlspecialchars(json_encode($commodityListData)),
    'supplierListData' => htmlspecialchars(json_encode($suppliersResult->fetchAll())),
    'customerListData' => htmlspecialchars(json_encode($customersResult->fetchAll())),
    'formFillerListData' => htmlspecialchars(json_encode($formFillerListData)),
    'minDate' => date("Y-m-d", strtotime("-" . $form->backDays . " days")),
    'rightListData' => htmlspecialchars(json_encode($rights)),
    'indirectly' => $indirectly
));

$t->set_global_var_to_template();
$t->pparse("out", "tmt");
