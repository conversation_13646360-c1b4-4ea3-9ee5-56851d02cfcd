<?php
header("Content-Type: text/html; charset=utf-8");

require_once '../conf/commonApp.php';

doInit();
core\AuthenticationManager::verifyTheAuthenticity($_authenticationCode, $_loggedAppUserId);
$authorizationManager = new core\AuthorizationManager($_loggedAppUser);
if (!$authorizationManager->hasRightToPage(core\RightName::receiptToStockList)) {
    core\HttpUtils::redirectAndExit();
}
$rights = $authorizationManager->getRights();
$fullAccessToReceiptToStockList = $authorizationManager->hasFullAccessToPage(core\RightName::receiptToStockList);
$canReadAllStocks = $authorizationManager->canRead('allStocks');

$filledFormManager = form\FilledForm::createForFoodBank($_loggedAppUser->foodBankId);

if (isset($_POST['filter'])) {
    $filterListData = array(
        'supplier' => $_POST['filterBySupplier'],
        'dateFrom' => empty($_POST['filterByDateFrom']) ? '' : date('Y-m-d', strtotime($_POST['filterByDateFrom'])),
        'dateTo' => empty($_POST['filterByDateTo']) ? '' : date('Y-m-d', strtotime($_POST['filterByDateTo'])),
        'stockFilledFormId' => $_POST['filterByStockFilledFormId'],
        'actionName' => $_POST['filterByActionName'],
        'filter' => 1
    );
} else {
    $today = date\HpCalendar::getToday();
    $dateFrom = date\HpCalendar::getFirstDayOfCurrentMonth();
    $dateTo = date\HpCalendar::getLastDayInMonth($today);

    $filterListData = array(
        'supplier' => '',
        'dateFrom' => date\DateFormatter::formatToSql($dateFrom),
        'dateTo' => date\DateFormatter::formatToSql($dateTo)
    );
}

$t = new ui\Template("./lang/cs");
$t->set_file("tmt", "receiptToStockList.tmt");

$filledForm = form\FilledForm::createForFoodBank($_loggedAppUser->foodBankId);
$filledForm->setStockByAppUser($_loggedAppUserId);
$filledForm->setSupplierNameFilter($filterListData['supplier']);
$filledForm->setDateFromFilter($filterListData['dateFrom']);
$filledForm->setDateToFilter($filterListData['dateTo']);
$filledForm->setStockFilledFormIdToFilter($filterListData['stockFilledFormId']);
$filledForm->setActionNameToFilter($filterListData['actionName']);
$receivedFormListData = $filledForm->getReceivedFormList();

$appUserStockRelation = form\AppUserStockRelation::createForFoodBank($_loggedAppUser->foodBankId);
$stockListData = $appUserStockRelation->getSelectedStockListByAppUser($_loggedAppUser->appUserId, $canReadAllStocks);

$t->set_var(array(
    'receivedFormListData' => htmlspecialchars(json_encode($receivedFormListData)),
    'filterListData' => htmlspecialchars(json_encode($filterListData)),
    'showInfoAboutCreateDonationAgreement' => 0,
    'stockListData' => htmlspecialchars(json_encode($stockListData)),
    'currentStockId' => is_null($_loggedAppUser->currentStockId) ? 'null' : $_loggedAppUser->currentStockId,
    'filledFormId' => isset($_GET['filledFormId']) ? $_GET['filledFormId'] : 0,
    'summaryItem' => $canReadAllStocks ? 'true' : 'false'
));

$t->set_global_var_to_template();
$t->pparse("out", "tmt");
exit;