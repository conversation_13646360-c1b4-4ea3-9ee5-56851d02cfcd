<?php
use ui\Visibility;
require_once '../conf/commonApp.php';
require_once '../lib/thirdparty/vendor/autoload.php';

doInit();
core\AuthenticationManager::verifyTheAuthenticity($_authenticationCode, $_loggedAppUserId);
$authorizationManager = new core\AuthorizationManager($_loggedAppUser);

$stockReleaseNote = form\StockReleaseNote::createForFoodBank($_loggedAppUser->foodBankId);
$stockReleaseNoteIds = array();
$stockReleaseNoteIds[] = $_GET['stockReleaseNoteId'];
$stockReleaseNoteList = array();

if (!empty($stockReleaseNoteIds)) {
    $stockReleaseNoteList = $stockReleaseNote->getStockReleaseNoteListByStockReleaseNoteIds($stockReleaseNoteIds);
}

$stockReleaseNote = (object) $stockReleaseNoteList[0];

if (
    !$authorizationManager->hasRightToPage(core\RightName::stockReleaseNoteList) &&
    !$authorizationManager->isTheSameCustomerAs($stockReleaseNote->customerId)
) {
    core\HttpUtils::redirectAndExit();
}

$t = new ui\Template("./lang/cs");
$t->set_file("tmt", "stockReleaseNote2PDF.tmt");
$t->set_block("tmt", "commoditiesList", "{tmtcommoditiesList}");
$t->set_block("tmt", "commodity", "{tmtcommodity}");

$creator = core\AppUserManager::createAppUser($stockReleaseNote->appUserId);

$t->set_var(array(
    'stockStockReleaseNoteId' => $stockReleaseNote->stockStockReleaseNoteId,
    'actionName' => $stockReleaseNote->actionName,
    'issuedAt' => date\DateFormatter::formatToDateFormatAppUserOrEmptyString($stockReleaseNote->issuedAt),
    'priceCalculation' => core\HpNumberFormatter::toMoney($stockReleaseNote->commodityListData['priceCalculation']),
    'totalAmount' => core\HpNumberFormatter::toAmount($stockReleaseNote->commodityListData['totalAmount']),
    'customerFullName' => $stockReleaseNote->customerName,
    'customerAddress' => $stockReleaseNote->customerAddress,
    'customerIC' => $stockReleaseNote->customerIC,
    'foodBankName' => $stockReleaseNote->foodBankName,
    'foodBankAddress' => $stockReleaseNote->foodBankAddress,
    'foodBankIC' => $stockReleaseNote->foodBankIC,
    'stockName' => $stockReleaseNote->stockName,
    'isNote' => $stockReleaseNote->isNote,
    'note' => $stockReleaseNote->note,
    'createdBy' => $creator->login . ' (' . $creator->localizedUserType . ')'
));

foreach ($stockReleaseNote->commodityListData['stockReleaseNoteCommodityListData'] as $commodity) {
    $t->set_var(array(
        'name' => $commodity['name'],
        'amount' => core\HpNumberFormatter::toAmount($commodity['amount']),
        'code' => $commodity['code']
    ));
    $t->parse("tmtcommoditiesList", "commodity", true);
}

$t->set_var(array(
    'showStampInStockRelease' => $_foodBank->stampInStockRelease ? Visibility::show : Visibility::hide,
    'stampAndSignaturePath' => STAMP_AND_SIGNATURE_PATH,
    'stampAndSignatureName' => $_foodBank->stampAndSignatureName,
    'showStampAndSignature' => !empty(trim($_foodBank->stampAndSignatureName)) ? Visibility::show : Visibility::hide
));

$t->set_global_var_to_template();
$stockReleaseNoteInHTML = $t->finish($t->parse("out", "tmt"));

$pdf = new party\ReceiptAndIssue2PDF(PDF_PAGE_ORIENTATION, PDF_UNIT, PDF_PAGE_FORMAT, true, 'UTF-8', false);
$pdf->setLanguageArray($l);
$pdf->setHeaderData($_foodBank->logoName);
$pdf->setPrintHeader(true);
$pdf->setDefaultPDFLayout();

$html .= '<html><head></head><body><style>' . file_get_contents(__DIR__ . '/style/print2PDF.css') . '</style>';
$html .= $stockReleaseNoteInHTML;
$pdf->writeHTML($html, true, false, true, false);

$stockReleaseNoteNumber = str_pad($stockReleaseNote->stockStockReleaseNoteId, numberLengthInFilename, "0", STR_PAD_LEFT);
$pdf->Output('vydejka_' . $stockReleaseNoteNumber . '.pdf', "I");
exit;
