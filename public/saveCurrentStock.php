<?php
header("Content-Type: text/html; charset=utf-8");

require_once '../conf/commonApp.php';
doInit();
core\AuthenticationManager::verifyTheAuthenticity($_authenticationCode, $_loggedAppUserId);
$authorizationManager = new core\AuthorizationManager($_loggedAppUser);
$fullAccessToIncomePerStockList = $authorizationManager->hasFullAccessToPage(core\RightName::incomePerStockList);

if ($fullAccessToIncomePerStockList) {
    $postData = json_decode(file_get_contents('php://input'), true);
    $foodBankId = $_loggedAppUser->foodBankId;
    $currentStockId = intval($postData['currentStockId']);
    core\AppUserManager::saveCurrentStockdIdByAppUser($_loggedAppUserId, $currentStockId);
    core\HttpUtils::sendSuccessfulEmptyAjaxResponseAndExit();
}
