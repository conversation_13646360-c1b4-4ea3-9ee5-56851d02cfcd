angular.module('PBanka', ['hpBankaMenu'])

.controller('SupplierListController', function($scope) {
	
	$scope.showEdit = false;
	
	$scope.editSupplier = function(supplierId) {
		
		angular.forEach($scope.supplierList, function(supplier) {
			if (supplier.supplierId == supplierId) {
				$scope.supplier = supplier;
			} 
		});

		$scope.showEdit = true;
		return;
	};
	
	$scope.addSupplier = function() {
		
		var supplier = {
				name: '',
				contactPerson: '',
				address: '',
				email: '',
				phone: '',
				groupName: '',
				supplierId: 0
		};
		
		$scope.supplier = supplier;
		$scope.showEdit = true;
		return;
	};

	$scope.showSupplier = function (supplierId) {
		$scope.editSupplier(supplierId);
	};
	
	$scope.canEdit = function() {
		return $scope.rightList.supplierList.fullAccess;
	};
	
	$scope.canSave = function() {
		return $scope.supplierListForm.$valid;
	};
	
	$scope.reverse = false;
	$scope.sordBy = 'name';
	$scope.order = function(sordBy) {
		$scope.reverse = ($scope.sordBy === sordBy) ? !$scope.reverse : false;
	    $scope.sordBy = sordBy;
	};	
	
	
});


