<?php
// zásoby skladu
header("Content-Type: text/html; charset=utf-8");

require_once '../conf/commonApp.php';

doInit();
core\AuthenticationManager::verifyTheAuthenticity($_authenticationCode, $_loggedAppUserId);
$authorizationManager = new core\AuthorizationManager($_loggedAppUser);
if (!$authorizationManager->hasRightToPage(core\RightName::supply)) {
    core\HttpUtils::redirectAndExit();
}
$rights = $authorizationManager->getRights();
$canReadAllStocks = $authorizationManager->canRead('allStocks');

$t = new ui\Template("./lang/cs");
$t->set_file("tmt", "supply.tmt");

$dateTo = date('Y-m-d');

$stockId = !empty($_GET['stockId']) ? $_GET['stockId'] : $_loggedAppUser->currentStockId;

$appUserStockRelation = form\AppUserStockRelation::createForFoodBank($_loggedAppUser->foodBankId);
$stockListData = $appUserStockRelation->getSelectedStockListByAppUser($_loggedAppUser->appUserId, $canReadAllStocks);

$reportManager = report\ReportManager::createForFoodBank($_loggedAppUser->foodBankId);
$commoditySummary = $reportManager->getStocks($dateTo, null, $stockId);

if ($_GET['export'] == 1) {
    $stockManager = form\StockManager::createForFoodBank($_loggedAppUser->foodBankId);
    $stockName = $stockManager->getNameStockByStockId($stockId);
    $separator = report\ReportManager::CSV_COLUMN_SEPARATOR;

    $csvContent = "Zásoby skladu: $stockName ke dni " . date('j.n.Y', strtotime($dateTo)) . "\n\n";
    $csvContent .= "Číslo komodity" . $separator . "Název komodity" . $separator . "Množství (kg)" . $separator . "Hodnota (" . localization\LocalizationProvider::getCurrencySymbol() . ")\n";

    foreach ($commoditySummary['commodities'] as $commodity) {
        $csvContent .= $commodity['code'] . $separator . $commodity['name'] . $separator . core\HpNumberFormatter::toAmount($commodity['amount']) . $separator . core\HpNumberFormatter::toMoney($commodity['value']) . "\n";
    }

    $csvContent .= $separator . "Celkem" . $separator . core\HpNumberFormatter::toAmount($commoditySummary['totalAmount']) . $separator . core\HpNumberFormatter::toMoney($commoditySummary['totalValue']);
    $csvFilename = report\ReportManager::getCsvFileName("Zasoby_skladu_id", $dateFrom, $dateTo); // TODO - jen datum / hodina  a asi ne z report manageru
    report\ReportManager::sendCsvReport($csvContent, $csvFilename);
    exit;
}

$reportData = array(
    'dateTo' => $dateTo,
    'showEmptyReport' => empty($commoditySummary) ? true : false
);

$t->set_var(array(
    'commoditySummary' => htmlspecialchars(json_encode($commoditySummary)),
    'stockListData' => htmlspecialchars(json_encode($stockListData)),
    'reportData' => htmlspecialchars(json_encode($reportData)),
    'rightListData' => htmlspecialchars(json_encode($rights)),
    'stockId' => $stockId,
    'currentStockId' => is_null($_loggedAppUser->currentStockId) ? 'null' : $_loggedAppUser->currentStockId,
    'summaryItem' => $canReadAllStocks ? 'true' : 'false'
));

$t->set_global_var_to_template();
$t->pparse("out", "tmt");
