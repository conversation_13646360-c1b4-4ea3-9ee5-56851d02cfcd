<?php
// stránka na vytvoření formul<PERSON>e (data: název formuláře, platnost od do, seznam komodit)

header("Content-Type: text/html; charset=utf-8");

require_once '../conf/commonApp.php';

doInit();
core\AuthenticationManager::verifyTheAuthenticity($_authenticationCode, $_loggedAppUserId);
$authorizationManager = new core\AuthorizationManager($_loggedAppUser);

if (!$authorizationManager->hasRightToPage(core\RightName::formList)) {
    core\HttpUtils::redirectAndExit();
}

$rights = $authorizationManager->getRights();

if (isset($_POST['backFormList'])) {
    header('Location: formList.php');
    exit;
}

$formManager = form\FormManager::createForFoodBank($_loggedAppUser->foodBankId);

$formId = $_GET['formId'];
$form = $formManager->getForm($formId);

$t = new ui\Template("./lang/cs");
$t->set_file("tmt", "formEditor.tmt");

$commodityManager = new form\CommodityManager();

$commodityListData = array();
$commodities = $commodityManager->getCommodityListForUse($formId);

foreach ($commodities as $commodity) {
    $commodityListData[] = array(
        'commodityId' => $commodity->commodityId,
        'name' => $commodity->name,
        'code' => $commodity->code,
        'pricePerUnit' => core\HpNumberFormatter::toMoney($commodity->pricePerUnit / 1000),
        'formId' => $formId,
        'isUsedCommodity' => false
    );
}

sortByCodeDescending($commodityListData);

$selectedCommodityListData = array();
$selectedCommodities = $commodityManager->getSelectedCommodityList($formId);
$stockReleaseNoteCommodity = form\StockReleaseNoteCommodity::createForFoodBank($_loggedAppUser->foodBankId);
$commodityIdsInStockReleaseNoteCommodity = $stockReleaseNoteCommodity->getUsedComodityIdListByFormId($formId);
$filledFormCommodity = form\FilledFormCommodity::createForFoodBank($_loggedAppUser->foodBankId);
$commodityIdsInFilledFormCommodity = $filledFormCommodity->getUsedComodityIdListByFormId($formId);

foreach ($selectedCommodities as $commodity) {
    $commodityId = $commodity->commodityId;
    $selectedCommodityListData[] = array(
        'commodityId' => $commodity->commodityId,
        'name' => $commodity->name,
        'code' => $commodity->code,
        'pricePerUnit' => core\HpNumberFormatter::toMoney($commodity->pricePerUnit / 1000),
        'formId' => $formId,
        'isUsedCommodity' => array_key_exists($commodityId, $commodityIdsInStockReleaseNoteCommodity) or array_key_exists($commodityId, $commodityIdsInFilledFormCommodity)
    );
}

$t->set_var(array(
    'actionName' => $form->actionName,
    'validFrom' => date('j.n.Y', strtotime($form->validFrom)),
    'validTo' => date('j.n.Y', strtotime($form->validTo)),
    'availableCommodityListData' => htmlspecialchars(json_encode($commodityListData)),
    'selectedCommodityListData' => htmlspecialchars(json_encode($selectedCommodityListData))
));

$t->set_global_var_to_template();
$t->pparse("out", "tmt");

function sortByCodeDescending(&$array) {
    usort($array, function ($a, $b) {
        return ($a['code'] === $b['code'] ? 0 : ($a['code'] < $b['code'] ? 1 : -1));
    });
}
