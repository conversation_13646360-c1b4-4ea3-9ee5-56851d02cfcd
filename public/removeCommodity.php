<?php
header("Content-Type: text/html; charset=utf-8");

require_once '../conf/commonApp.php';
doInit();
core\AuthenticationManager::verifyTheAuthenticity($_authenticationCode, $_loggedAppUserId);
$authorizationManager = new core\AuthorizationManager($_loggedAppUser);
$fullAccessToFormList = $authorizationManager->hasFullAccessToPage(core\RightName::formList);

if ($fullAccessToFormList) {
  $foodBankId = $_loggedAppUser->foodBankId;
  $commodityId = intval($_GET['commodityId']);
  $formId = intval($_GET['formId']);
  \dibi::delete('FormCommodity')->where('[commodityId]=' . $commodityId . ' AND [formId]=' . $formId . ' AND [foodBankId]=' . $foodBankId)->execute();
  core\HttpUtils::sendSuccessfulEmptyAjaxResponseAndExit();
}
