<?php
header("Content-Type: text/html; charset=utf-8");

require_once '../conf/commonApp.php';

doInit();
core\AuthenticationManager::verifyTheAuthenticity($_authenticationCode, $_loggedAppUserId);
$authorizationManager = new core\AuthorizationManager($_loggedAppUser);
if (!$authorizationManager->hasRightToPage(core\RightName::stockTransfersList)) {
    core\HttpUtils::redirectAndExit();
}
$rights = $authorizationManager->getRights();
$canReadAllStocks = $authorizationManager->canRead('allStocks');

if (isset($_GET['delete'])) {
    $transferId = intval($_GET['transferId']);
    $stockTransfer = form\StockTransfer::createForFoodBank($_loggedAppUser->foodBankId);
    $stockTransfer->deleteStockTransferByTransferId($transferId);
}

$stockManager = form\StockManager::createForFoodBank($_loggedAppUser->foodBankId);
$targetStockListData = $stockManager->getStockListWithEmptyItem();

if (isset($_POST['filter'])) {
    $filterListData = array(
        'dateFrom' => empty($_POST['filterByDateFrom']) ? '' : date('Y-m-d', strtotime($_POST['filterByDateFrom'])),
        'dateTo' => empty($_POST['filterByDateTo']) ? '' : date('Y-m-d', strtotime($_POST['filterByDateTo'])),
        'stockTransferId' => $_POST['filterByStockTransferId'],
        'stockId' => $_POST['filterByTargetStockId']
    );
} else {
    $today = date\HpCalendar::getToday();
    $dateFrom = date\HpCalendar::getFirstDayOfCurrentMonth();
    $dateTo = date\HpCalendar::getLastDayInMonth($today);

    $filterListData = array(
        'dateFrom' => date\DateFormatter::formatToSql($dateFrom),
        'dateTo' => date\DateFormatter::formatToSql($dateTo),
        'stockTransferId' => '',
        'stockId' => $_loggedAppUser->currentStockId || 0
    );
}

$t = new ui\Template("./lang/cs");
$t->set_file("tmt", "stockTransfersList.tmt");

$stockTransfer = form\StockTransfer::createForFoodBank($_loggedAppUser->foodBankId);
$stockTransfer->setStockByAppUser($_loggedAppUserId);
$stockTransfer->setDateFromFilter($filterListData['dateFrom']);
$stockTransfer->setDateToFilter($filterListData['dateTo']);
$stockTransfer->setStockTransferIdToFilter($filterListData['stockTransferId']);
$stockTransfer->setStockIdToFilter($filterListData['targetStockId']);
$stockTransfersListData = $stockTransfer->getStockTransfersList();

$appUserStockRelation = form\AppUserStockRelation::createForFoodBank($_loggedAppUser->foodBankId);
$stockListData = $appUserStockRelation->getSelectedStockListByAppUser($_loggedAppUser->appUserId, $canReadAllStocks);

$t->set_var(array(
    'stockTransfersListData' => htmlspecialchars(json_encode($stockTransfersListData)),
    'filterListData' => htmlspecialchars(json_encode($filterListData)),
    'stockListData' => htmlspecialchars(json_encode($stockListData)),
    'currentStockId' => is_null($_loggedAppUser->currentStockId) ? 'null' : $_loggedAppUser->currentStockId,
    'stockReleaseNoteId' => isset($_GET['stockReleaseNoteId']) ? $_GET['stockReleaseNoteId'] : 0,
    'summaryItem' => $canReadAllStocks ? 'true' : 'false',
    'targetStockListData' => htmlspecialchars(json_encode($targetStockListData))
));

$t->set_global_var_to_template();
$t->pparse("out", "tmt");
exit;
