<?php
header("Content-Type: text/html; charset=utf-8");

require_once '../conf/commonApp.php';

doInit();
core\AuthenticationManager::verifyTheAuthenticity($_authenticationCode, $_loggedAppUserId);
$authorizationManager = new core\AuthorizationManager($_loggedAppUser);
if (!$authorizationManager->hasRightToPage(core\RightName::outputFromStockList)) {
    core\HttpUtils::redirectAndExit();
}
$rights = $authorizationManager->getRights();
$canReadAllStocks = $authorizationManager->canRead('allStocks');

$formManager = form\FormManager::createForFoodBank($_loggedAppUser->foodBankId);
$sqlToday = date("Y-m-d");
$forms = $formManager->getActiveFormListForOutputFromStock($sqlToday);

$t = new ui\Template("./lang/cs");
$t->set_file("tmt", "outputFromStockList.tmt");

$formOrder = 0;
$outputFromStockListData = array();
foreach ($forms as $form) {
    $formOrder++;
    $outputFromStockListData[] = array(
        'formId' => $form->formId,
        'order' => $formOrder,
        'actionName' => $form->actionName,
        'validTo' => date('j.n.Y', strtotime($form->validTo))
    );
}

$appUserStockRelation = form\AppUserStockRelation::createForFoodBank($_loggedAppUser->foodBankId);
$stockListData = $appUserStockRelation->getSelectedStockListByAppUser($_loggedAppUser->appUserId, $canReadAllStocks);

$import = isset($_GET['import']) ? 1 : 0;
$customerId = isset($_GET['customerId']) ? $_GET['customerId'] : 0;
$issuedAt = isset($_GET['issuedAt']) ? $_GET['issuedAt'] : '';

$dataForOutputFromStock = array(
    'import' => $import,
    'customerId' => $customerId,
    'issuedAt' => $issuedAt
);

$t->set_var(array(
    'outputFromStockListData' => htmlspecialchars(json_encode($outputFromStockListData)),
    'stockListData' => htmlspecialchars(json_encode($stockListData)),
    'currentStockId' => is_null($_loggedAppUser->currentStockId) ? 'null' : $_loggedAppUser->currentStockId,
    'summaryItem' => $canReadAllStocks ? 'true' : 'false',
    'dataForOutputFromStock' => htmlspecialchars(json_encode($dataForOutputFromStock))
));

$t->set_global_var_to_template();
$t->pparse("out", "tmt");
exit;
