<?php
header("Content-Type: text/html; charset=utf-8");

require_once '../conf/commonApp.php';

doInit();
core\AuthenticationManager::verifyTheAuthenticity($_authenticationCode, $_loggedAppUserId);
$authorizationManager = new core\AuthorizationManager($_loggedAppUser);
if (!$authorizationManager->hasRightToPage(core\RightName::stockReleaseNoteList)) {
    core\HttpUtils::redirectAndExit();
}
$rights = $authorizationManager->getRights();
$fullAccessToStockReleaseNoteList = $authorizationManager->hasFullAccessToPage(core\RightName::stockReleaseNoteList);
$canReadAllStocks = $authorizationManager->canRead('allStocks');

if (isset($_POST['filter'])) {
    $filterListData = array(
        'customer' => $_POST['filterByCustomer'],
        'dateFrom' => empty($_POST['filterByDateFrom']) ? '' : date('Y-m-d', strtotime($_POST['filterByDateFrom'])),
        'dateTo' => empty($_POST['filterByDateTo']) ? '' : date('Y-m-d', strtotime($_POST['filterByDateTo'])),
        'stockStockReleaseNoteId' => $_POST['filterByStockStockReleaseNoteId'],
        'actionName' => $_POST['filterByActionName'],
        'withoutDonationAgreement' => $_POST['withoutDonationAgreement'] ? true : false
    );
} else {
    $today = date\HpCalendar::getToday();
    $dateFrom = date\HpCalendar::getFirstDayOfCurrentMonth();
    $dateTo = date\HpCalendar::getLastDayInMonth($today);

    $filterListData = array(
        'customer' => '',
        'dateFrom' => date\DateFormatter::formatToSql($dateFrom),
        'dateTo' => date\DateFormatter::formatToSql($dateTo),
        'stockStockReleaseNoteId' => '',
        'actionName' => '',
        'withoutDonationAgreement' => false
    );
}

$t = new ui\Template("./lang/cs");
$t->set_file("tmt", "stockReleaseNoteList.tmt");

$stockReleaseNote = form\StockReleaseNote::createForFoodBank($_loggedAppUser->foodBankId);
$stockReleaseNote->setStockByAppUser($_loggedAppUserId);
$stockReleaseNote->setCustomerNameFilter($filterListData['customer']);
$stockReleaseNote->setDateFromFilter($filterListData['dateFrom']);
$stockReleaseNote->setDateToFilter($filterListData['dateTo']);
$stockReleaseNote->setStockStockReleaseNoteIdToFilter($filterListData['stockStockReleaseNoteId']);
$stockReleaseNote->setActionNameToFilter($filterListData['actionName']);
$stockReleaseNote->setWithoutDonationAgreementToFilter($filterListData['withoutDonationAgreement']);
$stockReleaseNoteListData = $stockReleaseNote->getStockReleaseNoteList();

$appUserStockRelation = form\AppUserStockRelation::createForFoodBank($_loggedAppUser->foodBankId);
$stockListData = $appUserStockRelation->getSelectedStockListByAppUser($_loggedAppUser->appUserId, $canReadAllStocks);

$t->set_var(array(
    'stockReleaseNoteListData' => htmlspecialchars(json_encode($stockReleaseNoteListData)),
    'filterListData' => htmlspecialchars(json_encode($filterListData)),
    'showInfoAboutCreateDonationAgreement' => 0,
    'stockListData' => htmlspecialchars(json_encode($stockListData)),
    'currentStockId' => is_null($_loggedAppUser->currentStockId) ? 'null' : $_loggedAppUser->currentStockId,
    'stockReleaseNoteId' => isset($_GET['stockReleaseNoteId']) ? $_GET['stockReleaseNoteId'] : 0,
    'summaryItem' => $canReadAllStocks ? 'true' : 'false'
));

if (isset($_POST['createDonationAgreement']) and $fullAccessToStockReleaseNoteList) {
    $stockReleaseNoteList = json_decode($_POST['stockReleaseNoteList']);
    $stockReleaseNoteIdList = array();
    foreach ($stockReleaseNoteList as $stockReleaseNote) {
        if (!is_null($stockReleaseNote->donationAgreementId)) {
            continue;
        }

        $stockReleaseNoteIdList[] = $stockReleaseNote->stockReleaseNoteId;
    }

    $filterList = json_decode($_POST['filterList']);
    $donationAgreementData['stockReleaseNoteIdList'] = $stockReleaseNoteIdList;
    $donationAgreementData['dateFrom'] = $filterList->dateFrom;
    $donationAgreementData['dateTo'] = $filterList->dateTo;
    $donationAgreementData['appUserId'] = $_loggedAppUserId;
    $donationAgreementData['note'] = trim($_POST['note']);
    $donationAgreementManager = form\DonationAgreementManager::createForFoodBank($_loggedAppUser->foodBankId);
    $donationAgreementManager->saveDonationAgreement($donationAgreementData);
    $t->set_var('showInfoAboutCreateDonationAgreement', 1);
}

$t->set_global_var_to_template();
$t->pparse("out", "tmt");
exit;
