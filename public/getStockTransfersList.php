<?php
header("Content-Type: text/html; charset=utf-8");

require_once '../conf/commonApp.php';
doInit();
core\AuthenticationManager::verifyTheAuthenticity($_authenticationCode, $_loggedAppUserId);
$authorizationManager = new core\AuthorizationManager($_loggedAppUser);
if (!$authorizationManager->hasRightToPage(core\RightName::stockTransfersList)) {
    core\HttpUtils::redirectAndExit();
}

$postData = json_decode(file_get_contents('php://input'), true);
$stockTransfer = form\StockTransfer::createForFoodBank($_loggedAppUser->foodBankId);
$stockTransfer->setStockByAppUser($_loggedAppUserId);
$filterList = $postData['filterList'];
$stockTransfer->setDateFromFilter($filterList['dateFrom']);
$stockTransfer->setDateToFilter($filterList['dateTo']);
$stockTransfer->setStockIdToFilter($filterList['stockId']);
$stockTransferListData = $stockTransfer->getStockTransfersList();

$result = array('stockTransfersListData' => $stockTransferListData);
core\HttpUtils::sendSuccessfulJsonAjaxResponseAndExit($result);
