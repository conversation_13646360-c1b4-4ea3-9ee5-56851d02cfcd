angular.module('selectPicker', [])
    .directive('selectPicker', function () {
        return {
            restrict: 'A',
            link: function (scope, element, attrs) {
                // Inicializace bootstrap-select
                $(element).selectpicker();

                // Sledování změn modelu
                scope.$watch(attrs.ngModel, function () {
                    scope.$evalAsync(function () {
                        $(element).selectpicker('refresh');
                    });
                });

                // Explicitní sledování změn v customerList kolekci
                scope.$watchCollection('customerList', function () {
                    scope.$evalAsync(function () {
                        $(element).selectpicker('refresh');
                    });
                });
            }
        };
    });
