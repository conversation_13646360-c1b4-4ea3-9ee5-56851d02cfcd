<?php
header("Content-Type: text/html; charset=utf-8");

require_once '../conf/commonApp.php';

doInit();
core\AuthenticationManager::verifyTheAuthenticity($_authenticationCode, $_loggedAppUserId);
$authorizationManager = new core\AuthorizationManager($_loggedAppUser);

if (!$authorizationManager->hasRightToPage(core\RightName::incomePerStockList)) {
    core\HttpUtils::redirectAndExit();
}

$canReadAllStocks = $authorizationManager->canRead('allStocks');

$formManager = form\FormManager::createForFoodBank($_loggedAppUser->foodBankId);
$sqlToday = date("Y-m-d");
$forms = $formManager->getActiveFormListForStock($sqlToday);

$t = new ui\Template("./lang/cs");
$t->set_file("tmt", "incomePerStockList.tmt");

if (!empty(intval($_GET['showDirectConsumption']))) {
    $directConsumptionData = array(
        'showMessage' => true,
        'stockFilledFormId' => intval($_GET['stockFilledFormId']),
        'stockStockReleaseNoteId' => intval($_GET['stockStockReleaseNoteId'])
    );
} else {
    $directConsumptionData = array('showMessage' => false);
}

$formOrder = 0;
$incomePerStockListData = array();

foreach ($forms as $form) {
    $formOrder++;
    $incomePerStockListData[] = array(
        'formId' => $form->formId,
        'order' => $formOrder,
        'actionName' => $form->actionName,
        'validTo' => date('j.n.Y', strtotime($form->validTo)),
        'incomePerStock' => $form->incomePerStock ? 1 : 0,
        'directConsumption' => $form->directConsumption && $authorizationManager->hasFullAccessToPage(core\RightName::formFiller) ? 1 : 0
    );
}

$rights = $authorizationManager->getRights();
$appUserStockRelation = form\AppUserStockRelation::createForFoodBank($_loggedAppUser->foodBankId);
$stockListData = $appUserStockRelation->getSelectedStockListByAppUser($_loggedAppUser->appUserId, $canReadAllStocks);

$t->set_var(array(
    'incomePerStockListData' => htmlspecialchars(json_encode($incomePerStockListData)),
    'stockListData' => htmlspecialchars(json_encode($stockListData)),
    'currentStockId' => is_null($_loggedAppUser->currentStockId) ? 'null' : $_loggedAppUser->currentStockId,
    'summaryItem' => $canReadAllStocks ? 'true' : 'false',
    'directConsumptionData' => htmlspecialchars(json_encode($directConsumptionData))
));

$t->set_global_var_to_template();
$t->pparse("out", "tmt");
exit;
