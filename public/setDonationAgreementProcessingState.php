<?php
header("Content-Type: text/html; charset=utf-8");

require_once '../conf/commonApp.php';

doInit();

core\AuthenticationManager::verifyTheAuthenticity($_authenticationCode, $_loggedAppUserId);
$authorizationManager = new core\AuthorizationManager($_loggedAppUser);

if (!$authorizationManager->hasFullAccess(core\RightName::donationAgreement)) {
    core\HttpUtils::redirectAndExit();
}

$postData = json_decode(file_get_contents('php://input'), true);
$donationAgreementId = $postData['donationAgreementId'];
$processed = $postData['processed'] ? 't' : 'f';

$query = "UPDATE [DonationAgreement] SET processed='" . $processed . "' WHERE [donationAgreementId]='" . $donationAgreementId . "'";

$result = \dibi::query($query);

if ($result) {
    core\HttpUtils::sendSuccessfulEmptyAjaxResponseAndExit();
}

core\HttpUtils::sendErrorResponseAndExit();
exit;
