angular.module('PBanka', ['mgcrea.ngStrap', 'hpBanka', 'hpBankaMenu', 'selectPicker'])

	.controller('FormFillerController', function ($scope, $modal, $http, $window) {

		$scope.totalAmount = 0;

		$scope.regNumber = /^(0|[1-9][0-9]*)((\.[0-9]*)|(\,[0-9]*))?$/;

		$scope.countAmount = function () {
			var totalAmount = 0.0;
			angular.forEach($scope.commodityList, function (commodity) {
				var amount = commodity.amount !== undefined ? commodity.amount.toString().replace(/,/, ".") : '0.0';
				if (!Number.isNaN(parseFloat(amount))) {
					totalAmount += parseFloat(amount);
				}
			});
			$scope.totalAmount = totalAmount;
		};

		$scope.inicializeSelectedSupplier = function (selectedSupplierId) {
			for (var i = 0; i < $scope.supplierList.length; i++) {
				if ($scope.supplierList[i].supplierId == selectedSupplierId) {
					$scope.currentSupplier = $scope.supplierList[i];
				}
			}
		};

		$scope.setCustomer = function (currentCustomer) {

			$scope.formFillerList.customerEmail = currentCustomer.email;
			$scope.formFillerList.customerId = currentCustomer.customerId;
		}

		var confirmModal = $modal({scope: $scope, templateUrl: 'lang/cs/modalConfirm.tpl.html', title: '', content: '', show: false});
		$scope.showModal = function () {

			if ($scope.totalAmount > 0) {
				confirmModal.$promise.then(confirmModal.show);
			}
		};

		$scope.canSave = function () {
			return ($scope.formFillerForm.$valid && $scope.totalAmount > 0);
		};

		$scope.saveFormFiller = function () {

			var request = $http({
				method: "POST",
				url: "postFormFiller.php",
				data: {
					commodityList: $scope.commodityList,
					consumptionDate: $scope.consumptionDate,
					currentSupplier: $scope.currentSupplier,
					formFillerList: $scope.formFillerList
				},
				headers: {'Content-Type': 'application/x-www-form-urlencoded'}
			});

			request.success(function (result) {
				$scope.stockReleaseNoteId = result.stockReleaseNoteId;
				$scope.stockFilledFormId = result.stockFilledFormId;
				$scope.stockStockReleaseNoteId = result.stockStockReleaseNoteId;
				$scope.formHasBeenFilledAndSent = result.formHasBeenFilledAndSent;
				$scope.indirectly = result.indirectly;

				if ($scope.indirectly) {
					$window.location = 'incomePerStockList.php?stockFilledFormId=' + $scope.stockFilledFormId + '&stockStockReleaseNoteId=' + $scope.stockStockReleaseNoteId + '&showDirectConsumption=1';
				} else {
					$window.location = 'formFiller.php?stockFilledFormId=' + $scope.stockFilledFormId + '&stockStockReleaseNoteId=' + $scope.stockStockReleaseNoteId + '&stockReleaseNoteId=' + $scope.stockReleaseNoteId + '&formHasBeenFilledAndSent=' + $scope.formHasBeenFilledAndSent;
				}
			}).
				error(function (data) {

				});
		}

	});