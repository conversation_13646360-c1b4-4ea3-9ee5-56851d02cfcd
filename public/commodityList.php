<?php
// číselník komodit umo<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, zm<PERSON>nu a sma<PERSON> komodity (data: n<PERSON><PERSON><PERSON>, jednotka [kg, l], ID komodity)

header("Content-Type: text/html; charset=utf-8");

require_once '../conf/commonApp.php';

doInit();
core\AuthenticationManager::verifyTheAuthenticity($_authenticationCode, $_loggedAppUserId);
$authorizationManager = new core\AuthorizationManager($_loggedAppUser);
if (!$authorizationManager->hasRightToPage(core\RightName::commodityList)) {core\HttpUtils::redirectAndExit();}
$rights = $authorizationManager->getRights();
$fullAccessToCommodityList = $authorizationManager->hasFullAccessToPage(core\RightName::commodityList);

$commodityManager = new form\CommodityManager();

if (isset($_GET['delete']) and $fullAccessToCommodityList) {
    $commodityManager->deleteCommodity($_GET['commodityId']);
}

if (isset($_POST['save']) and $fullAccessToCommodityList) {

    $pricePerUnit = core\HpNumberFormatter::replaceCommaToDotInNumber($_POST['pricePerUnit']);

    $commodityToBeSaved = array(
        'code' => $_POST['code'],
        'name' => $_POST['name'],
        'unit' => 'kg',
        'pricePerUnit' => round($pricePerUnit * 1000),
        'commodityId' => $_POST['commodityId']
    );

    $commodityManager->saveCommodity($commodityToBeSaved);
}

$t = new ui\Template("./lang/cs");
$t->set_file("tmt", "commodityList.tmt");

$usedCommodityIdList = $commodityManager->getUsedCommodityIdList();
$commodities = $commodityManager->getCommodityList();
foreach ($commodities as $commodity) {

    $commodityId = $commodity->commodityId;
    $commodityListData[] = array(
        'commodityId' => $commodityId,
        'code' => intval($commodity->code),
        'name' => $commodity->name,
        'unit' => $commodity->unit,
        'pricePerUnit' => $commodity->pricePerUnit / 1000,
        'isUsedCommodityId' => array_key_exists($commodityId, $usedCommodityIdList)
    );
}

$t->set_var('commodityListData', htmlspecialchars(json_encode($commodityListData)));
$t->set_global_var_to_template();
$t->pparse("out", "tmt");
