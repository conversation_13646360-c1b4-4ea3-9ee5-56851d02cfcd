angular.module('PBanka', ['hpBankaMenu'])

.controller('StockListController', function($scope, $window) {
	
	$scope.showEdit = false;
	
	$scope.editStock = function(stockId) {
		
		angular.forEach($scope.stockList, function(stock) {
			if (stock.stockId == stockId) {
				$scope.stock = stock;
			} 
		});

		$scope.showEdit = true;
		return;
	};
	
	$scope.addStock = function() {
		
		$scope.stock = [{name: '', stockId: 0 }]; 
		$scope.showEdit = true;
		return;
	};
	
	$scope.canSave = function() {
		return $scope.stockListForm.$valid;
	};	
	
	$scope.reverse = false;
	$scope.sordBy = 'name';
	$scope.order = function(sordBy) {
		$scope.reverse = ($scope.sordBy === sordBy) ? !$scope.reverse : false;
	    $scope.sordBy = sordBy;
	};	
	
});


