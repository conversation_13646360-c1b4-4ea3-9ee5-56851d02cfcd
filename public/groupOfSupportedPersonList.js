angular.module('PBanka', ['hpBankaMenu', 'mgcrea.ngStrap', 'hpBanka'])

    .controller('GroupOfSupportedPersonListController', function ($scope, $window) {

        $scope.showEdit = false;

        $scope.editGroupOfSupportedPerson = function (groupId) {
            angular.forEach($scope.groupOfSupportedPersonList, function (groupOfSupportedPerson) {
                if (groupOfSupportedPerson.groupId == groupId) {
                    $scope.groupOfSupportedPerson = groupOfSupportedPerson;
                }
            });

            $scope.showEdit = true;
        };

        $scope.addGroupOfSupportedPerson = function () {
            $scope.groupOfSupportedPerson = [{name: '', groupId: 0}];
            $scope.showEdit = true;
        };

        $scope.canSave = function () {
            return $scope.groupOfSupportedPersonForm.$valid;
        };

        $scope.reverse = false;
        $scope.sordBy = 'name';
        $scope.order = function (sordBy) {
            $scope.reverse = ($scope.sordBy === sordBy) ? !$scope.reverse : false;
            $scope.sordBy = sordBy;
        };
    });
