angular.module('stockSelectList', [])

.directive('stockSelectList', function() {
 return {
 	restrict: 'E',
 	scope: {
 		stockList: '=',
 		currentStock: '=',
 		initCurrentStockId: '=',
 		summaryItem: '=',
 		changeStockFn: '&'
 	},
 	templateUrl: 'lang/cs/stockSelectList.tpl.html',
 	link: function(scope){
 		if (!scope.summaryItem && scope.initCurrentStockId < 1) {
 			scope.currentStock = scope.stockList[0];
 		} else if (scope.initCurrentStockId!=null) {
 	 			scope.currentStock = getCurrentStockByStockId(scope.initCurrentStockId, scope.stockList);
 		}
 		scope.changeStock = function(newCurrentStock) {
 			if (scope.changeStockFn) { 
 				scope.changeStockFn({newCurrentStock: newCurrentStock}); 
 			}
 		}
 	}
 }
});

function getCurrentStockByStockId(selectedStockId, stockList) {
	for (var i=0; i<stockList.length; i++) {
		if (stockList[i].stockId == selectedStockId) {
			currentStock = stockList[i];
	    }
	}
	return currentStock;
};
