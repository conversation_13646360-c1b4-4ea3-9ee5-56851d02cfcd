angular.module('PBanka', ['mgcrea.ngStrap', 'hpBanka', 'hpBankaMenu'])

	.controller('IncomePerStockController', function ($scope, $modal, $http, $window) {

		$scope.totalAmount = 0;

		$scope.regNumber = /^(0|[1-9][0-9]*)((\.[0-9]*)|(\,[0-9]*))?$/;

		$scope.countAmount = function () {
			var totalAmount = 0.0;
			angular.forEach($scope.commodityList, function (commodity) {
				var amount = commodity.amount !== undefined ? commodity.amount.toString().replace(/,/, ".") : '0.0';
				if (!Number.isNaN(parseFloat(amount))) {
					totalAmount += parseFloat(amount);
				}
			});
			$scope.incomePerStock.totalAmount = totalAmount;
		};

		$scope.inicializeSelectedSupplier = function (selectedSupplierId) {
			for (var i = 0; i < $scope.supplierList.length; i++) {
				if ($scope.supplierList[i].supplierId == selectedSupplierId) {
					$scope.currentSupplier = $scope.supplierList[i];
				}
			}
		};

		$scope.canSave = function () {
			return ($scope.incomePerStockForm.$valid && $scope.incomePerStock.totalAmount > 0);
		};
	});