<?php
require_once '../conf/commonApp.php';

doInit();

core\AuthenticationManager::verifyTheAuthenticity($_authenticationCode, $_loggedAppUserId);
$authorizationManager = new core\AuthorizationManager($_loggedAppUser);
if (!$authorizationManager->hasRightToPage(core\RightName::customerList)) { core\HttpUtils::redirectAndExit(); }
$rights = $authorizationManager->getRights();
$fullAccessToCustomerList = $authorizationManager->hasFullAccessToPage(core\RightName::customerList);

$customerManager = new party\CustomerManager();
$customers = $customerManager->getCustomerList($_loggedAppUser->foodBankId);

// create new PDF document
$pdf = new party\HpPDF(PDF_PAGE_ORIENTATION, PDF_UNIT, PDF_PAGE_FORMAT, true, 'UTF-8', false);
$pdf->setLanguageArray($l);

//$tcpdf->SetHeaderData(PDF_HEADER_LOGO, PDF_HEADER_LOGO_WIDTH, PDF_HEADER_TITLE.' 001', PDF_HEADER_STRING, array(0,64,255), array(0,64,128));
$pdf->setPrintHeader(false);
$pdf->setFooterData(array(0,0,0), array(0,0,0));
$pdf->setDefaultPDFLayout();

// Add a page
$pdf->AddPage();

/*
$html .= '<h1 style="font-size: 30pt;">Odběratelé</h1>';
$tcpdf->writeHTMLCell(0, 0, '', '', $html, 0, 1, 0, true, '', true);
$tcpdf->Ln(8);

$index = 1;
foreach ($customers as $customer) {
    if ($customer->archived) {
        continue;
    }
    $tcpdf->Cell(0, 0, $customer->name .', ' . $customer->address . ' (' .$customer->customerId .')'  , 0, 1);
    $tcpdf->write1DBarcode($customer->customerId, 'C39', '', '', '', 20, 0.5, $style, 'N');
    $tcpdf->Ln(8);
    $index++;
    if ($index > 7){
        $tcpdf->AddPage();
        $index =1;
    }
}
*/
$html = '<h1 style="font-size: 30pt;">Odběratelé</h1>';
$html .= '<table style="font-size: 15pt;"><tbody>';

foreach ($customers as $customer) {
    if ($customer->archived) {
        continue;
    }
    $params = $pdf->serializeTCPDFtagParameters(array($customer->customerId, 'C39', '', '',80, 15, 0.5, array(), 'N'));

    $html .= '<tr nobr="true"><td width="60%">' . $customer->name . '</td><td rowspan="2" width="10%">' . $customer->customerId . '</td>';
    $html .= '<td rowspan="2" width="30%"><tcpdf method="write1DBarcode" params="'.$params.'" /></td></tr>';
    $html .= '<tr><td style="font-size: 60%">' . $customer->address . '</td></tr>';
}

$html .= '</tbody></table></body></html>';

$pdf->writeHTML($html, true, false, true, false);
$pdf->Output();

