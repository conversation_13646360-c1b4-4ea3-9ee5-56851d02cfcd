<?php
use party\HpPDF;
require_once '../conf/commonApp.php';
require_once __DIR__ . '/../lib/thirdparty/vendor/autoload.php';

doInit();

core\AuthenticationManager::verifyTheAuthenticity($_authenticationCode, $_loggedAppUserId);
$authorizationManager = new core\AuthorizationManager($_loggedAppUser);
if (!$authorizationManager->hasRightToPage(core\RightName::commodityList)) {
    core\HttpUtils::redirectAndExit();
}
$rights = $authorizationManager->getRights();
$fullAccessToCommodityList = $authorizationManager->hasFullAccessToPage(core\RightName::commodityList);

$commodityManager = new form\CommodityManager();
$commodities = $commodityManager->getCommodityList();

$pdf = new party\ListOFCommodities2PDF(PDF_PAGE_ORIENTATION, PDF_UNIT, PDF_PAGE_FORMAT, true, 'UTF-8', false);
$pdf->setLanguageArray($l);
$pdf->setPrintHeader(false);
$pdf->setFooterData(array(0, 0, 0), array(0, 0, 0));
$pdf->setDefaultPDFLayout();

$firstPage = true;
$shorterTextLength = 25;

sortByCodeDescending($commodities);

foreach ($commodities as $commodity) {
    $pdf->AddPage();
    $html = getHeaderHTML();
    $params = $pdf->serializeTCPDFtagParameters(array($commodity->code, 'C39', '', '', 80, 20, 0.6, array('position' => 'C'), 'N'));
    $commodityTextClass = strlen($commodity->name) > $shorterTextLength ? 'longCommodityName' : 'commodityName';
    $html .= '<div align="center"><img width="875%" src="images/' . localization\LocalizationProvider::getLargeLogo() . '" alt="Potravinová banka" id="logoPBanka"></div>';
    $html .= '<h1 class="' . $commodityTextClass . '">' . $commodity->code . '</h1>';
    $html .= '<h1 class="' . $commodityTextClass . '">' . linkPrepositions($commodity->name) . '</h1>';
    $html .= '<tcpdf method="write1DBarcode" params="' . $params . '" />';
    $html .= '</body></html>';
    $pdf->writeHTML($html, true, false, true, false);
}

$pdf->Output();
exit;

function linkPrepositions($text) {
    return str_replace('v ', 'v&nbsp;', $text);
}

function getHeaderHTML() {
    $html = '
    <html>
    <head>
    <style>
    body {
    font-size: 16pt;
    }
    h5, p {	margin: 0pt;
    }
    .commodityName {
        font-size: 72pt;
        text-align: center;
    }
    .longCommodityName {
        font-size: 50pt;
        text-align: center;
    }
    </style>
    </head>
    <body>';
    return $html;
}

function sortByCodeDescending(&$array) {
    usort($array, function ($a, $b) {
        return ($a['code'] === $b['code'] ? 0 : ($a['code'] < $b['code'] ? 1 : -1));
    });
}
