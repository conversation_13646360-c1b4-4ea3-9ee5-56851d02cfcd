<?php
header("Content-Type: text/html; charset=utf-8");

require_once '../conf/commonApp.php';

doInit();
core\AuthenticationManager::verifyTheAuthenticity($_authenticationCode, $_loggedAppUserId);
$authorizationManager = new core\AuthorizationManager($_loggedAppUser);
if (!$authorizationManager->hasRightToPage(core\RightName::stockReleaseNoteList)) { core\HttpUtils::redirectAndExit(); }
$rights = $authorizationManager->getRights();

$stockReleaseNoteId = $_GET['stockReleaseNoteId'];

$stockReleaseNote = form\StockReleaseNote::createForFoodBank($_loggedAppUser->foodBankId);

$stockReleaseNoteData = $stockReleaseNote->getStockReleaseNoteByStockReleaseNoteId($stockReleaseNoteId);
$formId = $stockReleaseNoteData['formId'];

$customerManager = new party\CustomerManager();
$customerResult = $customerManager->getSelectedCustomerList($_loggedAppUser->foodBankId);
$customerListData = $customerResult->fetchAll();

$stockReleaseNoteCommodity = form\StockReleaseNoteCommodity::createForFoodBank($_loggedAppUser->foodBankId);
$commodityListData = $stockReleaseNoteCommodity->getCommodityListByFormIdAndStockReleaseNoteId($formId, $stockReleaseNoteId, $stockReleaseNoteData['stockId']);

$data = array('stockReleaseNote' => $stockReleaseNoteData, 'commodityList' => $commodityListData, 'customerList' => $customerListData);
core\HttpUtils::sendSuccessfulJsonAjaxResponseAndExit($data);
