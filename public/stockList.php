<?php
header("Content-Type: text/html; charset=utf-8");

require_once '../conf/commonApp.php';

doInit();
core\AuthenticationManager::verifyTheAuthenticity($_authenticationCode, $_loggedAppUserId);
$authorizationManager = new core\AuthorizationManager($_loggedAppUser);
if (!$authorizationManager->hasRightToPage(core\RightName::stockList)) { core\HttpUtils::redirectAndExit(); }
$rights = $authorizationManager->getRights();
$fullAccessToStockList = $authorizationManager->hasFullAccessToPage(core\RightName::stockList);

$stockManager = form\StockManager::createForFoodBank($_loggedAppUser->foodBankId);

if ($_GET['delete'] and $fullAccessToStockList) {
    $stockManager->deleteStock($_GET['stockId']);
}

if (isset($_POST['save']) and $fullAccessToStockList) {
    $stockToBeSaved = array(
         'name' => $_POST['name']
        ,'stockId' => $_POST['stockId'] 
        ,'appUserId' => $_loggedAppUserId       
    );
    $stockManager->saveStock($stockToBeSaved);
}

$t = new ui\Template("./lang/cs");
$t->set_file("tmt", "stockList.tmt");

$usedStockIdList = $stockManager->getUsedStockIdList();
$stockList = $stockManager->getStockList();
$stockListData = array();
foreach ($stockList as $stock) {
  
  $stockId = $stock['stockId'];
  $stockListData[] = array(
       'stockId' => $stockId
      ,'name' => $stock['name']
      ,'isUsedStockId' => array_key_exists($stockId, $usedStockIdList)
  );
}

$t->set_var(array(
     'stockListData' => htmlspecialchars(json_encode($stockListData))
));
$t->set_global_var_to_template();
$t->pparse("out", "tmt");
