angular.module('PBanka', ['ui.sortable','hpBankaMenu'])

.controller('FormEditorController', function($scope, $http, $timeout) {
	
	$scope.showErrorMessage = false;  
    $scope.showSaveMessage = false;
    $scope.showRemoveMessage = false;
	
	$scope.canEdit = function() {
		return $scope.rightList.formList.fullAccess;
	};
	
    $scope.sortableOption = {
    		axis: "y",
            cursor: "move", 
            handle: ".handleForSorting", 
            stop: function(e, ui) {
             // po presunuti se zde vzdy zastavi
              $scope.sortedItemId = []; // zde je aktualne serazeny seznam idecek
              itemList = $scope.selectedCommodities;
              angular.forEach(itemList, function(item) {
                $scope.sortedItemId.push(item.commodityId);
                formId = item.formId;
              });
              orderedIdList = $scope.sortedItemId;
              
              $http.get('saveCommoditiesOrder.php?orderedIdList=' + encodeURIComponent(orderedIdList) + '&formId=' + encodeURIComponent(formId))
              
              .success(function(result) {
              })
              
              .error(function(data) {
                $scope.showErrorMessage = true;
                ui.item.parent().sortable('cancel'); // pri chybe vrat puvodni razeni
              });
            }
     };
    
    
    $scope.removeSelectedCommodities = function(commodity) {
    	
    	if ($scope.rightList.formList.fullAccess && !commodity.isUsedCommodity)  {
    	
	    	var commodityId = commodity.commodityId;
	    	var formId = commodity.formId;
	    	$scope.availableCommodities.push(commodity);
	    	var selectedCommodities = $scope.selectedCommodities;
	    	$scope.selectedCommodities = [];
	    	
	    	angular.forEach(selectedCommodities, function(selectedCommodity) {
	    		if (selectedCommodity.commodityId !== commodity.commodityId) {
	    			$scope.selectedCommodities.push(selectedCommodity);
	    		}
	    	});
	    	
	    	$http.get('removeCommodity.php?formId=' + encodeURIComponent(formId) + '&commodityId=' + encodeURIComponent(commodityId))
	        .success(function(result) {
	        	$scope.showRemoveMessage = true;
	        	$timeout(function() {
	        		$scope.showRemoveMessage = false; 
	            }, 1500);
	        })
	        .error(function(data) {
	        });
	    	
    	}
    };   
    
    
    $scope.addToSelectedCommodities = function(commodity) {
    	
    	if ($scope.rightList.formList.fullAccess)  {    	
	    	var commodityId = commodity.commodityId;
	    	var formId = commodity.formId;
	    	$scope.selectedCommodities.push(commodity);
	    	var availableCommodities = $scope.availableCommodities;
	    	$scope.availableCommodities = [];
	    	
	    	angular.forEach(availableCommodities, function(availableCommodity) {
	    		if (availableCommodity.commodityId !== commodity.commodityId) {
	    			$scope.availableCommodities.push(availableCommodity);
	    		}
	    	});
	    	
	    	$http.get('saveCommodity.php?formId=' + encodeURIComponent(formId) + '&commodityId=' + encodeURIComponent(commodityId))
	        .success(function(result) {
	        	$scope.showSaveMessage = true;
	        	$timeout(function() {
	        		$scope.showSaveMessage = false; 
	            }, 1500);
	        })
	        .error(function(data) {
	        });
    	}
    };   
});


