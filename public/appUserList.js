angular.module('PBanka', ['hpBankaMenu','ui.sortable'])

.controller('AppUserListController', function($scope, $http, $timeout) {
	
	$scope.showEdit = false;
	$scope.showPassword = false;
	$scope.showAssignStock = false;
	
	$scope.inicializeSelectedUserType = function(selectedUserTypeId) {
		for (var i=0; i<$scope.userTypeList.length; i++) {
			if ($scope.userTypeList[i].userTypeId == selectedUserTypeId) {
				$scope.currentUserType = $scope.userTypeList[i];
		    }
		}
	};	
	
	$scope.editAppUser = function(appUserId) {
		angular.forEach($scope.appUserList, function(appUser) {
			if (appUser.appUserId == appUserId) {
				$scope.appUser = appUser;
				$scope.inicializeSelectedUserType(appUser.userType);
			} 
		});
		$scope.showEdit = true;
	};
	
	$scope.editPassword = function(appUserId) {
		$scope.editAppUser(appUserId);
		$scope.showPassword = true;
	}
	
	$scope.addAppUser = function() {
		var appUser = {
				login: '',
				password: '',
				appUserId: 0,
		}
		$scope.currentUserType = $scope.userTypeList[0];
		$scope.appUser = appUser;
		$scope.showEdit = true;
	};

	$scope.showAppUser = function (appUserId) {
		$scope.editAppUser(appUserId);
	};
	
	$scope.assignedStock = function(appUserId) {
		
		$http.get('retriveStockByAppUser.php?appUserId=' + encodeURIComponent(appUserId))
        .success(function(result) {
        	$scope.selectedStockList = result.selectedStockListData;
        	$scope.availableStockList = result.availableStockListData;
        	$scope.showAssignStock = true;
        })
        .error(function(data) {
        });
	};
	
    $scope.addToSelectedStockList = function(stock) {
    	if ($scope.rightList.formList.fullAccess)  { 
	    	var stockId = stock.stockId;
	    	var appUserId = stock.appUserId;
	    	$scope.selectedStockList.push(stock);
	    	var availableStockList = $scope.availableStockList;
	    	$scope.availableStockList = [];
	    	
	    	angular.forEach(availableStockList, function(availableStock) {
	    		if (availableStock.stockId !== stock.stockId) {
	    			$scope.availableStockList.push(availableStock);
	    		}
	    	});
	    	
	    	$http.get('saveAppUserStockRelation.php?appUserId=' + encodeURIComponent(appUserId) + '&stockId=' + encodeURIComponent(stockId))
	        .success(function(result) {
	        	$scope.showSaveMessage = true;
	        	$timeout(function() {
	        		$scope.showSaveMessage = false; 
	            }, 1500);
	        })
	        .error(function(data) {
	        });
    	}
    };  
    
    $scope.removeSelectedStockList = function(stock) {
    	
    	if ($scope.rightList.formList.fullAccess)  {
    	
	    	var stockId = stock.stockId;
	    	var appUserId = stock.appUserId;
	    	$scope.availableStockList.push(stock);
	    	var selectedStockList = $scope.selectedStockList;
	    	$scope.selectedStockList = [];
	    	
	    	angular.forEach(selectedStockList, function(selectedStock) {
	    		if (selectedStock.stockId !== stock.stockId) {
	    			$scope.selectedStockList.push(selectedStock);
	    		}
	    	});
	    	
	    	$http.get('removeAppUserStockRelation.php?appUserId=' + encodeURIComponent(appUserId) + '&stockId=' + encodeURIComponent(stockId))
	        .success(function(result) {
	        	$scope.showRemoveMessage = true;
	        	$timeout(function() {
	        		$scope.showRemoveMessage = false; 
	            }, 1500);
	        })
	        .error(function(data) {
	        });
    	}
    };   
    
    $scope.sortableOption = {
    		axis: "y",
            cursor: "move", 
            handle: ".handleForSorting", 
            stop: function(e, ui) {
             // po presunuti se zde vzdy zastavi
              $scope.sortedItemId = []; // zde je aktualne serazeny seznam idecek
              itemList = $scope.selectedStockList;
              angular.forEach(itemList, function(item) {
                $scope.sortedItemId.push(item.stockId);
                appUserId = item.appUserId;
              });
              orderedIdList = $scope.sortedItemId;
              console.log(orderedIdList);
              console.log(appUserId);
              
              $http.get('saveAppUserStockRelationOrder.php?orderedIdList=' + encodeURIComponent(orderedIdList) + '&appUserId=' + encodeURIComponent(appUserId))
              .success(function(result) {
              })
              .error(function(data) {
                $scope.showErrorMessage = true;
                ui.item.parent().sortable('cancel'); // pri chybe vrat puvodni razeni
              });
            }
     };
	
	$scope.canEdit = function() {
		return $scope.rightList.appUserList.fullAccess;
	};
	
	$scope.canSave = function() {
		return $scope.appUserListForm.$valid;
	};
	
	$scope.reverse = false;
	$scope.sordBy = 'login';
	$scope.order = function(sordBy) {
		$scope.reverse = ($scope.sordBy === sordBy) ? !$scope.reverse : false;
	    $scope.sordBy = sordBy;
	};	
})

.directive('username', function($q, $http) {
  return {
    require: 'ngModel',
    link: function(scope, elm, attrs, ctrl) {

      ctrl.$asyncValidators.username = function(modelValue, viewValue) {
    	  
        if (ctrl.$isEmpty(modelValue)) {
          // consider empty model valid
          return $q.when();
        }

        var def = $q.defer();
        
    	$http.get('getLogin.php?username=' + encodeURIComponent(modelValue))
        .success(function(result) {
            if (result.login != modelValue || result.appUserId == scope.appUser.appUserId) {
                // The username is available
                def.resolve();
              } else {
                def.reject();
              }
        })
        .error(function(data) {
        });

        return def.promise;
      };
    }
  };
})

.directive('sameasusername', function($q) {
  return {
    require: 'ngModel',
    link: function(scope, elm, attrs, ctrl) {
    	
    	ctrl.$validators.sameasusername = function(modelValue, viewValue) {
      	  
            if (ctrl.$isEmpty(modelValue)) {
              // consider empty model valid
              return true;
            }
            return scope.appUser.login != modelValue;
    	};
    }
  };
});


