<?php
header("Content-Type: text/html; charset=utf-8");

require_once '../conf/commonApp.php';
doInit();
core\AuthenticationManager::verifyTheAuthenticity($_authenticationCode, $_loggedAppUserId);
$authorizationManager = new core\AuthorizationManager($_loggedAppUser);
if (!$authorizationManager->hasRightToPage(core\RightName::supportedPersonList)) {
    core\HttpUtils::redirectAndExit();
}
$rights = $authorizationManager->getRights();
$fullAccessToSupportedPersonList = $authorizationManager->hasFullAccessToPage(core\RightName::supportedPersonList);

$year = $_GET['year'];
$month = $_GET['month'];

$supportedPersonManager = form\SupportedPersonManager::createForFoodBank($_loggedAppUser->foodBankId);
$supportedPersons = $supportedPersonManager->getSupportedPersonListByPeriodAndCustomerId($year, $month, $_loggedAppUser->customerId);

$supportPersonListData = array('supportedPersons' => $supportedPersons);
core\HttpUtils::sendSuccessfulJsonAjaxResponseAndExit($supportPersonListData);
