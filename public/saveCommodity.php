<?php
header("Content-Type: text/html; charset=utf-8");

require_once '../conf/commonApp.php';
doInit();
core\AuthenticationManager::verifyTheAuthenticity($_authenticationCode, $_loggedAppUserId);
$authorizationManager = new core\AuthorizationManager($_loggedAppUser);
$fullAccessToFormList = $authorizationManager->hasFullAccessToPage(core\RightName::formList);

if ($fullAccessToFormList) {
  $foodBankId = $_loggedAppUser->foodBankId;
  $commodityId = $_GET['commodityId'];
  $formId = $_GET['formId'];
  
  $result = \dibi::select('max([itemOrder])')->from('FormCommodity')->where("[formId] = %i AND [foodBankId] = %i", $formId, $foodBankId)->execute();
  $itemOrder = intval($result->fetchSingle());
  $itemOrder++;
  $values = array('formId' => $formId, 'commodityId' => $commodityId, 'itemOrder' => $itemOrder, 'foodBankId' => $foodBankId);
  \dibi::insert('FormCommodity', $values)->execute();
  
  core\HttpUtils::sendSuccessfulEmptyAjaxResponseAndExit();
}
