<?php
header("Content-Type: text/html; charset=utf-8");
require_once '../conf/commonApp.php';
doInit();
core\AuthenticationManager::verifyTheAuthenticity($_authenticationCode, $_loggedAppUserId);
$authorizationManager = new core\AuthorizationManager($_loggedAppUser);
$fullAccessToFormList = $authorizationManager->hasFullAccessToPage(core\RightName::formList);

if ($fullAccessToFormList) {
  $foodBankId = $_loggedAppUser->foodBankId;
  $orderedIdList = explode(",", $_GET['orderedIdList']);
  $formId = intval($_GET['formId']);
  $order = 1;
  foreach ($orderedIdList as $id) {
    $values = array( 'itemOrder' => $order++ );
    \dibi::update('FormCommodity', $values)->where('[commodityId]=' . intval($id) . ' AND [formId]=' . $formId . ' AND [foodBankId]=' . $foodBankId)->execute();
  }
  core\HttpUtils::sendSuccessfulEmptyAjaxResponseAndExit();
}
