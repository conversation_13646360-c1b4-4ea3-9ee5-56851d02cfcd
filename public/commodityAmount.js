angular.module('commodityAmount', [])

.directive('commodityAmount', function() {
 return {
 	restrict: 'E',
 	scope: {
 		commodity: '=',
 		form: '=',
 		id: '=',
 		countAmountFn: '&'
 	},
 	templateUrl: 'lang/cs/commodityAmount.tpl.html',
 	link: function(scope){
 		scope.regNumber = /^(0|[1-9][0-9]*)((\.[0-9]*)|(\,[0-9]*))?$/; 
 		scope.count = function() {
 			if (scope.countAmountFn) { 
 				scope.countAmountFn(); 
 			}
 		}
 	}
 }
});
