angular.module('PBanka', ['mgcrea.ngStrap', 'hpBanka', 'hpBankaMenu'])

.controller('FormListController', function($scope, $window) {
	
	$scope.showEdit = false;
	$scope.form = [];
	
	$scope.inicializeSelectedStock = function(selectedStockId) {
		for (var i=0; i<$scope.stockList.length; i++) {
			if ($scope.stockList[i].stockId == selectedStockId) {
				$scope.currentStock = $scope.stockList[i];
		    }
		}
	};	
	
	$scope.editForm = function(index) {
		
		$scope.form.actionName = $scope.formList[index].actionName;
		$scope.form.validFrom = $scope.formList[index].validFrom;
		$scope.form.validTo = $scope.formList[index].validTo;
		$scope.form.backDays = $scope.formList[index].backDays;		
		$scope.form.formId = $scope.formList[index].formId;
		$scope.inicializeSelectedStock($scope.formList[index].stockId);
		$scope.form.directConsumption = $scope.formList[index].directConsumption;
		$scope.form.incomePerStock = $scope.formList[index].incomePerStock;
		$scope.form.outputFromStock = $scope.formList[index].outputFromStock;
		$scope.form.editedItem = $scope.formList[index].editedItem;
		$scope.showEdit = true;
		return;
	};
	
	$scope.addForm = function() {
		
		$scope.form.actionName = '';
		$scope.form.validFrom = '';
		$scope.form.validTo = '';
		$scope.form.backDays = '';
		$scope.form.formId = 0;
		$scope.currentStock = $scope.stockList[0];
		$scope.form.directConsumption = false;
		$scope.form.incomePerStock = false;
		$scope.form.outputFromStock = false;
		$scope.form.editedItem = false;
		$scope.showEdit = true;
		return;
	};
	
	$scope.showForm = function (index) {
		$scope.editForm(index);
	};
	
	$scope.canEditedStockList = function() {
		return !$scope.form.editedItem;
	}
	
	$scope.canEdit = function() {
		return $scope.rightList.formList.fullAccess;
	};
	
	$scope.hasSelectedAtLeastOneForm = function() {
		return ($scope.form.directConsumption || $scope.form.incomePerStock || $scope.form.outputFromStock) ? true : false;
	};
	
	$scope.canSave = function() {
		return ($scope.formListForm.$valid && $scope.hasSelectedAtLeastOneForm());
	};
});