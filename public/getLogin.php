<?php
header("Content-Type: text/html; charset=utf-8");

require_once '../conf/commonApp.php';

doInit();
core\AuthenticationManager::verifyTheAuthenticity($_authenticationCode, $_loggedAppUserId);
$username = $_GET['username'];
$result = \dibi::select('[appUserId], [login]')->from('[AppUser]')->where('[login] = %s', $username)->execute();
$appUser = $result->fetch();
$appUserResult = array('appUserId' => $appUser->appUserId, 'login' => $appUser->login);
core\HttpUtils::sendSuccessfulJsonAjaxResponseAndExit($appUserResult);
