angular.module('PBanka', ['hpBankaMenu'])

.filter('trustAsHtml', function($sce) {
    return function(text) {
        return $sce.trustAsHtml(text);
    };
})

.controller('ManualListController', function($scope, $timeout) {

    // Inicializace
    $scope.showErrorMessage = false;
    $scope.errorMessage = '';
    $scope.editMode = false;
    $scope.originalManual = {};

    // Kontrola práv
    $scope.canEdit = function() {
        return $scope.rightList && $scope.rightList.manualList && $scope.rightList.manualList.fullAccess;
    };

    $scope.canRead = function() {
        return $scope.rightList && $scope.rightList.manualList && $scope.rightList.manualList.read;
    };

    // Kontrola, zda existuje obsah manuálu
    $scope.hasContent = function() {
        return $scope.manual && ($scope.manual.title || $scope.manual.content);
    };

    // Přepnutí do editačního režimu
    $scope.enterEditMode = function() {
        if (!$scope.canEdit()) {
            return;
        }

        // Uložení původních dat pro možnost zrušení
        $scope.originalManual = angular.copy($scope.manual);
        $scope.editMode = true;

        // Inicializace Summernote editoru s malým zpožděním
        $timeout(function() {
            $('#summernote').summernote({
                height: 400,
                lang: 'cs-CZ',
                focus: false,
                airMode: false,
                dialogsInBody: true,
                toolbar: [
                    ['style', ['style']],
                    ['font', ['bold', 'italic', 'underline', 'strikethrough', 'superscript', 'subscript', 'clear']],
                    ['fontname', ['fontname']],
                    ['fontsize', ['fontsize']],
                    ['color', ['forecolor', 'backcolor']],
                    ['para', ['ul', 'ol', 'paragraph']],
                    ['height', ['height']],
                    ['table', ['table']],
                    ['insert', ['link', 'picture', 'hr']],
                    ['view', ['fullscreen', 'codeview', 'help']]
                ],
                fontNames: ['Arial', 'Arial Black', 'Comic Sans MS', 'Courier New', 'Georgia', 'Helvetica Neue', 'Helvetica', 'Impact', 'Lucida Grande', 'Tahoma', 'Times New Roman', 'Trebuchet MS', 'Verdana'],
                fontSizes: ['8', '9', '10', '11', '12', '14', '16', '18', '20', '24', '28', '32', '36', '48', '64', '82', '150'],
                styleTags: [
                    'p',
                    { title: 'Blockquote', tag: 'blockquote', className: 'blockquote', value: 'blockquote' },
                    'pre', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6'
                ],
                lineHeights: ['0.2', '0.3', '0.4', '0.5', '0.6', '0.8', '1.0', '1.2', '1.4', '1.5', '2.0', '3.0'],
                callbacks: {
                    onChange: function(contents) {
                        $scope.$apply(function() {
                            $scope.manual.content = contents;
                        });
                    }
                }
            });

            // Nastavení obsahu do editoru
            $('#summernote').summernote('code', $scope.manual.content || '');
        }, 100);
    };

    // Zrušení editace
    $scope.cancelEdit = function() {
        // Obnovení původních dat
        $scope.manual = angular.copy($scope.originalManual);
        $scope.editMode = false;

        // Zničení Summernote editoru
        $('#summernote').summernote('destroy');
    };

    // Uložení manuálu
    $scope.saveManual = function() {
        if (!$scope.canEdit()) {
            return;
        }

        // Získání obsahu z Summernote editoru
        $scope.manual.content = $('#summernote').summernote('code');

        if (!$scope.manual.title || !$scope.manual.title.trim()) {
            $scope.showErrorMessage = true;
            $scope.errorMessage = 'Název manuálu je povinný.';
            return;
        }

        if (!$scope.manual.content || !$scope.manual.content.trim()) {
            $scope.showErrorMessage = true;
            $scope.errorMessage = 'Obsah manuálu je povinný.';
            return;
        }

        // Skrytí chybových zpráv
        $scope.showErrorMessage = false;

        // Příprava dat pro odeslání
        var manualData = {
            title: $scope.manual.title.trim(),
            content: $scope.manual.content.trim()
        };

        // Vytvoření formuláře a odeslání
        var form = document.createElement('form');
        form.method = 'POST';
        form.action = 'manualList.php';

        var saveInput = document.createElement('input');
        saveInput.type = 'hidden';
        saveInput.name = 'saveManual';
        saveInput.value = '1';
        form.appendChild(saveInput);

        var manualInput = document.createElement('input');
        manualInput.type = 'hidden';
        manualInput.name = 'manual';
        manualInput.value = JSON.stringify(manualData);
        form.appendChild(manualInput);

        document.body.appendChild(form);
        form.submit();
    };

    // Smazání manuálu
    $scope.deleteManual = function() {
        if (!$scope.canEdit()) {
            return;
        }

        if (confirm('Opravdu chcete smazat manuál? Tato akce je nevratná.')) {
            // Zničení Summernote editoru pokud je aktivní
            if ($scope.editMode) {
                $('#summernote').summernote('destroy');
            }

            var form = document.createElement('form');
            form.method = 'POST';
            form.action = 'manualList.php';

            var deleteInput = document.createElement('input');
            deleteInput.type = 'hidden';
            deleteInput.name = 'deleteManual';
            deleteInput.value = '1';
            form.appendChild(deleteInput);

            document.body.appendChild(form);
            form.submit();
        }
    };

    // Skrytí chybových zpráv
    $scope.hideErrorMessage = function() {
        $scope.showErrorMessage = false;
    };

    // Inicializace při načtení stránky
    $scope.$on('$viewContentLoaded', function() {
        // Pokud nemáme žádný obsah a máme práva k editaci, automaticky přepneme do editačního režimu
        if (!$scope.hasContent() && $scope.canEdit()) {
            $scope.editMode = false; // Začneme v zobrazovacím režimu
        }
    });
});
