angular.module('PBanka', ['hpBankaMenu'])

.controller('ManualListController', function($scope, $window) {
    
    // Inicializace zpráv
    $scope.showSaveMessage = false;
    $scope.showDeleteMessage = false;
    $scope.showErrorMessage = false;
    
    // Kontrola práv
    $scope.canEdit = function() {
        return $scope.rightList.manualList.fullAccess;
    };
    
    $scope.canRead = function() {
        return $scope.rightList.manualList.read;
    };
    
    // Uložení manuálu
    $scope.saveManual = function() {
        if (!$scope.canEdit()) {
            return;
        }
        
        if (!$scope.manual.title || !$scope.manual.title.trim()) {
            $scope.showErrorMessage = true;
            $scope.errorMessage = 'Název manuálu je povinný.';
            return;
        }
        
        if (!$scope.manual.content || !$scope.manual.content.trim()) {
            $scope.showErrorMessage = true;
            $scope.errorMessage = 'Obsah manuálu je povinný.';
            return;
        }
        
        // Skrytí chybových zpráv
        $scope.showErrorMessage = false;
        
        // Příprava dat pro odeslání
        var manualData = {
            title: $scope.manual.title.trim(),
            content: $scope.manual.content.trim()
        };
        
        // Vytvoření formuláře a odeslání
        var form = document.createElement('form');
        form.method = 'POST';
        form.action = 'manualList.php';
        
        var saveInput = document.createElement('input');
        saveInput.type = 'hidden';
        saveInput.name = 'saveManual';
        saveInput.value = '1';
        form.appendChild(saveInput);
        
        var manualInput = document.createElement('input');
        manualInput.type = 'hidden';
        manualInput.name = 'manual';
        manualInput.value = JSON.stringify(manualData);
        form.appendChild(manualInput);
        
        document.body.appendChild(form);
        form.submit();
    };
    
    // Smazání manuálu
    $scope.deleteManual = function() {
        if (!$scope.canEdit()) {
            return;
        }
        
        if (confirm('Opravdu chcete smazat manuál? Tato akce je nevratná.')) {
            var form = document.createElement('form');
            form.method = 'POST';
            form.action = 'manualList.php';
            
            var deleteInput = document.createElement('input');
            deleteInput.type = 'hidden';
            deleteInput.name = 'deleteManual';
            deleteInput.value = '1';
            form.appendChild(deleteInput);
            
            document.body.appendChild(form);
            form.submit();
        }
    };
    
    // Kontrola, zda existuje obsah manuálu
    $scope.hasContent = function() {
        return $scope.manual && ($scope.manual.title || $scope.manual.content);
    };
    
    // Skrytí zpráv
    $scope.hideMessages = function() {
        $scope.showSaveMessage = false;
        $scope.showDeleteMessage = false;
        $scope.showErrorMessage = false;
    };
});
