angular.module('PBanka', ['mgcrea.ngStrap', 'hpBanka', 'hpBankaMenu', 'commodityAmount', 'numberLimits', 'selectPicker'])

	.controller('OutputFromStockController', function ($scope) {
		$scope.showEdit = false;
		$scope.totalAmount = 0;

		$scope.countAmount = function () {

			var totalAmount = 0.0;
			angular.forEach($scope.commodityList, function (commodity) {
				var amount = commodity.amount !== undefined ? commodity.amount.toString().replace(/,/, ".") : '0.0';
				if (!Number.isNaN(parseFloat(amount))) {
					totalAmount += parseFloat(amount);
				}
			});
			$scope.outputFromStock.totalAmount = totalAmount;
		};

		$scope.inicializeSelectedCustomer = function (selectedCustomerId) {
			for (var i = 0; i < $scope.customerList.length; i++) {
				if ($scope.customerList[i].customerId == selectedCustomerId) {
					$scope.currentCustomer = $scope.customerList[i];
				}
			}
		};

		$scope.canSave = function () {
			return ($scope.outputFromStockForm.$valid && $scope.outputFromStock.totalAmount > 0);
		};

		$scope.$watch("commodityList.length", function () {
			$scope.countAmount();
		});
	});