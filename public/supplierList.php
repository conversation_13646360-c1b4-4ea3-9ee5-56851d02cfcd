<?php
// číselník dodavatelů umož<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, z<PERSON><PERSON><PERSON> a s<PERSON>z<PERSON> dodavatele (data: název, kontaktní osoba, adresa, email, telefon, organizace, ID odběratele)

header("Content-Type: text/html; charset=utf-8");

require_once '../conf/commonApp.php';

doInit();
core\AuthenticationManager::verifyTheAuthenticity($_authenticationCode, $_loggedAppUserId);
$authorizationManager = new core\AuthorizationManager($_loggedAppUser);
if (!$authorizationManager->hasRightToPage(core\RightName::supplierList)) { core\HttpUtils::redirectAndExit(); }
$rights = $authorizationManager->getRights();
$fullAccessToSupplierList = $authorizationManager->hasFullAccessToPage(core\RightName::supplierList);

$supplierManager = party\SupplierManager::createForFoodBank($_loggedAppUser->foodBankId);

if ($_GET['delete'] and $fullAccessToSupplierList) {
  $supplierManager->deleteSupplier($_GET['supplierId']);
}

if (isset($_POST['save']) and $fullAccessToSupplierList) {
  $supplierToBeSaved = array(
       'name' => $_POST['name']
      ,'contactPerson' => $_POST['contactPerson']
      ,'address' => $_POST['address']
      ,'email' => $_POST['email']
      ,'phone' => $_POST['phone']
      ,'groupName' => $_POST['groupName']
      ,'supplierId' => $_POST['supplierId']
      ,'IC' => trim(substr(core\HpNumberFormatter::removeGaps($_POST['IC']), 0, 8))
      ,'appUserId' => $_loggedAppUserId     
  );
  $supplierManager->saveSupplier($supplierToBeSaved);
}

$t = new ui\Template("./lang/cs");
$t->set_file("tmt", "supplierList.tmt");

$suppliers = $supplierManager->getSupplierList();
foreach ($suppliers as $supplier) {
  $supplierId = $supplier->supplierId;
  $supplierListData[] = array(
       'supplierId' => $supplierId
      ,'name' => $supplier->name
      ,'contactPerson' => $supplier->contactPerson
      ,'address' => $supplier->address
      ,'email' => $supplier->email
      ,'phone' => $supplier->phone
      ,'groupName' => $supplier->groupName
      ,'IC' => core\HpNumberFormatter::toIC($supplier->IC)
      ,'usedItem' => $supplierManager->isSupplierUsed($supplierId)
  );
}

$t->set_var(array(
     'supplierListData' => htmlspecialchars(json_encode($supplierListData))
));

$t->set_global_var_to_template();
$t->pparse("out", "tmt");
