<?php
require_once '../conf/commonApp.php';
require_once '../lib/thirdparty/vendor/autoload.php';

doInit();
core\AuthenticationManager::verifyTheAuthenticity($_authenticationCode, $_loggedAppUserId);
$authorizationManager = new core\AuthorizationManager($_loggedAppUser);

if (!$authorizationManager->hasRightToPage(core\RightName::stockTransfersList)) {
    core\HttpUtils::redirectAndExit();
}

$stockTransfer = form\StockTransfer::createForFoodBank($_loggedAppUser->foodBankId);
$transfer = (object) $stockTransfer->getStockTransferByTransferId($_GET['transferId']);

$t = new ui\Template("./lang/cs");
$t->set_file("tmt", "stockTransfer2PDF.tmt");
$t->set_block("tmt", "commoditiesList", "{tmtcommoditiesList}");
$t->set_block("tmt", "commodity", "{tmtcommodity}");

$creator = core\AppUserManager::createAppUser($transfer->appUserId);

$t->set_var(array(
    'foodBankName' => $_foodBank->name,
    'stockTransferId' => $transfer->stockTransferId,
    'nameSourceStock' => $transfer->nameSourceStock,
    'nameTargetStock' => $transfer->nameTargetStock,
    'note' => $transfer->note,
    'issuedAt' => date\DateFormatter::formatToDateFormatAppUserOrEmptyString($transfer->issuedAt),
    'priceCalculation' => core\HpNumberFormatter::toMoney($transfer->commoditiesListData['priceCalculation']),
    'totalAmount' => core\HpNumberFormatter::toAmount($transfer->commoditiesListData['totalAmount']),
    'createdBy' => $creator->login . ' (' . $creator->localizedUserType . ')'
));

foreach ($transfer->commoditiesListData['commoditiesListData'] as $commodity) {
    $t->set_var(array(
        'name' => $commodity['name'],
        'amount' => core\HpNumberFormatter::toAmount($commodity['amount']),
        'code' => $commodity['code'],
        'pricePerUnit' => core\HpNumberFormatter::toMoney($commodity['pricePerUnit'])
    ));
    $t->parse("tmtcommoditiesList", "commodity", true);
}

$t->set_global_var_to_template();
$stockTransferInHTML = $t->finish($t->parse("out", "tmt"));

$pdf = new party\ReceiptAndIssue2PDF(PDF_PAGE_ORIENTATION, PDF_UNIT, PDF_PAGE_FORMAT, true, 'UTF-8', false);
$pdf->setLanguageArray($l);
$pdf->setHeaderData($_foodBank->logoName);
$pdf->setPrintHeader(true);
$pdf->setDefaultPDFLayout();

$html .= '<html><head></head><body><style>' . file_get_contents(__DIR__ . '/style/print2PDF.css') . '</style>';
$html .= $stockTransferInHTML;
$pdf->writeHTML($html, true, false, true, false);

$stockTransferNumber = str_pad($transfer->stockTransferId, numberLengthInFilename, "0", STR_PAD_LEFT);
$pdf->Output('prevodka_' . $stockTransferNumber . '.pdf', "I");
exit;
