/*!
 * Bootstrap-select v1.13.14 (https://developer.snapappointments.com/bootstrap-select)
 *
 * Copyright 2012-2020 SnapAppointments, LLC
 * Licensed under MIT (https://github.com/snapappointments/bootstrap-select/blob/master/LICENSE)
 */

!function(e,t){void 0===e&&void 0!==window&&(e=window),"function"==typeof define&&define.amd?define(["jquery"],function(e){return t(e)}):"object"==typeof module&&module.exports?module.exports=t(require("jquery")):t(e.jQuery)}(this,function(e){e.fn.selectpicker.defaults={noneSelectedText:"\u0686\u06cc\u0632\u06cc \u0627\u0646\u062a\u062e\u0627\u0628 \u0646\u0634\u062f\u0647 \u0627\u0633\u062a",noneResultsText:"\u0647\u06cc\u062c \u0645\u0634\u0627\u0628\u0647\u06cc \u0628\u0631\u0627\u06cc {0} \u067e\u06cc\u062f\u0627 \u0646\u0634\u062f",countSelectedText:"{0} \u0627\u0632 {1} \u0645\u0648\u0631\u062f \u0627\u0646\u062a\u062e\u0627\u0628 \u0634\u062f\u0647",maxOptionsText:["\u0628\u06cc\u0634\u062a\u0631 \u0645\u0645\u06a9\u0646 \u0646\u06cc\u0633\u062a {\u062d\u062f\u0627\u06a9\u062b\u0631 {n} \u0639\u062f\u062f}","\u0628\u06cc\u0634\u062a\u0631 \u0645\u0645\u06a9\u0646 \u0646\u06cc\u0633\u062a {\u062d\u062f\u0627\u06a9\u062b\u0631 {n} \u0639\u062f\u062f}"],selectAllText:"\u0627\u0646\u062a\u062e\u0627\u0628 \u0647\u0645\u0647",deselectAllText:"\u0627\u0646\u062a\u062e\u0627\u0628 \u0647\u06cc\u0686 \u06a9\u062f\u0627\u0645",multipleSeparator:", "}});