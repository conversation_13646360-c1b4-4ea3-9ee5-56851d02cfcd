<?php
header("Content-Type: text/html; charset=utf-8");
require_once '../conf/commonApp.php';
doInit();
core\AuthenticationManager::verifyTheAuthenticity($_authenticationCode, $_loggedAppUserId);
$authorizationManager = new core\AuthorizationManager($_loggedAppUser);
if (!$authorizationManager->hasRightToPage(core\RightName::receiptToStockList)) { core\HttpUtils::redirectAndExit(); }
$fullAccessToReceiptToStockList = $authorizationManager->hasFullAccessToPage(core\RightName::receiptToStockList);

if ($fullAccessToReceiptToStockList) {
    $filledFormManager = form\FilledForm::createForFoodBank($_loggedAppUser->foodBankId);
    $filledFormManager->deleteFilledForm($_GET['filledFormId']);
    core\HttpUtils::sendSuccessfulEmptyAjaxResponseAndExit();
}
