<?php
header("Content-Type: text/html; charset=utf-8");
require_once '../conf/commonApp.php';

doInit();
core\AuthenticationManager::verifyTheAuthenticity($_authenticationCode, $_loggedAppUserId);
$authorizationManager = new core\AuthorizationManager($_loggedAppUser);

if (!$authorizationManager->hasRightToPage(core\RightName::settingFoodBank)) {
    core\HttpUtils::redirectAndExit();
}

$rights = $authorizationManager->getRights();
$fullAccessToSettingFoodBank = $authorizationManager->hasFullAccessToPage(core\RightName::settingFoodBank);
$foodBankManager = party\FoodBankManager::createForFoodBank($_loggedAppUser->foodBankId);

if (isset($_POST['removeStampAndSignature']) and $fullAccessToSettingFoodBank) {
    $foodBankData = $foodBankManager->getFoodBankDataToSettingsByFoodBankId();
    $fileName = $foodBankData->stampAndSignatureName;
    $fileExists = false;
    if (!is_null($fileName) && !empty($fileName)) {
        $fileExists = (file_exists(STAMP_AND_SIGNATURE_PATH . '/' . $fileName));
    }
    if ($fileExists) {
        @unlink(STAMP_AND_SIGNATURE_PATH . '/' . $fileName);
        $foodBankManager->setStampAndSignatureName(null);
    }
}

if (isset($_POST['saveSettings']) and $fullAccessToSettingFoodBank) {
    $foodBankData = json_decode($_POST['foodBank']);
    $foodBankManager->saveFoodBank($foodBankData);
}

$foodBankData = $foodBankManager->getFoodBankDataToSettingsByFoodBankId();
$stampAndSignatureExists['stampAndSignatureExists'] = false;
$fileName = $foodBankData->stampAndSignatureName;

if (!is_null($fileName) && !empty($fileName)) {
    $stampAndSignatureExists['stampAndSignatureExists'] = (file_exists(STAMP_AND_SIGNATURE_PATH . '/' . $fileName));
}

$t = new ui\Template("./lang/cs");
$t->set_file("tmt", "settingFoodBank.tmt");
$t->set_var('foodBankData', htmlspecialchars(json_encode($foodBankData)));
$t->set_var('stampAndSignatureExistsData', htmlspecialchars(json_encode($stampAndSignatureExists)));
$t->set_global_var_to_template();
$t->pparse("out", "tmt");
