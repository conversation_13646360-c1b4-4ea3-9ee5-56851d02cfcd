angular.module('PBanka', ['hpBankaMenu'])

    .controller('SupportPersonListController', function ($scope, $http) {
        $scope.regNumber = /^(0|[1-9][0-9]*)((\.[0-9]*)|(\,[0-9]*))?$/;

        $scope.initializeYearList = function (selectedYear) {
            for (var i = 0; i < $scope.yearList.length; i++) {
                if ($scope.yearList[i].id == selectedYear) {
                    $scope.supportPersonList.selectedYear = $scope.yearList[i];
                }
            }
        };

        $scope.inicializeMonthList = function (selectedMonth) {
            for (var i = 0; i < $scope.monthList.length; i++) {
                if ($scope.monthList[i].id == selectedMonth) {
                    $scope.supportPersonList.selectedMonth = $scope.monthList[i];
                }
            }
        };

        $scope.setYearAndMonth = function () {
            $scope.canShowSave();
            var year = $scope.supportPersonList.selectedYear.id;
            var month = $scope.supportPersonList.selectedMonth.id;

            $http.get('getSupportedPersonListByPeriodAndCustomerId.php?year=' + encodeURIComponent(year) + '&month=' + encodeURIComponent(month))
                .success(function (result) {
                    $scope.supportPersonList.supportedPersons = result.supportedPersons;
                })
                .error(function (data) {
                });
        };

        $scope.canShowSave = function () {
            if (!$scope.supportPersonList.isUser) {
                return true;
            }

            var year = $scope.supportPersonList.selectedYear.id;
            var month = $scope.supportPersonList.selectedMonth.id;
            var dateInSupportedPersonList = new Date(year + '-' + month + '-01 00:00:00');
            var lockoutDateForSupportedPersons = new Date($scope.supportPersonList.lockoutDateForSupportedPersons);
            var today = new Date($scope.supportPersonList.today);

            if ((dateInSupportedPersonList < lockoutDateForSupportedPersons) || (dateInSupportedPersonList > today)) {
                return false;
            } else {
                return true;
            }
        };

        $scope.canSave = function () {
            return ($scope.supportedPersonsForm.$valid);
        };
    });
