<?php
require_once __DIR__ . '/../lib/thirdparty/vendor/autoload.php';
require_once 'appConfig.php';

spl_autoload_register(function ($className) {
    $classFile = convertClassNameWithNameSpaceToFullFilePath($className);
    loadClassIfClassFileExists($classFile);
});

function convertClassNameWithNameSpaceToFullFilePath($className) {
    return dirname(__FILE__) . '/..' . DIRECTORY_SEPARATOR . 'lib' . DIRECTORY_SEPARATOR . 'hpbanka' . DIRECTORY_SEPARATOR . str_replace('\\', '/', $className) . '.class.php';
}

function loadClassIfClassFileExists($classFile) {
    if (is_file($classFile)) {
        require_once $classFile;
    }
}

define('COPY', 'Copyright © 2025');
define('HPBANKAVERSION', "1.48.00");
define('PRINT_NAME', 'Vytvořeno v aplikaci <span style="font-weight: bold;">hpbanka v. ' . HPBANKAVERSION . '</span>');
define('DEVEL', false);

// roky pro options
define('START_YEAR', "2018");
define('END_YEAR', "2025");

// TCPDF library
define('K_TCPDF_EXTERNAL_CONFIG', true);
define('K_TCPDF_CALLS_IN_HTML', true); // If true allows to call TCPDF methods using HTML syntax
define('PDF_PAGE_FORMAT', 'A4'); // Page format.
define('PDF_PAGE_ORIENTATION', 'P'); // Page orientation (P=portrait, L=landscape).
define('PDF_CREATOR', 'hpbanka'); // Document creator.
define('PDF_AUTHOR', 'hpbanka'); // Document author.
define('PDF_UNIT', 'mm'); // Document unit of measure [pt=point, mm=millimeter, cm=centimeter, in=inch].

\dibi::connect([
    'driver' => 'postgre',
    'host' => PBANKA_HOST,
    'database' => PBANKA_DATABASE_NAME,
    'username' => PBANKA_USER,
    'password' => PBANKA_PASSWORD
]);

// Nastavení správného schématu (globálně pro session)
\dibi::query('SET search_path TO ' . PBANKA_SCHEMA_NAME);

function doInit() {
    global $_loggedAppUserId, $_loggedAppUser, $_authenticationCode, $_foodBank, $_basicInformationList;

    session_start();
    $_basicInformationList = array();
    // inicializace appUser pokud existuje
    if (isset($_SESSION['loggedAppUserId']) and $_SESSION['loggedAppUserId'] > 0) {
        $_loggedAppUserId = $_SESSION['loggedAppUserId'];
        $_loggedAppUser = core\AppUserManager::createAppUser($_loggedAppUserId);
        $foodBankManager = party\FoodBankManager::createForFoodBank($_loggedAppUser->foodBankId);
        $_foodBank = $foodBankManager->getFoodBankByFoodBankId();
        setBasicInformationInPage();
    } else {
        $_SESSION['loggedAppUserId'] = 0;
    }

    if (!isset($_SESSION['authenticationCode'])) {
        $_authenticationCode = "";
    } else {
        $_authenticationCode = $_SESSION['authenticationCode'];
    }
}

function setBasicInformationInPage() {
    global $_loggedAppUser, $_basicInformationList;

    $alertManager = new util\AlertManager($_loggedAppUser);
    $_basicInformationList = $alertManager->getDataForAlertDanger();
}

set_exception_handler('exceptionHandler');

function exceptionHandler($exception) {

    if ($exception instanceof exception\HpAuthenticationException) {
        header("Location: index.php?authenticationError");
        exit;
    }

    echo $exception->getMessage();
    echo $exception->getTraceAsString();
    exit;
}

function getCurrency() {
    return CURRENCY === 'EUR' ? '&euro;' : 'Kč';
}
